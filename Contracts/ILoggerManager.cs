namespace Contracts;

public interface ILoggerManager
{
    void LogVerbose(string message);
    void LogVerbose(Exception ex, string message);

    void LogVerbose(string message, params object[] parameters);
    void LogVerbose(Exception ex, string message, params object[] parameters);
    void LogDebug(string message);
    void LogDebug(Exception ex, string message);
    void LogDebug(string message, params object[] parameters);
    void LogDebug(Exception ex, string message, params object[] parameters);
    void LogInfo(string message);
    void LogInfo(Exception ex, string message);
    void LogInfo(string message, params object[] parameters);
    void LogInfo(Exception ex, string message, params object[] parameters);
    void LogWarn(string message);
    void LogWarn(Exception ex, string message);
    void LogWarn(string message, params object[] parameters);
    void LogWarn(Exception ex, string message, params object[] parameters);
    void LogError(string message);
    void LogError(Exception ex, string message);
    void LogError(string message, params object[] parameters);
    void LogError(Exception ex, string message, params object[] parameters);
    void LogFatal(string message);
    void LogFatal(Exception ex, string message);
    void LogFatal(string message, params object[] parameters);
    void LogFatal(Exception ex, string message, params object[] parameters);
}