using Contracts.IModels;

namespace Contracts;

public interface IRepositoryManager
{
    IAradressdatenRepository Aradressdaten { get; }
    IArhauptdateiRepository Arhauptdatei { get; }
    IKundenRepository Kunden { get; }
    IAuftragskopfRepository Auftragskopf { get; set; }
    IArtikelRepository Artikel { get; }
    IKontraktRepository Kontrakt { get; }
    IAuftragsposRepository Auftragspos { get; }
    ITriggerLogRepository TriggerLog { get; }
    void Save();
}