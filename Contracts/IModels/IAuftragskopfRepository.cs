using Entities.Models;

namespace Contracts.IModels;

public interface IAuftragskopfRepository
{
    void WriteOrderToDb(bool trackChanges, Auftragskopf order);

    IEnumerable<Auftragskopf>? GetAuftragskopfById(int orderId, bool trackChanges);
    
    List<long?> GetWatchedLieferscheine(bool trackChanges);
    
    void DisableWatcher(List<long?> watchList);
    
    string GetOrderIdByAuftragsnummer(long auftragsnummer, bool trackChanges);
    
    long? GetMaxAuftragsnummer();
}