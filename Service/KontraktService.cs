using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;

namespace Service;

public class KontraktService : IKontraktService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;
    
    public KontraktService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }
    
    
    public IEnumerable<Kontrakt> GetKontraktById(int kontraktId, bool trackChanges)
    {
        _logger.LogInfo($"KontraktService: Getting contract with ID {kontraktId}");
        var kontrakt = _repository.Kontrakt.GetKontraktById(kontraktId, false);
        
        if (kontrakt == null) 
        {
            _logger.LogWarn($"KontraktService: Contract with ID {kontraktId} not found (null)");
            throw new KontraktNotFoundException(kontraktId);
        }
        
        if (!kontrakt.Any())
        {
            _logger.LogWarn($"KontraktService: Contract with ID {kontraktId} not found (empty collection)");
            throw new KontraktNotFoundException(kontraktId);
        }
        
        _logger.LogInfo($"KontraktService: Found contract with ID {kontraktId}");
        return kontrakt;
    }

    public IEnumerable<Kontrakt> GetKontrakteByKundeId(int kundeId, bool trackChanges)
    {
        var kontrakte = _repository.Kontrakt.GetKontrakteByKundeId(kundeId, false);
        if (kontrakte == null) throw new KontraktNotFoundException();
        return kontrakte;
    }
    
    public void ChangeKontraktAmount(Kontrakt kontrakt, float amount)
    {
        _repository.Kontrakt.ChangeKontraktAmount(kontrakt, amount);
    }
}