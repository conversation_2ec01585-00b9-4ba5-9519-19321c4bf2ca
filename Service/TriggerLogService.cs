using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;
using Exception = System.Exception;

namespace Service;

public class TriggerLogService : ITriggerLogService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;

    public TriggerLogService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }

    public TriggerLog GetOldestTrigger(bool trackChanges)
    {
        var trigger = _repository.TriggerLog.GetOldestTrigger(trackChanges);
        if (trigger == null) throw new TriggerLogNotFoundException();
        return trigger;
    }

    public IEnumerable<TriggerLog?> GetOpenTriggers(bool trackChanges)
    {
        var triggers = _repository.TriggerLog.GetOpenTriggers(trackChanges);
        if (triggers == null) throw new TriggerLogNotFoundException();
        return triggers;
    }

    public TriggerLog GetNewestTrigger(bool trackChanges)
    {
        var trigger = _repository.TriggerLog.GetNewestTrigger(trackChanges);
        if (trigger == null) throw new TriggerLogNotFoundException();
        return trigger;
    }
    
    public void SetAsDone(TriggerLog triggerLog, bool saveChanges = true)
    {
        _repository.TriggerLog.SetAsDone(triggerLog);
    }
}