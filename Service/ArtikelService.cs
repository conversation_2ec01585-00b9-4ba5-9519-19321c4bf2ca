using AutoMapper;
using Contracts;
using Entities.Models;
using Entities.Exceptions;
using Service.Contracts;

namespace Service;

public class ArtikelService : IArtikelService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;
    
    public ArtikelService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }
    
    public IEnumerable<Artikel>? GetArtikelById(long artikelnummer, bool trackBack)
    {
        var artikel = _repository.Artikel.GetArtikelById(artikelnummer, false);
        if (artikel == null) throw new ArtikelNotFoundExcepion();
        return artikel;
    }

    public IEnumerable<Artikel?> GetAllArtikel(bool trackBack)
    {
        var artikel = _repository.Artikel.GetAllArtikel(false);
        if (artikel == null) throw new ArtikelNotFoundExcepion();
        return artikel;
    }
}