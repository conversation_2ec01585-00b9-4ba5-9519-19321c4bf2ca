using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;
using Exception = System.Exception;

namespace Service;

public class AuftragskopfService : IAuftragskopfService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;

    public AuftragskopfService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }

    public void WriteOrderToDb(bool trackChanges, Auftragskopf order)
    {
        _repository.Auftragskopf.WriteOrderToDb(trackChanges, order);
    }

    public IEnumerable<Auftragskopf>? GetAuftragskopfById(int orderId, bool trackChanges)
    {
        var auftragskopf = _repository.Auftragskopf.GetAuftragskopfById(orderId, false);
        if (auftragskopf == null) throw new AuftragskopfNotFoundException();
        return auftragskopf;
    }

    public List<long?> GetWatchedLieferscheine(bool trackChanges)
    {
        return _repository.Auftragskopf.GetWatchedLieferscheine(false);
    }

    public void DisableWatcher(List<long?> watchList)
    {
        _repository.Auftragskopf.DisableWatcher(watchList);
    }
    

    public string GetOrderIdByAuftragsnummer(long auftragsnummer, bool trackChanges)
    {
        return _repository.Auftragskopf.GetOrderIdByAuftragsnummer(auftragsnummer, false);
    }


    public long? GetMaxAuftragsnummer()
    {
        return _repository.Auftragskopf.GetMaxAuftragsnummer();
    }
}