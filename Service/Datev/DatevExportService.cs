using Contracts;
using Entities.Common;
using Entities.Exceptions;
using Service.Contracts;

namespace Service;

internal sealed class DatevExportService(IAradressdatenService aradressdatenService,
                                         IArhaupdateiService arhaupdateiService,
                                         ILoggerManager logger)
    : IDatevExportService
{
    private IAradressdatenService AradressdatenService { get; } = aradressdatenService;
    private IArhaupdateiService ArhaupdateiService { get; } = arhaupdateiService;
    
    public string ExportInvoicesAsCsv(bool trackChanges, DateTime fromDate, DateTime toDate)
    {
        var invoiceOverviews = AradressdatenService.GetAllAradressdatenOverview(trackChanges, fromDate,toDate);

        foreach (var invoiceOverview in invoiceOverviews)
        {
              var test2 = ArhaupdateiService.GetAllPostingsFromInvoice(trackChanges, invoiceOverview.InvoiceNumber, PostingsSchema.Po);
              
        }
        
        return string.Empty;
    }
}