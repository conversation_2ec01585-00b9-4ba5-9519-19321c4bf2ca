using AutoMapper;
using Contracts;
using Quartz;
using Service.Contracts;

namespace Service;

public class ServiceManager : IServiceManager
{
    private Lazy<IAradressdatenService> _aradresdatenService;
    private Lazy<IArhaupdateiService> _arhauptdateiService;
    private Lazy<IKundenService> _kundenService;
    private Lazy<IDatevExportService> _datevExportService;
    private Lazy<IAuftragskopfService> _auftragskopfService;
    private Lazy<IArtikelService> _artikelService;
    private Lazy<IKontraktService> _kontraktService;
    private Lazy<IAuftragsposService> _auftragsposService;
    private Lazy<ITriggerLogService> _triggerLogService;

    public IAradressdatenService AradressdatenService => _aradresdatenService.Value;
    public IArhaupdateiService ArhauptdateiService => _arhauptdateiService.Value;
    public IKundenService KundenService => _kundenService.Value;
    public IDatevExportService DatevExportService => _datevExportService.Value;
    public IAuftragskopfService AuftragskopfService => _auftragskopfService.Value;
    public IArtikelService ArtikelService => _artikelService.Value;
    public IKontraktService KontraktService => _kontraktService.Value;
    public IAuftragsposService AuftragsposService => _auftragsposService.Value;
    public ITriggerLogService TriggerLogService => _triggerLogService.Value;

    public ServiceManager(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _aradresdatenService = new Lazy<IAradressdatenService>(() => new AradressdatenService(repositoryManager, logger, mapper));
        _arhauptdateiService = new Lazy<IArhaupdateiService>(() => new ArhauptdateiService(repositoryManager, logger, mapper));
        _kundenService=new Lazy<IKundenService>(()=>new KundenService(repositoryManager, logger, mapper));
        _datevExportService=new Lazy<IDatevExportService>(()=>new DatevExportService(AradressdatenService,ArhauptdateiService, logger));
        _auftragskopfService=new Lazy<IAuftragskopfService>(()=>new AuftragskopfService(repositoryManager, logger, mapper));
        _artikelService=new Lazy<IArtikelService>(()=>new ArtikelService(repositoryManager, logger, mapper));
        _kontraktService=new Lazy<IKontraktService>(()=>new KontraktService(repositoryManager, logger, mapper));
        _auftragsposService=new Lazy<IAuftragsposService>(()=>new AuftragsposService(repositoryManager, logger, mapper));
        _triggerLogService=new Lazy<ITriggerLogService>(()=>new TriggerLogService(repositoryManager, logger, mapper));
    }
}