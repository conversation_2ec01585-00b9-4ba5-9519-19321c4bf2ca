using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;
using Exception = System.Exception;

namespace Service;

public class KundenService : IKundenService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;

    public KundenService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }

    public IEnumerable<Kunden> GetAllKunden(bool trackBack)
    {
        var customers = _repository.Kunden.GetAllKunden(false);
        if (customers.Count() == 0 ) throw new KundenNotFoundException();
        return customers;
    }
    
    public IEnumerable<Kunden> GetKundeById(long kundennummer, bool trackBack)
    {
        var customer = _repository.Kunden.GetKundeById(kundennummer, false);
        if (customer == null) throw new KundenNotFoundException();
        return customer;
    }
    
    public void WriteKundeToDb(bool trackChanges, Kunden kunde)
    {
        _repository.Kunden.WriteOrderToDb(trackChanges, kunde);
    }
}