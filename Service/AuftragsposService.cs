using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;
using Exception = System.Exception;

namespace Service;

public class AuftragsposService : IAuftragsposService
{
    private readonly IRepositoryManager _repository;
    private readonly ILoggerManager _logger;
    private readonly IMapper _mapper;

    public AuftragsposService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    {
        _repository = repositoryManager;
        _logger = logger;
        _mapper = mapper;
    }

    public void WriteOrderItemToDb(bool trackChanges, Auftragspo item)
    {
        _repository.Auftragspos.WriteOrderItemToDb(trackChanges, item);
    }

    public IEnumerable<Auftragspo>? GetAuftragsposById(int orderId, bool trackChanges)
    {
        var auftragspos = _repository.Auftragspos.GetAuftragsposById(orderId, false);
        if (auftragspos == null) throw new AuftragsposNotFoundException();
        return auftragspos;
    }
}