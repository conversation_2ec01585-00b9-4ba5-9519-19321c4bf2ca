using AutoMapper;
using Contracts;
using Entities.Common;
using Entities.Exceptions;
using Service.Contracts;
using Shared.DataTransferObjects;

namespace Service;

internal sealed class ArhauptdateiService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    : IArhaupdateiService
{
    public IEnumerable<InvoicePostingDto> GetAllPostingsFromInvoice(bool trackChanges, long invoiceNumber, PostingsSchema? schema = null)
    {
            var arhauptdatei = repositoryManager.Arhauptdatei.GetAllArhauptdateiFromInvoice(trackChanges, invoiceNumber,schema);

            if (arhauptdatei is null || !arhauptdatei.Any())
                throw new ArhauptdateiNotFoundException();

            return arhauptdatei.Select(x => (InvoicePostingDto)x);
    }
}