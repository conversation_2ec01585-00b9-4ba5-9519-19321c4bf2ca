using AutoMapper;
using Contracts;
using Entities.Exceptions;
using Entities.Models;
using Service.Contracts;
using Shared.DataTransferObjects;

namespace Service;

internal sealed class AradressdatenService(IRepositoryManager repositoryManager, ILoggerManager logger, IMapper mapper)
    : IAradressdatenService
{
    public IEnumerable<Aradressdaten> GetAllAradressdaten(bool trackChanges)
    {
        var aradressdaten = repositoryManager.Aradressdaten.GetAllAradressdaten(trackChanges);

        if (aradressdaten is null || !aradressdaten.Any())
            throw new AradressdatenNotFoundException();

        return aradressdaten;
    }

    public IEnumerable<OutgoingInvoiceOverviewEntryDto> GetAllAradressdatenOverview(bool trackChanges, DateTime? fromDate, DateTime? toDate)
    {
        IEnumerable<Aradressdaten>? aradressdaten = null;
        
        if(fromDate.HasValue && toDate.HasValue)
            aradressdaten = repositoryManager.Aradressdaten.GetAllAradressdaten(trackChanges, fromDate.Value, toDate.Value);
        else
            aradressdaten = GetAllAradressdaten(trackChanges);

        if (aradressdaten is null || !aradressdaten.Any())
            throw new AradressdatenNotFoundException();

        return aradressdaten.Select(a => (OutgoingInvoiceOverviewEntryDto)a);
    }
}