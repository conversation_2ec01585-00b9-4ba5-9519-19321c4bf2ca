namespace Entities.Models;

public partial class Erhauptdatei
{
    public long Renummer { get; set; }

    public string? Status { get; set; }

    public string? ErfSchema { get; set; }

    public DateTime? Redatum { get; set; }

    public short? GridPosition { get; set; }

    public long? ArtNr { get; set; }

    public string? Bezeichnung { get; set; }

    public double? Anzahl { get; set; }

    public string? BzgGr { get; set; }

    public decimal? GesStck { get; set; }

    public decimal? Gesamtmenge { get; set; }

    public decimal? Einzelpreis { get; set; }

    public decimal? Gesamtnetto { get; set; }

    public decimal? MwstProz { get; set; }

    public long? Lsnr { get; set; }

    public string? Konto { get; set; }

    public long? KontraktNr { get; set; }

    public long? Hartikel { get; set; }

    public long? VertrNr { get; set; }

    public long? VpckNr { get; set; }

    public short? MwstSchl { get; set; }

    public long? StreckenNr { get; set; }

    public long? KdNrWe { get; set; }

    public short? AnzLspos { get; set; }

    public short? VertrProv { get; set; }

    public string? Vdmproz { get; set; }

    public short? Faktor { get; set; }

    public string? KnEzZuschlag { get; set; }

    public string? KontraktKng { get; set; }

    public decimal? Fracht { get; set; }

    public string? Filiale { get; set; }

    public short? Bld { get; set; }

    public short? ArtGruppe { get; set; }

    public decimal? Vxz { get; set; }

    public decimal? Vez { get; set; }

    public decimal? Gr { get; set; }

    public string? Zustellart { get; set; }

    public double? AlteElsmenge { get; set; }

    public decimal? KalkuKo { get; set; }

    public string? Kgrund { get; set; }

    public short? ErkaKu { get; set; }

    public bool? Lkw { get; set; }

    public long? Kostentraeger { get; set; }

    public long? Kostenstelle { get; set; }

    public short? LagerNr { get; set; }

    public long? KolliInh { get; set; }

    public decimal? PakGewicht { get; set; }

    public long Id { get; set; }

    public DateTime? MaabrDatum { get; set; }
}
