namespace Entities.Models;

public partial class ArtLager
{
    public long ArtNr { get; set; }

    public decimal? ArtGdp { get; set; }

    public double? ArtMla { get; set; }

    public double? ArtDla { get; set; }

    public decimal? ArtLgw { get; set; }

    public double? ArtLgm { get; set; }

    public decimal? ArtNetto { get; set; }

    public decimal? ArtSelP { get; set; }

    public int? ArtLgs { get; set; }

    public string? ArtOpt1 { get; set; }

    public DateTime? ArtDatum { get; set; }

    public double? ArtAkabaltKg { get; set; }

    public double? ArtAkabaltWert { get; set; }

    public double? ArtAkabaltDp { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public decimal? ArtStckLa { get; set; }

    public decimal? ArtAkabaltStck { get; set; }

    public decimal? ArtLgstck { get; set; }
}
