namespace Entities.Models;

public partial class Blspo
{
    public long? Auftragsnummer { get; set; }

    public string? Status { get; set; }

    public int? GridPos { get; set; }

    public string? Erfassungsschema { get; set; }

    public long? ArtikelNummer { get; set; }

    public string? ArtBestellNr { get; set; }

    public string? Bezeichnung { get; set; }

    public string? Text { get; set; }

    public double? Anzahl { get; set; }

    public string? BezGr { get; set; }

    public decimal? GesStck { get; set; }

    public decimal? GesMenge { get; set; }

    public decimal? Epreis { get; set; }

    public decimal? Gpreis { get; set; }

    public decimal? Mwst { get; set; }

    public string? Faktor { get; set; }

    public decimal? Mwst2 { get; set; }

    public long? VerpckNr { get; set; }

    public long? ArtikelNummer2 { get; set; }

    public string? Selektionskennung { get; set; }

    public decimal? AlteMenge { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public short? EkaKu { get; set; }

    public DateTime? Mhd { get; set; }

    public string? ChargenNr { get; set; }

    public short? LagerNr { get; set; }

    public decimal? MinBmenge { get; set; }

    public decimal? PakGewicht { get; set; }

    public long? KolliInh { get; set; }

    public long Id { get; set; }
}
