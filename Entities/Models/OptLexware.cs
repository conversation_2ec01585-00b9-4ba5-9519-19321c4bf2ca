namespace Entities.Models;

public partial class OptLexware
{
    public string? Fibu2Pfad { get; set; }

    public bool Fibu2Art { get; set; }

    public bool Fibu2Ar { get; set; }

    public bool Fibu2Gb { get; set; }

    public bool Fibu2Aab { get; set; }

    public bool Fibu2Er { get; set; }

    public bool Fibu2Sta { get; set; }

    public bool FibuKostentraeger { get; set; }

    public bool Fibu2Anz { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
