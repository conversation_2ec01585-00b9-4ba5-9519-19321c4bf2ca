namespace Entities.Models;

public partial class TabIndex
{
    public long? AktArnr { get; set; }

    public long? AktAlsnr { get; set; }

    public long AktErnr { get; set; }

    public long AktElsnr { get; set; }

    public long AktWgsnr { get; set; }

    public long AktAwgsnr { get; set; }

    public long AktKtEkNr { get; set; }

    public long AktKtVkNr { get; set; }

    public long AktKdnr { get; set; }

    public long AktLfnr { get; set; }

    public long AktArtNr { get; set; }

    public long AktGbvnr { get; set; }

    public long AktAgbvnr { get; set; }

    public long AktArgbvnr { get; set; }

    public long AktEstrNr { get; set; }

    public long AktMstrNr { get; set; }

    public long AktBstrNr { get; set; }

    public long AktBestNr { get; set; }

    public long AktKassIndNr { get; set; }

    public long AktVtnr { get; set; }

    public long AktMiBuNr { get; set; }

    public long AktAnzNr { get; set; }

    public string? Version { get; set; }

    public long Id { get; set; }

    public long? AktUmbIdx { get; set; }

    public bool? Gdanbindung { get; set; }

    public bool? GdartUeb { get; set; }

    public long? AktPktrNr { get; set; }

    public long? AktRgbvnr { get; set; }

    public long? AktLabNr { get; set; }

    public string? Gddatenpfad { get; set; }

    public long? GdkdNr { get; set; }

    public long? Gdlfnr { get; set; }

    public string? Gddb { get; set; }

    public string? Gduser { get; set; }

    public string? Gdpasswort { get; set; }

    public long? AktDepNr { get; set; }

    public string? ElodatenpfadLs { get; set; }

    public string? ElodatenpfadAr { get; set; }

    public int? Eloformat { get; set; }

    public bool? Eloanbindung { get; set; }

    public string? Elodatenpfad { get; set; }

    public bool? ChargLfdNr { get; set; }
}
