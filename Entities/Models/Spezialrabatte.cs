namespace Entities.Models;

public partial class Spezialrabatte
{
    public long KundenNr { get; set; }

    public long? Artikel { get; set; }

    public long? ArtNrVon { get; set; }

    public long? ArtNrBis { get; set; }

    public int? ArtGruppe { get; set; }

    public string? PlusMinus { get; set; }

    public decimal? Rabatt { get; set; }

    public string? ProzentEuro { get; set; }

    public DateTime? ZeitVon { get; set; }

    public DateTime? ZeitBis { get; set; }

    public string? Rabattbezeichnung { get; set; }

    public bool DirektBerechnen { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool? Festbetrag { get; set; }
}
