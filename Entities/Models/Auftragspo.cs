namespace Entities.Models;

public partial class Auftragspo
{
    public long Auftragsnummer { get; set; }

    public string? Status { get; set; }

    public int? GridPos { get; set; }

    public string? Erfassungsschema { get; set; }

    public long? ArtikelNummer { get; set; }

    public string? Bezeichnung { get; set; }

    public double? <PERSON><PERSON><PERSON> { get; set; }

    public string? BezGr { get; set; }

    public decimal? GesStck { get; set; }

    public decimal? GesMenge { get; set; }

    public decimal? Epreis { get; set; }

    public decimal? Gpreis { get; set; }

    public decimal? Mwst { get; set; }

    public string? Faktor { get; set; }

    public string? ChargenNr { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public string? Mhd { get; set; }

    public long? Wgsnr { get; set; }

    public long? Fbzgnr { get; set; }

    public decimal? Mwst2 { get; set; }

    public long? VerpckNr { get; set; }

    public long? ArtikelNummer2 { get; set; }

    public string? Selektionskennung { get; set; }

    public decimal? AlteMenge { get; set; }

    public int? LsdepotNr { get; set; }

    public string? Fbneu { get; set; }

    public decimal? Pfracht { get; set; }

    public string? Baeckerzelle { get; set; }

    public string? Lkwkammer { get; set; }

    public string? Muehlenzelle { get; set; }

    public string? Rabatt { get; set; }

    public decimal? GpreisRab { get; set; }

    public string? NuebTexte { get; set; }

    public bool? KdtxTcheck { get; set; }

    public int? AvisAnzahl { get; set; }

    public int? FilialNr { get; set; }

    public short? LagerNr { get; set; }

    public bool? Nvobilanziert { get; set; }

    public string? NvobilanzNr { get; set; }

    public string? Nvonr { get; set; }

    public bool? Nvo { get; set; }

    public decimal? ArtEinhVp { get; set; }

    public decimal? PakGewicht { get; set; }

    public decimal? PalGew { get; set; }

    public long? KolliInh { get; set; }

    public string? PalArt { get; set; }

    public long? AltNr { get; set; }

    public long Id { get; set; }

    public bool? LoeschKng { get; set; }

    public string? Eannr { get; set; }

    public string? PosNrBest { get; set; }

    public string? BestNr { get; set; }

    public int? EdiQalifier { get; set; }

    public int? ProduktionsvorgangId { get; set; }

    public bool? ZtxtKng { get; set; }
}
