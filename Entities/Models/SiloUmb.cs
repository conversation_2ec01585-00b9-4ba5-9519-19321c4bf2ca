namespace Entities.Models;

public partial class SiloUmb
{
    public long? BuchNr { get; set; }

    public int? ZielSilo { get; set; }

    public string? SiloBez { get; set; }

    public string? Sorte { get; set; }

    public decimal? Menge { get; set; }

    public string? ChargNr { get; set; }

    public long? Rmnr { get; set; }

    public string? Bediener { get; set; }

    public DateTime? JobDatum { get; set; }

    public int? Silo1 { get; set; }

    public int? Silo2 { get; set; }

    public int? Silo3 { get; set; }

    public int? Silo4 { get; set; }

    public int? Silo5 { get; set; }

    public int? Silo6 { get; set; }

    public int? Silo7 { get; set; }

    public int? Silo8 { get; set; }

    public decimal? Menge1 { get; set; }

    public decimal? Menge2 { get; set; }

    public decimal? Menge3 { get; set; }

    public decimal? Menge4 { get; set; }

    public decimal? Menge5 { get; set; }

    public decimal? Menge6 { get; set; }

    public decimal? Menge7 { get; set; }

    public decimal? Menge8 { get; set; }

    public decimal? Proz1 { get; set; }

    public decimal? Proz2 { get; set; }

    public decimal? Proz3 { get; set; }

    public decimal? Proz4 { get; set; }

    public decimal? Proz5 { get; set; }

    public decimal? Proz6 { get; set; }

    public decimal? Proz7 { get; set; }

    public decimal? Proz8 { get; set; }

    public int? Zsilo1 { get; set; }

    public int? Zsilo2 { get; set; }

    public int? Zsilo3 { get; set; }

    public decimal? Zmenge1 { get; set; }

    public decimal? Zmenge2 { get; set; }

    public decimal? Zmenge3 { get; set; }

    public decimal? Zproz1 { get; set; }

    public decimal? Zproz2 { get; set; }

    public decimal? Zproz3 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? Rezept { get; set; }

    public bool? Reinigung { get; set; }

    public long? Leistung { get; set; }

    public string AlibiNr { get; set; } = null!;

    public DateTime? WiegeZeit { get; set; }

    public DateTime? WiegeDatum { get; set; }

    public decimal? MengeNetto { get; set; }

    public long? ArtikelNr { get; set; }

    public string? ArtikelBez1 { get; set; }
}
