using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Entities.Models;

[Table("VerbPosTbl")]
public partial class VerbPosTbl
{
    public long ERNummer { get; set; }

    public long? ArtNr { get; set; }

    public int? Sp0 { get; set; }

    public int? Sp1 { get; set; }

    public int? Sp2 { get; set; }

    public int? Sp3 { get; set; }

    public int? Sp4 { get; set; }

    public int? Sp5 { get; set; }

    public int? Sp6 { get; set; }

    public int? Sp7 { get; set; }

    public int? Sp8 { get; set; }

    public int? Sp9 { get; set; }

    public int? Sp10 { get; set; }

    public int? Sp11 { get; set; }

    public int? Sp12 { get; set; }

    public int? Sp13 { get; set; }

    public int? Sp14 { get; set; }

    public int? Sp15 { get; set; }

    public int? Sp16 { get; set; }

    public int? Sp17 { get; set; }

    public int? Sp18 { get; set; }

    public int? Sp19 { get; set; }

    public int? Sp20 { get; set; }

    public int? Sp21 { get; set; }

    public int? Sp22 { get; set; }

    public int? Sp23 { get; set; }

    [Key]
    public long ID { get; set; }
}
