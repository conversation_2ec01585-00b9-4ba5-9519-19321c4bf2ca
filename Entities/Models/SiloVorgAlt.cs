namespace Entities.Models;

public partial class SiloVorgAlt
{
    public int? SiloNr { get; set; }

    public int? DepotNr { get; set; }

    public long? VorgNr { get; set; }

    public string? VgArt { get; set; }

    public DateTime? Datum { get; set; }

    public string? <PERSON><PERSON><PERSON> { get; set; }

    public long? ArtikelNr { get; set; }

    public double? Menge { get; set; }

    public int? UmlSilo { get; set; }

    public long? VgscheinNr { get; set; }

    public string? VgchargeNr { get; set; }

    public short? ProfilNr { get; set; }

    public string? ProfilBez { get; set; }

    public string? Bemerkung { get; set; }

    public int? DplfdNr { get; set; }

    public int? FilialNr { get; set; }

    public bool? UmschlAnz { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public long Rmnummer { get; set; }
}
