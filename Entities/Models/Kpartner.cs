namespace Entities.Models;

public partial class Kpartner
{
    public long Kdnr { get; set; }

    public long PosNr { get; set; }

    public string? Name1 { get; set; }

    public string? Name2 { get; set; }

    public string? Name3 { get; set; }

    public string? Strasse { get; set; }

    public string? Plz { get; set; }

    public string? Ort { get; set; }

    public string? Plzpostfach { get; set; }

    public string? Postfach { get; set; }

    public string? Telefon1 { get; set; }

    public string? Telefon2 { get; set; }

    public string? Telefax { get; set; }

    public string? Mobil { get; set; }

    public string? Email { get; set; }

    public string? Internet { get; set; }

    public DateTime? Geburtstag { get; set; }

    public string? Memo { get; set; }

    public DateTime SysTime { get; set; }

    public string SysUser { get; set; } = null!;

    public long Id { get; set; }
}
