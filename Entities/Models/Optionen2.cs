namespace Entities.Models;

public partial class Optionen2
{
    public bool KrabAlgArt { get; set; }

    public bool LsfremdNr { get; set; }

    public bool Arzweizeilig { get; set; }

    public bool Reweswitch { get; set; }

    public int? TourNr { get; set; }

    public short? StatusLsdrucker1 { get; set; }

    public short? StatusLsdrucker2 { get; set; }

    public short? StatusLsdrucker3 { get; set; }

    public short? StatusLsdrucker4 { get; set; }

    public short? StatusLsdrucker5 { get; set; }

    public short? StatusLsdrucker6 { get; set; }

    public bool Zzwere { get; set; }

    public short? Wgssilo { get; set; }

    public byte? IntraStat { get; set; }

    public bool Eltagesdatum { get; set; }

    public bool Ertagesdatum { get; set; }

    public bool Arbemerkung { get; set; }

    public bool Albemerkung { get; set; }

    public bool Elzustellart { get; set; }

    public bool ArspezRabShow { get; set; }

    public string? Fibu2Pfad { get; set; }

    public bool Fibu2Art { get; set; }

    public bool Fibu2Ar { get; set; }

    public bool Fibu2Gb { get; set; }

    public bool Fibu2Aab { get; set; }

    public bool Fibu2Er { get; set; }

    public bool Fibu2Vdatum { get; set; }

    public bool AlssuchStart { get; set; }

    public bool Fibu2Sta { get; set; }

    public bool VdatumOk { get; set; }

    public bool ChargenNr { get; set; }

    public bool Arlfonly { get; set; }

    public bool Erlfonly { get; set; }

    public bool FibuKostentraeger { get; set; }

    public string? ZusTxtBez1 { get; set; }

    public string? ZusTxtBez2 { get; set; }

    public string? ZusTxtBez3 { get; set; }

    public string? ZusTxtBez4 { get; set; }

    public string? ZusTxtBez5 { get; set; }

    public string? ZusTxtBez6 { get; set; }

    public string? OrdersPfad { get; set; }

    public string? OrdersPfadBak { get; set; }

    public bool KtbruttoGew { get; set; }

    public bool Alslkwabfrage { get; set; }

    public int? KostArtNr1 { get; set; }

    public int? KostArtNr2 { get; set; }

    public int? KostArtNr3 { get; set; }

    public int? KostArtNr4 { get; set; }

    public int? KostArtNr5 { get; set; }

    public bool NullPreis { get; set; }

    public bool FibuKstmenge { get; set; }

    public double? LfanzKonto1 { get; set; }

    public double? LfanzKonto2 { get; set; }

    public double? LfanzKontoEg { get; set; }

    public double? LfanzKontoDrittLand { get; set; }

    public bool Fibu2Anz { get; set; }

    public bool Hoegemann { get; set; }

    public string? HoegemannSendPfad { get; set; }

    public string? HoegemannEmpfPfad { get; set; }

    public string? HoegemannSendArchiv { get; set; }

    public string? HoegemannEmpfArchiv { get; set; }

    public decimal? Kundenfrachtsatz { get; set; }

    public bool FrachtAbr { get; set; }

    public bool MitCharge { get; set; }

    public bool MitMhd { get; set; }

    public bool CfC { get; set; }

    public int? CfCartNr { get; set; }

    public bool DatPrf { get; set; }

    public bool Alsnrsort { get; set; }

    public bool AlszustLoeschen { get; set; }

    public short? AlstextNr { get; set; }

    public short? Bltagesdatum { get; set; }

    public int? Blsindex { get; set; }

    public bool BausIndex { get; set; }

    public short? Nvobilanz { get; set; }

    public decimal? Nvowert { get; set; }

    public decimal? NvowertVorLieferant { get; set; }

    public bool LagerNeu { get; set; }

    public string? Version { get; set; }

    public short? GewFeuchtKost { get; set; }

    public bool StckBuchung { get; set; }

    public bool WgsbemAnz { get; set; }

    public bool AbgLsprf { get; set; }

    public bool KtvonAnl { get; set; }

    public bool Mstrecke { get; set; }

    public bool AlsbemTxtSchreiben { get; set; }

    public bool KtbemTxt { get; set; }

    public bool NeuGbv { get; set; }

    public bool KdpreisRe { get; set; }

    public string? EmailAbsender { get; set; }

    public string? EmailSmtp { get; set; }

    public string? EmailLogin { get; set; }

    public string? EmailPassw { get; set; }

    public bool EmailAuth { get; set; }

    public int? EmailPort { get; set; }

    public bool Eloanbindung { get; set; }

    public bool ArbedAbfr { get; set; }

    public bool KolliPruef { get; set; }

    public string? EmailAnhang { get; set; }

    public bool MitKolli { get; set; }

    public bool PruefKal { get; set; }

    public bool KrabAbfr { get; set; }

    public bool HarryEan { get; set; }

    public bool SatLog { get; set; }

    public bool AlbemRe { get; set; }

    public bool Elbemerkung { get; set; }

    public string? LogFile { get; set; }

    public bool Eksuche { get; set; }

    public bool Vksuche { get; set; }

    public string? MapFile { get; set; }

    public string? MappUser { get; set; }

    public string? MappPassw { get; set; }

    public bool ArfreigPruef { get; set; }

    public bool NurDispo { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
