namespace Entities.Models;

public partial class <PERSON><PERSON><PERSON>
{
    public long? AuftragsNr { get; set; }

    public string? Betreuer { get; set; }

    public DateTime? Anlagedatum { get; set; }

    public DateTime? AuftrDatumVon { get; set; }

    public DateTime? AuftrDatumBis { get; set; }

    public bool? DispoKng { get; set; }

    public int? AbrWeg { get; set; }

    public long? PersNrEk { get; set; }

    public string? Name1Ek { get; set; }

    public string? Name2Ek { get; set; }

    public string? StrasseEk { get; set; }

    public string? Plzek { get; set; }

    public string? OrtEk { get; set; }

    public string? TelefonEk { get; set; }

    public string? TelefaxEk { get; set; }

    public string? AsldEk { get; set; }

    public string? LandEk { get; set; }

    public long? BelStNr { get; set; }

    public string? BelStName { get; set; }

    public string? BelStStrasse { get; set; }

    public string? BelStPlz { get; set; }

    public string? BelStOrt { get; set; }

    public string? BelStTelefon { get; set; }

    public string? BelStTelefax { get; set; }

    public string? BelStLand { get; set; }

    public long? AuftrNrEk { get; set; }

    public long? RenrEk { get; set; }

    public string? FremdLsnrEk { get; set; }

    public string? FremdRenrEk { get; set; }

    public long? PersNrVk { get; set; }

    public string? Name1Vk { get; set; }

    public string? Name2Vk { get; set; }

    public string? StrasseVk { get; set; }

    public string? Plzvk { get; set; }

    public string? OrtVk { get; set; }

    public string? TelefonVk { get; set; }

    public string? TelefaxVk { get; set; }

    public string? AsldVk { get; set; }

    public string? LandVk { get; set; }

    public long? AblStNr { get; set; }

    public string? AblStName { get; set; }

    public string? AblStStrasse { get; set; }

    public string? AblStPlz { get; set; }

    public string? AblStOrt { get; set; }

    public string? AblStTelefon { get; set; }

    public string? AblStTelefax { get; set; }

    public string? AblStLand { get; set; }

    public long? AuftrNrVk { get; set; }

    public long? RenrVk { get; set; }

    public string? FremdLsnrVk { get; set; }

    public string? FremdRenrVk { get; set; }

    public long? SpedNr { get; set; }

    public string? SpedName { get; set; }

    public string? SpedName2 { get; set; }

    public string? SpedName3 { get; set; }

    public string? SpedStrasse { get; set; }

    public string? SpedPlz { get; set; }

    public string? SpedOrt { get; set; }

    public string? SpedTelefon { get; set; }

    public string? SpedTelefax { get; set; }

    public string? SpedLand { get; set; }

    public string? StfrSpedNr { get; set; }

    public long? ArtNrFracht { get; set; }

    public decimal? FrachtPreis { get; set; }

    public long? FrachtSchNr { get; set; }

    public bool? Send { get; set; }

    public bool? Lkw { get; set; }

    public string? BemEk { get; set; }

    public string? BemVk { get; set; }

    public string? BemBelSt { get; set; }

    public string? BemAblSt { get; set; }

    public string? BemSped { get; set; }

    public bool? EigLagerEk { get; set; }

    public bool? EigLagerVk { get; set; }

    public bool? EigWare { get; set; }

    public int? FilialNr { get; set; }

    public short? Lagerstelle { get; set; }

    public string? Uhrzeit { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
