namespace Entities.Models;

public partial class TseTransakt
{
    public int KasseId { get; set; }

    public DateTime? KabschlDatum { get; set; }

    public long KabschlNr { get; set; }

    public string? BonId { get; set; }

    public long BonNr { get; set; }

    public long TseId { get; set; }

    public long TseTanr { get; set; }

    public string? TseStart { get; set; }

    public string? TseEnde { get; set; }

    public string? TseVorgArt { get; set; }

    public long TseSigz { get; set; }

    public string? TseSig { get; set; }

    public string? TseFehler { get; set; }

    public string? TseVorgDaten { get; set; }

    public long Id { get; set; }
}
