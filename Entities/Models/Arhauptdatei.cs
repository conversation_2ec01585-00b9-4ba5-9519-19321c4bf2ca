using Entities.Common;

namespace Entities.Models;

public partial class Arhauptdatei
{
    public long? Renummer { get; set; }

    public string? Status { get; set; }

    public PostingsSchema? ErfSchema { get; set; }

    public DateTime? Redatum { get; set; }

    public short? GridPosition { get; set; }

    public long? ArtNr { get; set; }

    public string? Bezeichnung { get; set; }

    public double? Anzahl { get; set; }

    public string? BzgGr { get; set; }

    public decimal? GesStck { get; set; }

    public decimal? Gesamtmenge { get; set; }

    public decimal? Einzelpreis { get; set; }

    public decimal? Gesamtnetto { get; set; }

    public short? MwstSchl { get; set; }

    public decimal? MwstProz { get; set; }

    public string? Mhd { get; set; }

    public string? ChargenNr { get; set; }

    public decimal? Zn { get; set; }

    public decimal? Sk1p { get; set; }

    public decimal? Sk2p { get; set; }

    public decimal? Gr { get; set; }

    public long? VpckNr { get; set; }

    public long? Lsnr { get; set; }

    public short? AnzLspos { get; set; }

    public decimal? VertrProv { get; set; }

    public string? Vdmproz { get; set; }

    public short? Faktor { get; set; }

    public string? Konto { get; set; }

    public long? KontraktNr { get; set; }

    public string? KnEzZuschlag { get; set; }

    public string? KontraktKng { get; set; }

    public decimal? Fracht { get; set; }

    public string? Filiale { get; set; }

    public long? KdNrWe { get; set; }

    public long? VertrNr { get; set; }

    public short? Bld { get; set; }

    public short? ArtGruppe { get; set; }

    public string? ArtEan { get; set; }

    public string? Zustellart { get; set; }

    public decimal? AlteMenge { get; set; }

    public long? StreckenNr { get; set; }

    public long? Hartikel { get; set; }

    public decimal? Pfracht { get; set; }

    public decimal? GrpSkonto { get; set; }

    public double? SpezRab { get; set; }

    public double? SpezRabGes { get; set; }

    public double? MengRab { get; set; }

    public double? SpezSkonto { get; set; }

    public double? DirektRab { get; set; }

    public DateTime? MaabrDatum { get; set; }

    public bool? FrachtCheck { get; set; }

    public bool? Lkw { get; set; }

    public long? Kostentraeger { get; set; }

    public long? Kostenstelle { get; set; }

    public long? FbzgNr { get; set; }

    public decimal? ArtEinhVp { get; set; }

    public decimal? NettoEk { get; set; }

    public decimal? EmpfBruttoVk { get; set; }

    public short? LagerNr { get; set; }

    public long? KolliInh { get; set; }

    public decimal? PakGewicht { get; set; }

    public long Id { get; set; }
}
