namespace Entities.Models;

public partial class Etabelle
{
    public int? Tbnr { get; set; }

    public short? Tbaw { get; set; }

    public decimal? Lbwert1 { get; set; }

    public decimal? Lbwert2 { get; set; }

    public decimal? Lbwert3 { get; set; }

    public decimal? Lbwert4 { get; set; }

    public decimal? Lbwert5 { get; set; }

    public decimal? Lbwert6 { get; set; }

    public decimal? Lbwert7 { get; set; }

    public decimal? Lbwert8 { get; set; }

    public decimal? Lbwert9 { get; set; }

    public decimal? Lbwert10 { get; set; }

    public decimal? Lbwert11 { get; set; }

    public decimal? Lbwert12 { get; set; }

    public decimal? Lbwert13 { get; set; }

    public decimal? Lbwert14 { get; set; }

    public decimal? Lbwert15 { get; set; }

    public decimal? Lbwert16 { get; set; }

    public decimal? Lbwert17 { get; set; }

    public decimal? Lbwert18 { get; set; }

    public decimal? Lbwert19 { get; set; }

    public decimal? Lbwert20 { get; set; }

    public decimal? Lbwert21 { get; set; }

    public decimal? Lbwert22 { get; set; }

    public decimal? Lbwert23 { get; set; }

    public decimal? Lbwert24 { get; set; }

    public decimal? Lbwert25 { get; set; }

    public decimal? Lbwert26 { get; set; }

    public decimal? Lbwert27 { get; set; }

    public decimal? Lbwert28 { get; set; }

    public decimal? Lbwert29 { get; set; }

    public decimal? Lbwert30 { get; set; }

    public decimal? Lbwert31 { get; set; }

    public decimal? Lbwert32 { get; set; }

    public decimal? Lbwert33 { get; set; }

    public decimal? Lbwert34 { get; set; }

    public decimal? Lbwert35 { get; set; }

    public decimal? Lbwert36 { get; set; }

    public decimal? Lbwert37 { get; set; }

    public decimal? Lbwert38 { get; set; }

    public decimal? Lbwert39 { get; set; }

    public decimal? Lbwert40 { get; set; }

    public decimal? Lbwert41 { get; set; }

    public decimal? Lbwert42 { get; set; }

    public decimal? Lbwert43 { get; set; }

    public decimal? Lbwert44 { get; set; }

    public decimal? Lbwert45 { get; set; }

    public decimal? Lbwert46 { get; set; }

    public decimal? Lbwert47 { get; set; }

    public decimal? Lbwert48 { get; set; }

    public decimal? Lbwert49 { get; set; }

    public decimal? Lbwert50 { get; set; }

    public decimal? Lbwert51 { get; set; }

    public decimal? Lbwert52 { get; set; }

    public decimal? Lbwert53 { get; set; }

    public decimal? Lbwert54 { get; set; }

    public decimal? Lbwert55 { get; set; }

    public decimal? Lbwert56 { get; set; }

    public decimal? Lbwert57 { get; set; }

    public decimal? Lbwert58 { get; set; }

    public decimal? Lbwert59 { get; set; }

    public decimal? Lbwert60 { get; set; }

    public decimal? Lbwert61 { get; set; }

    public decimal? Lbwert62 { get; set; }

    public decimal? Lbwert63 { get; set; }

    public decimal? Lbwert64 { get; set; }

    public decimal? Lbwert65 { get; set; }

    public decimal? Lbwert66 { get; set; }

    public decimal? Lbwert67 { get; set; }

    public decimal? Lbwert68 { get; set; }

    public decimal? Lbwert69 { get; set; }

    public decimal? Lbwert70 { get; set; }

    public decimal? Lbwert71 { get; set; }

    public decimal? Lbwert72 { get; set; }

    public decimal? Lbwert73 { get; set; }

    public decimal? Lbwert74 { get; set; }

    public decimal? Lbwert75 { get; set; }

    public decimal? Lbwert76 { get; set; }

    public decimal? Lbwert77 { get; set; }

    public decimal? Lbwert78 { get; set; }

    public decimal? Lbwert79 { get; set; }

    public decimal? Lbwert80 { get; set; }

    public decimal? Lbwert81 { get; set; }

    public decimal? Lbwert82 { get; set; }

    public decimal? Lbwert83 { get; set; }

    public decimal? Lbwert84 { get; set; }

    public decimal? Lbwert85 { get; set; }

    public decimal? Lbwert86 { get; set; }

    public decimal? Lbwert87 { get; set; }

    public decimal? Lbwert88 { get; set; }

    public decimal? Lbwert89 { get; set; }

    public decimal? Lbwert90 { get; set; }

    public decimal? Lbwert91 { get; set; }

    public decimal? Lbwert92 { get; set; }

    public decimal? Lbwert93 { get; set; }

    public decimal? Lbwert94 { get; set; }

    public decimal? Lbwert95 { get; set; }

    public decimal? Lbwert96 { get; set; }

    public decimal? Lbwert97 { get; set; }

    public decimal? Lbwert98 { get; set; }

    public decimal? Lbwert99 { get; set; }

    public decimal? Lbwert100 { get; set; }

    public decimal? Wert1 { get; set; }

    public decimal? Wert2 { get; set; }

    public decimal? Wert3 { get; set; }

    public decimal? Wert4 { get; set; }

    public decimal? Wert5 { get; set; }

    public decimal? Wert6 { get; set; }

    public decimal? Wert7 { get; set; }

    public decimal? Wert8 { get; set; }

    public decimal? Wert9 { get; set; }

    public decimal? Wert10 { get; set; }

    public decimal? Wert11 { get; set; }

    public decimal? Wert12 { get; set; }

    public decimal? Wert13 { get; set; }

    public decimal? Wert14 { get; set; }

    public decimal? Wert15 { get; set; }

    public decimal? Wert16 { get; set; }

    public decimal? Wert17 { get; set; }

    public decimal? Wert18 { get; set; }

    public decimal? Wert19 { get; set; }

    public decimal? Wert20 { get; set; }

    public decimal? Wert21 { get; set; }

    public decimal? Wert22 { get; set; }

    public decimal? Wert23 { get; set; }

    public decimal? Wert24 { get; set; }

    public decimal? Wert25 { get; set; }

    public decimal? Wert26 { get; set; }

    public decimal? Wert27 { get; set; }

    public decimal? Wert28 { get; set; }

    public decimal? Wert29 { get; set; }

    public decimal? Wert30 { get; set; }

    public decimal? Wert31 { get; set; }

    public decimal? Wert32 { get; set; }

    public decimal? Wert33 { get; set; }

    public decimal? Wert34 { get; set; }

    public decimal? Wert35 { get; set; }

    public decimal? Wert36 { get; set; }

    public decimal? Wert37 { get; set; }

    public decimal? Wert38 { get; set; }

    public decimal? Wert39 { get; set; }

    public decimal? Wert40 { get; set; }

    public decimal? Wert41 { get; set; }

    public decimal? Wert42 { get; set; }

    public decimal? Wert43 { get; set; }

    public decimal? Wert44 { get; set; }

    public decimal? Wert45 { get; set; }

    public decimal? Wert46 { get; set; }

    public decimal? Wert47 { get; set; }

    public decimal? Wert48 { get; set; }

    public decimal? Wert49 { get; set; }

    public decimal? Wert50 { get; set; }

    public decimal? Wert51 { get; set; }

    public decimal? Wert52 { get; set; }

    public decimal? Wert53 { get; set; }

    public decimal? Wert54 { get; set; }

    public decimal? Wert55 { get; set; }

    public decimal? Wert56 { get; set; }

    public decimal? Wert57 { get; set; }

    public decimal? Wert58 { get; set; }

    public decimal? Wert59 { get; set; }

    public decimal? Wert60 { get; set; }

    public decimal? Wert61 { get; set; }

    public decimal? Wert62 { get; set; }

    public decimal? Wert63 { get; set; }

    public decimal? Wert64 { get; set; }

    public decimal? Wert65 { get; set; }

    public decimal? Wert66 { get; set; }

    public decimal? Wert67 { get; set; }

    public decimal? Wert68 { get; set; }

    public decimal? Wert69 { get; set; }

    public decimal? Wert70 { get; set; }

    public decimal? Wert71 { get; set; }

    public decimal? Wert72 { get; set; }

    public decimal? Wert73 { get; set; }

    public decimal? Wert74 { get; set; }

    public decimal? Wert75 { get; set; }

    public decimal? Wert76 { get; set; }

    public decimal? Wert77 { get; set; }

    public decimal? Wert78 { get; set; }

    public decimal? Wert79 { get; set; }

    public decimal? Wert80 { get; set; }

    public decimal? Wert81 { get; set; }

    public decimal? Wert82 { get; set; }

    public decimal? Wert83 { get; set; }

    public decimal? Wert84 { get; set; }

    public decimal? Wert85 { get; set; }

    public decimal? Wert86 { get; set; }

    public decimal? Wert87 { get; set; }

    public decimal? Wert88 { get; set; }

    public decimal? Wert89 { get; set; }

    public decimal? Wert90 { get; set; }

    public decimal? Wert91 { get; set; }

    public decimal? Wert92 { get; set; }

    public decimal? Wert93 { get; set; }

    public decimal? Wert94 { get; set; }

    public decimal? Wert95 { get; set; }

    public decimal? Wert96 { get; set; }

    public decimal? Wert97 { get; set; }

    public decimal? Wert98 { get; set; }

    public decimal? Wert99 { get; set; }

    public decimal? Wert100 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
