namespace Entities.Models;

public partial class Blskopf
{
    public long? Auftragsnummer { get; set; }

    public DateTime? Ldatum { get; set; }

    public bool? Abgeschlossen { get; set; }

    public string? EigKdnr { get; set; }

    public string? Bearbeiter { get; set; }

    public long? Lfnr { get; set; }

    public string? Lfsbg { get; set; }

    public string? AnredeLf { get; set; }

    public string? Name1Lf { get; set; }

    public string? Name2Lf { get; set; }

    public string? Name3Lf { get; set; }

    public string? StrasseLf { get; set; }

    public string? Plzlf { get; set; }

    public string? OrtLf { get; set; }

    public string? LandLf { get; set; }

    public short? Bldlf { get; set; }

    public string? TelefonLf { get; set; }

    public string? FaxLf { get; set; }

    public long? Alnr { get; set; }

    public string? AnredeAl { get; set; }

    public string? Name1Al { get; set; }

    public string? Name2Al { get; set; }

    public string? Name3Al { get; set; }

    public string? StrasseAl { get; set; }

    public string? Plzal { get; set; }

    public string? OrtAl { get; set; }

    public string? LandAl { get; set; }

    public int? Bldal { get; set; }

    public string? TelefonAl { get; set; }

    public string? FaxAl { get; set; }

    public bool? Lieferschein { get; set; }

    public bool? ProdAuftrag { get; set; }

    public string? Zustellart { get; set; }

    public string? Bemerkung { get; set; }

    public string? AuslansSchl { get; set; }

    public string? AbrKennzeichen { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public long? Ervnummer { get; set; }

    public short? FilialeNr { get; set; }

    public string? RenrLf { get; set; }

    public string? LsnrLf { get; set; }

    public bool? FrachtKng { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public DateTime? Lieferdatum { get; set; }

    public string? LiefRestr { get; set; }
}
