namespace Entities.Models;

public partial class ProdSpez
{
    public long? SpezNr { get; set; }

    public long? ArtikelNr { get; set; }

    public long? KundNr { get; set; }

    public string? ProdBeschr { get; set; }

    public string? <PERSON><PERSON><PERSON>toff { get; set; }

    public string? Bezeichn { get; set; }

    public string? ZollNr { get; set; }

    public string? Farbe { get; set; }

    public string? Geruch { get; set; }

    public string? Geschmack { get; set; }

    public string? PflSchutzMittel { get; set; }

    public string? Gvo { get; set; }

    public string? Allergen { get; set; }

    public string? VerpArt { get; set; }

    public string? TranspBed { get; set; }

    public string? Mhd { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public int RevNr { get; set; }

    public short SprachIndx { get; set; }

    public DateTime? Gdatum { get; set; }

    public string? ArtBeschr { get; set; }

    public long Id { get; set; }
}
