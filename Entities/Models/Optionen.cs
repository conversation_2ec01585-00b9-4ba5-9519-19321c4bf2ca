namespace Entities.Models;

public partial class Optionen
{
    public int? AktKundenNr { get; set; }

    public short? KdlfartikelPrsFdg { get; set; }

    public int? AktLieferantenNr { get; set; }

    public decimal? Lfust1Proz { get; set; }

    public decimal? Lfust2Proz { get; set; }

    public decimal? Lfust3Proz { get; set; }

    public bool ListeErzgem { get; set; }

    public bool TabelleEntfKl { get; set; }

    public bool Radatumsabfr { get; set; }

    public bool Rastreckenabfr { get; set; }

    public bool Rakontraktabgleich { get; set; }

    public bool Razustellart { get; set; }

    public bool Ravpckg { get; set; }

    public bool Ralsdruck { get; set; }

    public bool RabelegDruck { get; set; }

    public bool Ralswebs { get; set; }

    public bool Ravormonat { get; set; }

    public bool Raekvk { get; set; }

    public bool RaeinKontrakt { get; set; }

    public bool Rabemanzeigen { get; set; }

    public bool RazzvonRd { get; set; }

    public bool Rabeztext { get; set; }

    public bool RaautoDruck { get; set; }

    public string? Razzstandard { get; set; }

    public string? RaausdruckRe { get; set; }

    public string? RaausdruckGu { get; set; }

    public decimal? Raskonto1Proz { get; set; }

    public short? Raskonto1Tage { get; set; }

    public decimal? Raskonto2Proz { get; set; }

    public short? Raskonto2Tage { get; set; }

    public short? RanettoTage { get; set; }

    public short? RakarenzTage { get; set; }

    public int? AktRechnNr { get; set; }

    public bool Ravorschau { get; set; }

    public bool KundNrInd { get; set; }

    public bool LieferantenNrInd { get; set; }

    public bool LiefAutoDruck { get; set; }

    public int? VtNrInd { get; set; }

    public short? VtDrNr { get; set; }

    public bool Alsliefersch { get; set; }

    public bool Alsauftrag { get; set; }

    public bool AlsautoDruck { get; set; }

    public bool AlsautoDruckA { get; set; }

    public bool Alslagerakt { get; set; }

    public bool AlsauftrSperren { get; set; }

    public bool Alszustellartabfr { get; set; }

    public bool AlsartNrDruck { get; set; }

    public bool AlsprAbglWere { get; set; }

    public bool AlsprEkvk { get; set; }

    public bool Alsopanzeige { get; set; }

    public bool AlsletzterPreis { get; set; }

    public bool AlsnrIndex { get; set; }

    public int? Alsnr { get; set; }

    public string? AlsdrucktextLs { get; set; }

    public string? AlsdrucktextA { get; set; }

    public string? AlsdrucktextRt { get; set; }

    public bool Alsserienerfassung { get; set; }

    public int? KteingangNr { get; set; }

    public int? KtausgangNr { get; set; }

    public bool Ktbetreuer { get; set; }

    public bool Ktklassifikation { get; set; }

    public bool Ktplusminus { get; set; }

    public bool Ktfracht { get; set; }

    public bool Ktf1check { get; set; }

    public bool Ktf10modus { get; set; }

    public short? KtdruckerNr { get; set; }

    public bool KthändlerNr { get; set; }

    public bool KtautoDruck { get; set; }

    public short? StdruckerNr { get; set; }

    public string? WgserPort { get; set; }

    public int? WgsindexNr { get; set; }

    public decimal? WggewAkzept { get; set; }

    public bool Wgwaagemanuell { get; set; }

    public string? Wgwaagenart { get; set; }

    public bool Wgktabgl { get; set; }

    public bool Wganlieferer { get; set; }

    public bool Wgsiloakt { get; set; }

    public bool Wgstapel { get; set; }

    public bool Wglagerakt { get; set; }

    public bool Wgpos2 { get; set; }

    public bool Wgextrakt { get; set; }

    public bool Wgskp { get; set; }

    public short? Wgdruckpar { get; set; }

    public short? Wgindexpar { get; set; }

    public bool Ercheck1 { get; set; }

    public bool Ercheck2 { get; set; }

    public bool Ercheck3 { get; set; }

    public bool Ercheck4 { get; set; }

    public bool Ercheck5 { get; set; }

    public bool Ercheck6 { get; set; }

    public bool Ercheck7 { get; set; }

    public string? Erdrucktext { get; set; }

    public short? ErdruckerNr { get; set; }

    public int? ErindexNr { get; set; }

    public short? ErindexParameter { get; set; }

    public short? ErdruckParameter { get; set; }

    public int? Gbindex { get; set; }

    public short? Gbrapspos1 { get; set; }

    public short? Gbrapspos2 { get; set; }

    public short? Gbrapspos3 { get; set; }

    public short? Gbsbpos1 { get; set; }

    public short? Gbsbpos2 { get; set; }

    public short? Gbsbpos3 { get; set; }

    public short? Gboelpos1 { get; set; }

    public short? Gboelpos2 { get; set; }

    public short? Gboelpos3 { get; set; }

    public int? Gbkdnrvon { get; set; }

    public int? Gbkdnrbis { get; set; }

    public int? Gblfnrvon { get; set; }

    public int? Gblfnrbis { get; set; }

    public short? Gbzgk { get; set; }

    public string? Gbmt1 { get; set; }

    public string? Gbmt2 { get; set; }

    public short? GbberFaktor { get; set; }

    public bool Gbpartie { get; set; }

    public bool GbbruttoSilo { get; set; }

    public bool Gbzusatz { get; set; }

    public bool GbbruttoAbr { get; set; }

    public bool Gbvormonat { get; set; }

    public bool Gbspreis { get; set; }

    public bool Gbzzdatum { get; set; }

    public bool Elsparam1 { get; set; }

    public bool Elsparam2 { get; set; }

    public int? Elsparam3 { get; set; }

    public short? GbvdruckerNr { get; set; }

    public short? ElsdruckerNr { get; set; }

    public int? KindexNr { get; set; }

    public short? KdruckerNr { get; set; }

    public string? KasText1 { get; set; }

    public string? KasText2 { get; set; }

    public string? KasText3 { get; set; }

    public string? KasText4 { get; set; }

    public string? KasText5 { get; set; }

    public string? KasText6 { get; set; }

    public string? KasText7 { get; set; }

    public string? KasText8 { get; set; }

    public string? KasText9 { get; set; }

    public string? KasText10 { get; set; }

    public string? KasText11 { get; set; }

    public string? KasText12 { get; set; }

    public string? KasText13 { get; set; }

    public string? KasText14 { get; set; }

    public string? KasText15 { get; set; }

    public string? KasText16 { get; set; }

    public string? KasText17 { get; set; }

    public string? KasText18 { get; set; }

    public string? KasText19 { get; set; }

    public bool Kedit { get; set; }

    public short? KasDrAbschl { get; set; }

    public string? KtoWashOut1 { get; set; }

    public string? KtoWashOut2 { get; set; }

    public string? KtoWashOut3 { get; set; }

    public short? RadruckerNr2 { get; set; }

    public short? RaausdrAnzahl2 { get; set; }

    public bool Rasopt { get; set; }

    public bool Rawee { get; set; }

    public int? Ubindex { get; set; }

    public short? GbwgsSort { get; set; }

    public short? GbwgsSortFolge { get; set; }

    public short? GbvdruckerNr2 { get; set; }

    public short? Gbanz1 { get; set; }

    public short? Gbanz2 { get; set; }

    public string? KasGtext1 { get; set; }

    public string? KasGtext2 { get; set; }

    public string? KasGtext3 { get; set; }

    public string? KasGtext4 { get; set; }

    public string? KasGtext5 { get; set; }

    public string? KasGtext6 { get; set; }

    public string? KasGtext7 { get; set; }

    public string? KasGtext8 { get; set; }

    public string? KasGtext9 { get; set; }

    public string? KasGtext10 { get; set; }

    public short? KtdruckAnz1 { get; set; }

    public short? KtdruckAnz2 { get; set; }

    public short? KtdruckerNr2 { get; set; }

    public short? AlsdruckerNr2 { get; set; }

    public short? AlsanzAusdr2 { get; set; }

    public short? AufdruckerNr1 { get; set; }

    public short? AufanzAusdr1 { get; set; }

    public short? AufdruckerNr2 { get; set; }

    public short? AufanzAusdr2 { get; set; }

    public string? Ardrname1 { get; set; }

    public int? Dp1nr { get; set; }

    public bool Dp1man { get; set; }

    public bool Ralsabr { get; set; }

    public bool Kdgdi { get; set; }

    public bool Lfgdi { get; set; }

    public short? AnlanzAusdr1 { get; set; }

    public short? AnlanzAusdr2 { get; set; }

    public short? AnldruckerNr1 { get; set; }

    public short? AnldruckerNr2 { get; set; }

    public string? KasAtext1 { get; set; }

    public string? KasAtext2 { get; set; }

    public string? KasAtext3 { get; set; }

    public string? KasAtext4 { get; set; }

    public string? KasAtext5 { get; set; }

    public string? KasAtext6 { get; set; }

    public string? KasAtext7 { get; set; }

    public string? KasAtext8 { get; set; }

    public string? KasAtext9 { get; set; }

    public string? KasAtext10 { get; set; }

    public int? Dp2nr { get; set; }

    public bool Dp2man { get; set; }

    public int? KartVon { get; set; }

    public int? KartBis { get; set; }

    public short? RaausdrAnzahl3 { get; set; }

    public short? RadruckerNr3 { get; set; }

    public short? Ktrs3ek { get; set; }

    public short? Ktrs3vk { get; set; }

    public bool GbdruckAbfr { get; set; }

    public bool GbnrDrehen { get; set; }

    public bool GbvalutaBeachten { get; set; }

    public bool AlspreisEingabe { get; set; }

    public string? ScheckDrn { get; set; }

    public bool Gbdarst { get; set; }

    public bool GbktrAlle { get; set; }

    public bool Gblswe { get; set; }

    public bool Lpakt { get; set; }

    public bool SendRecAkt { get; set; }

    public bool MibuIndex { get; set; }

    public int? MibuNr { get; set; }

    public bool GbdatumAb { get; set; }

    public bool ArdatumAb { get; set; }

    public bool Wgrsm { get; set; }

    public short? Wgrsmanz { get; set; }

    public short? Wgrsmdrucker { get; set; }

    public bool ArgrpSkt { get; set; }

    public bool Arzusa { get; set; }

    public short? AlssortN { get; set; }

    public short? AlssortF { get; set; }

    public bool KtdatumDr { get; set; }

    public bool ArzwiZei { get; set; }

    public bool KrabArt { get; set; }

    public bool Alseingabe { get; set; }

    public bool Alsposition { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
