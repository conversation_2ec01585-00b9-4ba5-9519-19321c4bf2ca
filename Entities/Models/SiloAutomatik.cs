namespace Entities.Models;

public partial class SiloAutomatik
{
    public long RezNr { get; set; }

    public int? PosNr { get; set; }

    public string? Bezeichnung { get; set; }

    public decimal? IdealWert { get; set; }

    public bool? Muss { get; set; }

    public decimal? <PERSON>l<PERSON><PERSON> { get; set; }

    public decimal? <PERSON><PERSON><PERSON><PERSON> { get; set; }

    public decimal? Gr<PERSON>ann { get; set; }

    public decimal? Gr<PERSON><PERSON> { get; set; }

    public int Reihenfolge { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
