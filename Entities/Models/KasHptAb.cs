namespace Entities.Models;

public partial class KasHptAb
{
    public long? Kvnr { get; set; }

    public int? KuserNr { get; set; }

    public DateTime? Kdatum { get; set; }

    public short? KposNr { get; set; }

    public string? Kkn { get; set; }

    public long? KartNr { get; set; }

    public string? Kbez { get; set; }

    public double? <PERSON><PERSON><PERSON> { get; set; }

    public decimal? Kep { get; set; }

    public decimal? Kgp { get; set; }

    public decimal? Kmwst { get; set; }

    public long? KartNr2 { get; set; }

    public long? HartNr { get; set; }

    public string? KvpNr { get; set; }

    public short? Kfaktor { get; set; }

    public string? Kkonto { get; set; }

    public short? KmwstSchl { get; set; }

    public short? KartGrp { get; set; }

    public string? Kopt1 { get; set; }

    public decimal? Kopt2 { get; set; }

    public double? Kopt3 { get; set; }

    public string? Kopt4 { get; set; }

    public double? KaltMenge { get; set; }

    public short? Ksta<PERSON> { get; set; }

    public string? Kgrund { get; set; }

    public short? Kvnv { get; set; }

    public int? KabschlNr { get; set; }

    public DateTime? Kuhrzeit { get; set; }

    public bool? KmonAbschl { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public long? KvnrAlt { get; set; }

    public decimal? Gegeben { get; set; }

    public decimal? Zurueck { get; set; }

    public long? FilialNr { get; set; }

    public long? Gsnr { get; set; }

    public decimal? Gsrest { get; set; }
}
