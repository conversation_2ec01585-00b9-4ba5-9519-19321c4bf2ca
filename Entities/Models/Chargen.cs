namespace Entities.Models;

public partial class Chargen
{
    public string? ChargNr { get; set; }

    public long? ArtikelNr { get; set; }

    public string? ArtBez1 { get; set; }

    public string? ArtBez2 { get; set; }

    public long? Kdnr { get; set; }

    public DateTime? JobDatum { get; set; }

    public DateTime? Mhd { get; set; }

    public string? Bediener { get; set; }

    public string? ProtokollNr { get; set; }

    public string? SiloProtokoll { get; set; }

    public string? Artikelzusatz { get; set; }

    public string? ChargTxt { get; set; }

    public string? Qualitaet { get; set; }

    public string? Aussehen { get; set; }

    public string? Konsistenz { get; set; }

    public string? Geruch { get; set; }

    public string? Atext0 { get; set; }

    public string? Atext1 { get; set; }

    public string? Atext2 { get; set; }

    public string? Atext3 { get; set; }

    public string? Atext4 { get; set; }

    public string? Atext5 { get; set; }

    public string? Atext6 { get; set; }

    public string? Atext7 { get; set; }

    public string? Atext8 { get; set; }

    public string? Atext9 { get; set; }

    public string? Atext10 { get; set; }

    public string? Atext11 { get; set; }

    public string? Atext12 { get; set; }

    public string? Atext13 { get; set; }

    public string? Atext14 { get; set; }

    public string? Atext15 { get; set; }

    public string? Atext16 { get; set; }

    public string? Atext17 { get; set; }

    public string? Atext18 { get; set; }

    public string? Atext19 { get; set; }

    public string? Atext20 { get; set; }

    public string? Atext21 { get; set; }

    public string? Atext22 { get; set; }

    public string? Atext23 { get; set; }

    public string? Atext24 { get; set; }

    public string? Awert0 { get; set; }

    public string? Awert1 { get; set; }

    public string? Awert2 { get; set; }

    public string? Awert3 { get; set; }

    public string? Awert4 { get; set; }

    public string? Awert5 { get; set; }

    public string? Awert6 { get; set; }

    public string? Awert7 { get; set; }

    public string? Awert8 { get; set; }

    public string? Awert9 { get; set; }

    public string? Awert10 { get; set; }

    public string? Awert11 { get; set; }

    public string? Awert12 { get; set; }

    public string? Awert13 { get; set; }

    public string? Awert14 { get; set; }

    public string? Awert15 { get; set; }

    public string? Awert16 { get; set; }

    public string? Awert17 { get; set; }

    public string? Awert18 { get; set; }

    public string? Awert19 { get; set; }

    public string? Awert20 { get; set; }

    public string? Awert21 { get; set; }

    public string? Awert22 { get; set; }

    public string? Awert23 { get; set; }

    public string? Awert24 { get; set; }

    public string? Adesc0 { get; set; }

    public string? Adesc1 { get; set; }

    public string? Adesc2 { get; set; }

    public string? Adesc3 { get; set; }

    public string? Adesc4 { get; set; }

    public string? Adesc5 { get; set; }

    public string? Adesc6 { get; set; }

    public string? Adesc7 { get; set; }

    public string? Adesc8 { get; set; }

    public string? Adesc9 { get; set; }

    public string? Adesc10 { get; set; }

    public string? Adesc11 { get; set; }

    public string? Adesc12 { get; set; }

    public string? Adesc13 { get; set; }

    public string? Adesc14 { get; set; }

    public string? Adesc15 { get; set; }

    public string? Adesc16 { get; set; }

    public string? Adesc17 { get; set; }

    public string? Adesc18 { get; set; }

    public string? Adesc19 { get; set; }

    public string? Adesc20 { get; set; }

    public string? Adesc21 { get; set; }

    public string? Adesc22 { get; set; }

    public string? Adesc23 { get; set; }

    public string? Adesc24 { get; set; }

    public string? Aeinheit0 { get; set; }

    public string? Aeinheit1 { get; set; }

    public string? Aeinheit2 { get; set; }

    public string? Aeinheit3 { get; set; }

    public string? Aeinheit4 { get; set; }

    public string? Aeinheit5 { get; set; }

    public string? Aeinheit6 { get; set; }

    public string? Aeinheit7 { get; set; }

    public string? Aeinheit8 { get; set; }

    public string? Aeinheit9 { get; set; }

    public string? Aeinheit10 { get; set; }

    public string? Aeinheit11 { get; set; }

    public string? Aeinheit12 { get; set; }

    public string? Aeinheit13 { get; set; }

    public string? Aeinheit14 { get; set; }

    public string? Aeinheit15 { get; set; }

    public string? Aeinheit16 { get; set; }

    public string? Aeinheit17 { get; set; }

    public string? Aeinheit18 { get; set; }

    public string? Aeinheit19 { get; set; }

    public string? Aeinheit20 { get; set; }

    public string? Aeinheit21 { get; set; }

    public string? Aeinheit22 { get; set; }

    public string? Aeinheit23 { get; set; }

    public string? Aeinheit24 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
