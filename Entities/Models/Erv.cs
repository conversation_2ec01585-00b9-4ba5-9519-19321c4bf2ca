namespace Entities.Models;

public partial class Erv
{
    public long Ernummer { get; set; }

    public DateTime? Erdatum { get; set; }

    public string? Errelf { get; set; }

    public long? Erlfnr { get; set; }

    public string? Ersbg { get; set; }

    public string? Eranrede { get; set; }

    public string? Ername1 { get; set; }

    public string? Ername2 { get; set; }

    public string? Ername3 { get; set; }

    public string? Erstrasse { get; set; }

    public string? Erplz { get; set; }

    public string? Erort { get; set; }

    public string? Erland { get; set; }

    public string? Erzustellart { get; set; }

    public decimal? Erskto1P { get; set; }

    public short? Erskto1T { get; set; }

    public decimal? Erskto2P { get; set; }

    public short? Erskto2T { get; set; }

    public short? ErnettoTage { get; set; }

    public decimal? Ermwst1P { get; set; }

    public decimal? Ermwst2P { get; set; }

    public decimal? Ernetto1 { get; set; }

    public decimal? Ernetto2 { get; set; }

    public decimal? Ermwst1E { get; set; }

    public decimal? Ermwst2E { get; set; }

    public decimal? Erbrutto1 { get; set; }

    public decimal? Erbrutto2 { get; set; }

    public decimal? Erskto1E { get; set; }

    public decimal? Erskto2E { get; set; }

    public decimal? ErgesNetto { get; set; }

    public decimal? ErgesMwst { get; set; }

    public decimal? ErgesBrutto { get; set; }

    public decimal? Errebetrag { get; set; }

    public short? Erstatus { get; set; }

    public double? KontoNr { get; set; }

    public string? Erbank { get; set; }

    public string? Erblz { get; set; }

    public string? Erkontonr { get; set; }

    public string? Erbemerkung { get; set; }

    public string? Erbld { get; set; }

    public short? Erbldnr { get; set; }

    public long? ErstreckenNr { get; set; }

    public short? ErfrachtRe { get; set; }

    public string? ErstornoKennung { get; set; }

    public string? Erasld { get; set; }

    public string? Erwru { get; set; }

    public decimal? ErwhrgKurs { get; set; }

    public short? ErumrFakt { get; set; }

    public string? Erustidnr { get; set; }

    public string? ErstNr { get; set; }

    public string? Erzz1 { get; set; }

    public string? Erzz2 { get; set; }

    public string? Erzz3 { get; set; }

    public long? ErvertrNr { get; set; }

    public double? ErgesMenge { get; set; }

    public long? EralteNr { get; set; }

    public bool? Erfibu2 { get; set; }

    public bool? Eingang { get; set; }

    public bool? Send { get; set; }

    public bool? Anzahlung { get; set; }

    public string? Iban { get; set; }

    public string? Swift { get; set; }

    public long? AnzAbrNummer { get; set; }

    public short? AnzBankTage { get; set; }

    public int? FilialNr { get; set; }

    public DateTime? Vdatum { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool? BruttoAend { get; set; }

    public bool? NuetzEing { get; set; }
}
