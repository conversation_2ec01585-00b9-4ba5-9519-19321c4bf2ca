namespace Entities.Models;

public partial class Kontrakt
{
    public long Ktnr { get; set; }

    public string? Ktbezeichnung { get; set; }

    public string? KtmatchCode { get; set; }

    public DateTime? Ktdatum { get; set; }

    public bool? Ktek { get; set; }

    public long KtliefNr { get; set; }

    public string? KtliefAdr1 { get; set; }

    public string? KtliefPlz { get; set; }

    public string? KtliefOrt { get; set; }

    public DateTime? Ktstart { get; set; }

    public DateTime? Ktende { get; set; }

    public long KtartikelNr { get; set; }

    public string? KtartBez1 { get; set; }

    public decimal? Ktbasispreis { get; set; }

    public short? Kternte { get; set; }

    public decimal? Ktreportaufschlag { get; set; }

    public DateTime? KtabDatum { get; set; }

    public DateTime? KtbisDatum { get; set; }

    public long? KtpreislistNr { get; set; }

    public bool? K<PERSON>bnah<PERSON> { get; set; }

    public long? KthändlerNr { get; set; }

    public string? KtnrH<PERSON>nd<PERSON> { get; set; }

    public float? KtstartMenge { get; set; }

    public float? KtabgerMenge { get; set; }

    public float? KtrestMenge { get; set; }

    public string? Ktvalutatage { get; set; }

    public DateTime? Ktvdatum { get; set; }

    public string? Ktvcode { get; set; }

    public bool? Ktvbank { get; set; }

    public bool? Kterledigt { get; set; }

    public bool? EigLager { get; set; }

    public string? KtliefSbg { get; set; }

    public string? KtliefAnrede { get; set; }

    public string? KtliefAdr2 { get; set; }

    public string? KtliefAdr3 { get; set; }

    public string? KtliefStraße { get; set; }

    public string? KtliefLand { get; set; }

    public string? KtliefTelefon { get; set; }

    public string? KtliefTelefax { get; set; }

    public short? Ktkennung { get; set; }

    public string? Ktbetreuer { get; set; }

    public short? Ktklassifizierung { get; set; }

    public string? KtartBez2 { get; set; }

    public float? Ktvorauszahlg { get; set; }

    public string? Ktparität { get; set; }

    public long? KtmaklerNr { get; set; }

    public float? KtprovHöhe { get; set; }

    public long? KtbeladestelleNr { get; set; }

    public string? KtbstSbg { get; set; }

    public string? KtbstName1 { get; set; }

    public string? KtbstName2 { get; set; }

    public string? KtbstStrasse { get; set; }

    public string? KtbstPlz { get; set; }

    public string? KtbstOrt { get; set; }

    public string? KtbstTelefon { get; set; }

    public string? KtbstTelefax { get; set; }

    public bool? KtmonRate { get; set; }

    public bool? Ktkwahl { get; set; }

    public bool? KtfreiEing { get; set; }

    public string? KtfreiText { get; set; }

    public bool? KtloeschKng { get; set; }

    public decimal? Ktfracht { get; set; }

    public short? Ktasld { get; set; }

    public string? Ktwhrg { get; set; }

    public decimal? KtwhrgKurs { get; set; }

    public bool? KtkursTabelle { get; set; }

    public string? KtmaklerN1 { get; set; }

    public string? KtmaklerN2 { get; set; }

    public string? KtmaklerPlz { get; set; }

    public string? KtmaklerOrt { get; set; }

    public string? KtmaklerTel { get; set; }

    public string? KtustIdnr { get; set; }

    public double? PeriodMenge { get; set; }

    public bool? Send { get; set; }

    public string? Ktmobil { get; set; }

    public string? KtzuText1 { get; set; }

    public string? KtzuText2 { get; set; }

    public string? KtzuText3 { get; set; }

    public string? KtzuText4 { get; set; }

    public string? KtzuText5 { get; set; }

    public string? KtzuText6 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public decimal? GrPreis { get; set; }

    public decimal? GrWert { get; set; }

    public int? Lbpos { get; set; }

    public decimal? MatifPreis { get; set; }

    public bool? PktrKng { get; set; }

    public long? PktrNr { get; set; }

    public decimal? PrPreis { get; set; }

    public string? Email { get; set; }

    public long? KtvertrNr { get; set; }

    public bool? Lagergeld { get; set; }
}
