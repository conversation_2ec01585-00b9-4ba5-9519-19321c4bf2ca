namespace Entities.Models;

public partial class Kunden
{
    public long Kdnummer { get; set; }

    public string Kdsbg { get; set; } = null!;

    public string? KdansprPartner { get; set; }

    public string? Kdanrede { get; set; }

    public string? Kdname1 { get; set; }

    public string? Kdname2 { get; set; }

    public string? Kdname3 { get; set; }

    public string? Kdstrasse { get; set; }

    public string? Kdplz { get; set; }

    public string? Kdort { get; set; }

    public string? Kdpostfach { get; set; }

    public string? Kdplzpostfach { get; set; }

    public string? Kdland { get; set; }

    public long? KdvertrNr { get; set; }

    public short? Kdstaffelnr { get; set; }

    public string? Kdbundesland { get; set; }

    public short? KdbldKennung { get; set; }

    public string? KdustIdnr { get; set; }

    public short? KdbranchenKng1 { get; set; }

    public short? KdbranchenKng2 { get; set; }

    public short? KdbranchenKng3 { get; set; }

    public short? KdbranchenKng4 { get; set; }

    public short? KdbranchenKng5 { get; set; }

    public short? KdbranchenKng6 { get; set; }

    public string? Kdtelefon1 { get; set; }

    public string? Kdtelefon2 { get; set; }

    public string? Kdtelefax { get; set; }

    public string? Kdmobil { get; set; }

    public string? Kdemail { get; set; }

    public string? Kdinternet { get; set; }

    public long? KdpreislNr1 { get; set; }

    public long? KdpreislNr2 { get; set; }

    public long? KdpreislNr3 { get; set; }

    public long? KdpreislNr4 { get; set; }

    public long? KdpreislNr5 { get; set; }

    public long? KdpreislNr6 { get; set; }

    public string? Kdblz { get; set; }

    public string? KdkontoNr { get; set; }

    public string? Kdbank { get; set; }

    public short? KdbankEinzug { get; set; }

    public string? Kdzahlungsziel { get; set; }

    public decimal? Kdskt1Proz { get; set; }

    public short? Kdskt1Tage { get; set; }

    public decimal? Kdskt2Proz { get; set; }

    public short? Kdskt2Tage { get; set; }

    public short? KdtageNetto { get; set; }

    public decimal? KdgesRabatt { get; set; }

    public long? KdabrStelleNr { get; set; }

    public string? KdabrStelleName { get; set; }

    public string? KdabrStelleOrt { get; set; }

    public string? KdabrStelleStatus { get; set; }

    public string? Kdnotiz { get; set; }

    public DateTime? KddatLetztRechn { get; set; }

    public short? KdbankZuOrdnung { get; set; }

    public short? Kdlieferrythmus { get; set; }

    public long? KdtourenNr { get; set; }

    public decimal? KdlieferSatz { get; set; }

    public int? KdreAusdrAnzahl { get; set; }

    public decimal? KdsollZins { get; set; }

    public decimal? KdhabenZins { get; set; }

    public decimal? Kdkreditlimit { get; set; }

    public string? Kdmitteilungstext { get; set; }

    public short? Kdgruppe { get; set; }

    public bool? Kdhänger { get; set; }

    public bool? Kdentsorger { get; set; }

    public DateTime? Kdgeburtstag { get; set; }

    public short? KdfilialNr { get; set; }

    public short? Kdasld { get; set; }

    public short? Kdrabatt { get; set; }

    public string? Kdintra1 { get; set; }

    public short? Kdintra2 { get; set; }

    public string? Kdle { get; set; }

    public string? Kdleh { get; set; }

    public string? KdLfNr { get; set; }

    public string? KdrabText { get; set; }

    public string? Kdlfr1 { get; set; }

    public string? Kdlfr2 { get; set; }

    public string? Kdlfr3 { get; set; }

    public string? Kdlfr4 { get; set; }

    public bool? Kdanalyse { get; set; }

    public bool? Kdmtl { get; set; }

    public bool? Kdhmtl { get; set; }

    public bool? Kdwtl { get; set; }

    public bool? Kddekade { get; set; }

    public bool? Kdlfstopp { get; set; }

    public bool? Send { get; set; }

    public bool? Mvanalyse { get; set; }

    public bool? Edifact { get; set; }

    public short? Kdgaw { get; set; }

    public bool? Kdsperr { get; set; }

    public bool? EigLager { get; set; }

    public DateTime? EdifactDatum { get; set; }

    public int? Druckanzahl { get; set; }

    public DateTime? KdletztAend { get; set; }

    public bool? KdmitTour { get; set; }

    public double? Kilometer { get; set; }

    public string? Zfeld1 { get; set; }

    public string? Zfeld2 { get; set; }

    public string? Zfeld3 { get; set; }

    public bool? KdfacFlowSend { get; set; }

    public bool? FactSend { get; set; }

    public bool? Gedifact { get; set; }

    public bool? LieferAvis { get; set; }

    public DateTime? LieferAvisDatum { get; set; }

    public string? Kdswift { get; set; }

    public string? Kdiban { get; set; }

    public bool? Iln { get; set; }

    public decimal? Anzahlungsbetrag { get; set; }

    public bool? Nvo { get; set; }

    public short? Nvojahr { get; set; }

    public string? Nvonr { get; set; }

    public bool? Nvoscheinegeschrieben { get; set; }

    public string? Vvonr { get; set; }

    public string? DruckForm1 { get; set; }

    public string? DruckForm2 { get; set; }

    public string? ArdruckForm { get; set; }

    public long? KdktrStelleNr { get; set; }

    public long? Katnr { get; set; }

    public long? VerbNr { get; set; }

    public bool? Hebebuehne { get; set; }

    public string? KreditBem { get; set; }

    public short? MandatNr { get; set; }

    public string? DruckRform { get; set; }

    public bool? EmailVersand { get; set; }

    public bool? SachKuNachweis { get; set; }

    public string? Ramail { get; set; }

    public string? Mvmail { get; set; }

    public bool? SammelEmail { get; set; }

    public bool? MvmailVersand { get; set; }

    public bool? OrdersBest { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? AlsdruckForm { get; set; }

    public string? Mvformular { get; set; }

    public bool? Mvetikett { get; set; }

    public bool? WgsmailVersand { get; set; }

    public string? MvanalyseFormular { get; set; }

    public short? MvdruckAnzahl { get; set; }

    public bool? Vorkasse { get; set; }

    public DateTime? TimeWindowFrom1 { get; set; }

    public DateTime? TimeWindowFrom2 { get; set; }

    public DateTime? TimeWindowFrom3 { get; set; }

    public DateTime? TimeWindowFrom4 { get; set; }

    public DateTime? TimeWindowFrom5 { get; set; }

    public DateTime? TimeWindowFrom6 { get; set; }

    public DateTime? TimeWindowFrom7 { get; set; }

    public DateTime? TimeWindowTo1 { get; set; }

    public DateTime? TimeWindowTo2 { get; set; }

    public DateTime? TimeWindowTo3 { get; set; }

    public DateTime? TimeWindowTo4 { get; set; }

    public DateTime? TimeWindowTo5 { get; set; }

    public DateTime? TimeWindowTo6 { get; set; }

    public DateTime? TimeWindowTo7 { get; set; }
}
