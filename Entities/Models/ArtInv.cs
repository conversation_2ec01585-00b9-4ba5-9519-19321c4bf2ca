namespace Entities.Models;

public partial class ArtInv
{
    public int? InvPos { get; set; }

    public long? InvArtNr { get; set; }

    public string? InvBez1 { get; set; }

    public string? InvBzgGr { get; set; }

    public double? InvMenge1 { get; set; }

    public decimal? InvLek { get; set; }

    public decimal? InvLab { get; set; }

    public decimal? InvWert { get; set; }

    public short? InvFaktor { get; set; }

    public double? InvMengeStck { get; set; }

    public string? InvBez2 { get; set; }

    public short? InvFaktorWert { get; set; }

    public DateTime? InvDatum { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public DateTime? ScanDatum { get; set; }
}
