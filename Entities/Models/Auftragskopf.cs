namespace Entities.Models;

public partial class Auftragskopf
{
    public long? Auftragsnummer { get; set; }

    public bool? Auftrag { get; set; }

    public bool? Lieferschein { get; set; }

    public bool? Retoure { get; set; }

    public DateTime? Auftragsdatum { get; set; }

    public DateTime? LvonDatum { get; set; }

    public DateTime? LbisDatum { get; set; }

    public DateTime? Eldatum { get; set; }

    public bool? Tankzug { get; set; }

    public bool? Planenzug { get; set; }

    public bool? Palettenware { get; set; }

    public long? KundenNrWe { get; set; }

    public string? Name1We { get; set; }

    public string? StrasseWe { get; set; }

    public string? Plzwe { get; set; }

    public string? OrtWe { get; set; }

    public long? KundenNrRe { get; set; }

    public string? Name1Re { get; set; }

    public string? StrasseRe { get; set; }

    public string? Plzre { get; set; }

    public string? OrtRe { get; set; }

    public string? AbrKennzeichen { get; set; }

    public long? Arvnummer { get; set; }

    public string? Bearbeiter { get; set; }

    public string? Bestellnr { get; set; }

    public string? Uhrzeit { get; set; }

    public string? GegBlgNr { get; set; }

    public double? GesGew { get; set; }

    public string? Zustellart { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public bool? OhneDisposition { get; set; }

    public string? Transport { get; set; }

    public string? Packungsart { get; set; }

    public string? Sbgwe { get; set; }

    public string? AnredeWe { get; set; }

    public string? Name2We { get; set; }

    public string? Name3We { get; set; }

    public string? LandWe { get; set; }

    public short? Bldwe { get; set; }

    public string? TelefonWe { get; set; }

    public string? FaxWe { get; set; }

    public string? MobilWe { get; set; }

    public string? PfachWe { get; set; }

    public string? Ilnnrwe { get; set; }

    public long? VertrNrWe { get; set; }

    public string? Sbgre { get; set; }

    public string? AnredeRe { get; set; }

    public string? Name2Re { get; set; }

    public string? Name3Re { get; set; }

    public string? LandRe { get; set; }

    public short? Bldre { get; set; }

    public string? TelefonRe { get; set; }

    public string? FaxRe { get; set; }

    public string? MobilRe { get; set; }

    public string? PfachRe { get; set; }

    public string? Ilnnrre { get; set; }

    public long? VertrNrRe { get; set; }

    public long? RegioNummer { get; set; }

    public string? RegioName1 { get; set; }

    public string? RegioOrt { get; set; }

    public string? RegioStatus { get; set; }

    public string? AuslansSchl { get; set; }

    public string? Disponiert { get; set; }

    public short? Preisliste { get; set; }

    public short? Fpreisliste { get; set; }

    public short? BruttoKennung { get; set; }

    public short? Dkennung { get; set; }

    public long? SiloNummer { get; set; }

    public int? FilialNr { get; set; }

    public string? Zustellart2 { get; set; }

    public bool? LsabrKng { get; set; }

    public string? Lsanliefer { get; set; }

    public string? LslfnrWe { get; set; }

    public int? LsdepotNr { get; set; }

    public short? LsdepotKng { get; set; }

    public double? Skontosatz { get; set; }

    public string? LsfremdNr { get; set; }

    public long? TourNr { get; set; }

    public bool? Send { get; set; }

    public string? UhrzeitTxt { get; set; }

    public string? Bemerkung { get; set; }

    public string? Lieferrest { get; set; }

    public string? Lfrestr1 { get; set; }

    public string? Lfrestr2 { get; set; }

    public string? Lfrestr3 { get; set; }

    public string? Lfrestr4 { get; set; }

    public int? PalNr1 { get; set; }

    public string? PalBez1 { get; set; }

    public double? PalAnz1 { get; set; }

    public int? PalNr2 { get; set; }

    public string? PalBez2 { get; set; }

    public double? PalAnz2 { get; set; }

    public int? PalNr3 { get; set; }

    public string? PalBez3 { get; set; }

    public double? PalAnz3 { get; set; }

    public string? ZusTxtBez1 { get; set; }

    public string? ZusTxtBez2 { get; set; }

    public string? ZusTxtBez3 { get; set; }

    public string? ZusTxtBez4 { get; set; }

    public string? ZusTxtBez5 { get; set; }

    public string? ZusTxtBez6 { get; set; }

    public string? ZusTxtInh1 { get; set; }

    public string? ZusTxtInh2 { get; set; }

    public string? ZusTxtInh3 { get; set; }

    public string? ZusTxtInh4 { get; set; }

    public string? ZusTxtInh5 { get; set; }

    public string? ZusTxtInh6 { get; set; }

    public bool? MitHänger { get; set; }

    public bool? AvisSend { get; set; }

    public long? SpeditionsNr { get; set; }

    public long? Bstrecke { get; set; }

    public bool? LoeschKng { get; set; }

    public string? VvonrWe { get; set; }

    public decimal? FrachtPreis { get; set; }

    public bool? PalKlPck { get; set; }

    public string? FahrerName { get; set; }

    public bool? Abholung { get; set; }

    public bool? SpedSend { get; set; }

    public bool? Mischpalette { get; set; }

    public bool? Xlsexp { get; set; }

    public DateTime? LockLast { get; set; }

    public DateTime? LockStart { get; set; }

    public string? LockUser { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool? Angebot { get; set; }

    public DateTime? LadeDatum { get; set; }

    public string? LadeUhrzeit { get; set; }

    public bool? Satlog { get; set; }

    public string? FahrNam { get; set; }

    public string? NrtelefonWe { get; set; }

    public string? NrtelefonRe { get; set; }

    public bool? Wgskng { get; set; }

    public short? NuetzelSend { get; set; }

    public long? FrachtScheinNr { get; set; }

    public long? ElscheinNr { get; set; }

    public bool? MalzKng { get; set; }

    public long? ErstArtikel { get; set; }

    public string? ZahlZiel { get; set; }

    public string? PfadBitMap { get; set; }
    
    public string? WatcherStatus { get; set; }
}
