namespace Entities.Models;

public partial class Preislisten
{
    public long Prlnr { get; set; }

    public short? Prlkng { get; set; }

    public long PrlartNr { get; set; }

    public string? PrlartBez { get; set; }

    public string? PrlartBez2 { get; set; }

    public string? Prlbzg { get; set; }

    public decimal? PrlartPreis { get; set; }

    public decimal? Prlekpreis { get; set; }

    public decimal? PrlartProz { get; set; }

    public int Prlpos { get; set; }

    public decimal? Prlfracht { get; set; }

    public DateTime? Prldatum1 { get; set; }

    public DateTime? Prldatum2 { get; set; }

    public decimal? Prlpreis1 { get; set; }

    public decimal? Prlpreis2 { get; set; }

    public bool? Send { get; set; }

    public string SysUser { get; set; } = null!;

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
