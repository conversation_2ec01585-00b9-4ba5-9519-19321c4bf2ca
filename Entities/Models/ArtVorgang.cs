namespace Entities.Models;

public partial class ArtVorgang
{
    public DateTime? Datum { get; set; }

    public long? ArtikelNr { get; set; }

    public long? VorgNr { get; set; }

    public string? VgArt { get; set; }

    public decimal? Menge { get; set; }

    public long? ScheinNr { get; set; }

    public long? LscheinNr { get; set; }

    public int? FilialNr { get; set; }

    public bool? Abschl { get; set; }

    public short? <PERSON>gerstelle { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public decimal? Stueck { get; set; }

    public decimal? Bstueck { get; set; }

    public decimal? Bmenge { get; set; }
}
