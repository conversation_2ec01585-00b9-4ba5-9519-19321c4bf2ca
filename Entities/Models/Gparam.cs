namespace Entities.Models;

public partial class Gparam
{
    public short? PosNr { get; set; }

    public string? Bezeichnung { get; set; }

    public string? Kennung { get; set; }

    public string? Wgs { get; set; }

    public long? ArtikelNr { get; set; }

    public bool? Abrgesp { get; set; }

    public short? Konto1 { get; set; }

    public short? Konto2 { get; set; }

    public short? KontoHandel { get; set; }

    public short? Konto3 { get; set; }

    public string? Wgskennung1 { get; set; }

    public string? Wgskennung2 { get; set; }

    public string? Wgskennung3 { get; set; }

    public string? Wgskennung4 { get; set; }

    public string? Wgskennung5 { get; set; }

    public string? Wgskennung6 { get; set; }

    public string? Wgskennung7 { get; set; }

    public string? Wgskennung8 { get; set; }

    public string? Wgskennung9 { get; set; }

    public string? Wgskennung10 { get; set; }

    public string? Wgskennung11 { get; set; }

    public string? Wgskennung12 { get; set; }

    public string? WgsBezeichnung1 { get; set; }

    public string? WgsEinh1 { get; set; }

    public string? WgsBezeichnung2 { get; set; }

    public string? WgsEinh2 { get; set; }

    public string? WgsBezeichnung3 { get; set; }

    public string? WgsEinh3 { get; set; }

    public string? WgsBezeichnung4 { get; set; }

    public string? WgsEinh4 { get; set; }

    public string? WgsBezeichnung5 { get; set; }

    public string? WgsEinh5 { get; set; }

    public string? WgsBezeichnung6 { get; set; }

    public string? WgsEinh6 { get; set; }

    public string? WgsBezeichnung7 { get; set; }

    public string? WgsEinh7 { get; set; }

    public string? WgsBezeichnung8 { get; set; }

    public string? WgsEinh8 { get; set; }

    public string? WgsBezeichnung9 { get; set; }

    public string? WgsEinh9 { get; set; }

    public string? WgsBezeichnung10 { get; set; }

    public string? WgsEinh10 { get; set; }

    public string? WgsBezeichnung11 { get; set; }

    public string? WgsEinh11 { get; set; }

    public string? WgsBezeichnung12 { get; set; }

    public string? WgsEinh12 { get; set; }

    public long Id { get; set; }
}
