namespace Entities.Models;

public partial class ArtKtoE
{
    public int? Akposnr { get; set; }

    public long? Akartnr { get; set; }

    public DateTime? Akdatum { get; set; }

    public long? Akktnr { get; set; }

    public long? Akblgnr { get; set; }

    public int? Akktonr { get; set; }

    public string? Akgrund { get; set; }

    public double? Akbmge { get; set; }

    public double? Aklgew { get; set; }

    public decimal? Akbp { get; set; }

    public double? Akzug { get; set; }

    public double? Akab { get; set; }

    public decimal? Akzn { get; set; }

    public decimal? Akan { get; set; }

    public short? Aklgst { get; set; }

    public string? Akper { get; set; }

    public string? Akkng { get; set; }

    public long? AkurBlgnr { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public decimal Akbstck { get; set; }

    public decimal Aklstck { get; set; }

    public decimal AkzuStck { get; set; }

    public decimal AkabStck { get; set; }
}
