namespace Entities.Models;

public partial class Aradressdaten
{
    public long? Arnummer { get; set; }

    public DateTime? Ardatum { get; set; }

    public long? ArkdnrRe { get; set; }

    public string? Arname1Re { get; set; }

    public string? ArstrasseRe { get; set; }

    public string? ArustIdNrRe { get; set; }

    public string? Arplzre { get; set; }

    public string? ArortRe { get; set; }

    public long? ArkdnrWe { get; set; }

    public string? Arname1We { get; set; }

    public string? ArstrasseWe { get; set; }

    public string? Arplzwe { get; set; }

    public string? ArortWe { get; set; }

    public double? ArgesMenge { get; set; }

    public decimal? Arnetto1 { get; set; }

    public decimal? Arnetto2 { get; set; }

    public decimal? Arnetto3 { get; set; }

    public decimal? ArgesNetto { get; set; }

    public decimal? Armwst1 { get; set; }

    public decimal? Armwst2 { get; set; }

    public decimal? Armwst3 { get; set; }

    public decimal? ArgesMwst { get; set; }

    public decimal? ArgesBrutto1 { get; set; }

    public decimal? ArgesBrutto2 { get; set; }

    public decimal? ArgesBrutto3 { get; set; }

    public decimal? ArgesBrutto { get; set; }

    public decimal? ArmwSt1Proz { get; set; }

    public decimal? ArmwSt2Proz { get; set; }

    public decimal? ArmwSt3Proz { get; set; }

    public decimal? Arskt1Betrag { get; set; }

    public decimal? Arskt2Betrag { get; set; }

    public decimal? Arskt3Betrag { get; set; }

    public decimal? Arskto1P { get; set; }

    public short? Arskto1T { get; set; }

    public decimal? Arskto2P { get; set; }

    public short? Arskto2T { get; set; }

    public short? ArnettoTage { get; set; }

    public decimal? ArgesRabatt { get; set; }

    public string? Arwhrg { get; set; }

    public decimal? ArwhrgKurs { get; set; }

    public bool? ArstatusRechnung { get; set; }

    public bool? ArstatusGutschrift { get; set; }

    public bool? ArstatusStorno { get; set; }

    public bool? Arwashout { get; set; }

    public bool? Arkorrektur { get; set; }

    public bool? WertGut { get; set; }

    public bool? Arfibu2 { get; set; }

    public bool? Send { get; set; }

    public bool? Edifact { get; set; }

    public bool? Factoring { get; set; }

    public bool? Factoringv2 { get; set; }

    public string? ArstornoKng { get; set; }

    public long? AraltNr { get; set; }

    public long? ArstreckenNr { get; set; }

    public string? Arbearbeiter { get; set; }

    public string? ArbestellNr { get; set; }

    public string? Aruhrzeit { get; set; }

    public string? UhrzeitTxt { get; set; }

    public string? Arkdsbgre { get; set; }

    public string? AranredeRe { get; set; }

    public string? Arname2Re { get; set; }

    public string? Arname3Re { get; set; }

    public short? Arbldre { get; set; }

    public string? ArlandRe { get; set; }

    public string? IlnnrRe { get; set; }

    public string? Arkdsbgwe { get; set; }

    public string? AranredeWe { get; set; }

    public string? Arname2We { get; set; }

    public string? Arname3We { get; set; }

    public short? Arbldwe { get; set; }

    public string? ArlandWe { get; set; }

    public string? IlnnrWe { get; set; }

    public string? VvonrWe { get; set; }

    public long? ArregioNr { get; set; }

    public string? ArregioName { get; set; }

    public string? ArregioName2 { get; set; }

    public string? ArregioStrasse { get; set; }

    public string? ArregioPlz { get; set; }

    public string? ArregioOrt { get; set; }

    public string? ArregioStatus { get; set; }

    public string? ArregIln { get; set; }

    public long? ArvertrNr { get; set; }

    public string? ArvertrName { get; set; }

    public string? ArvertrOrt { get; set; }

    public string? ArzahlZiel { get; set; }

    public short? Arbez { get; set; }

    public string? Arbank { get; set; }

    public string? Arblz { get; set; }

    public string? ArkontoNr { get; set; }

    public string? ArzhlDruck { get; set; }

    public string? ArzhlDruck02 { get; set; }

    public string? ArzhlDruck03 { get; set; }

    public decimal? ArkdgrabattP { get; set; }

    public decimal? ArkdgrabattD { get; set; }

    public decimal? ArreBetrag { get; set; }

    public short? Arasld { get; set; }

    public string? Argblnr { get; set; }

    public string? ArkdlsEz { get; set; }

    public long? ArkdnrCl { get; set; }

    public string? ArfbzgKng { get; set; }

    public string? Arlkw { get; set; }

    public double? Skontosatz { get; set; }

    public DateTime? Vdatum { get; set; }

    public string? Vdok { get; set; }

    public short? ArbankZuOrdn { get; set; }

    public decimal? Opre { get; set; }

    public int? FilialNr { get; set; }

    public string? ArpfachRe { get; set; }

    public string? ArpfachWe { get; set; }

    public long? KdliefNr { get; set; }

    public string? StornoBemerkung { get; set; }

    public int? MandatNr { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? ArlfnrWe { get; set; }

    public bool? Bezahlt { get; set; }

    public DateTime? LockLast { get; set; }

    public DateTime? LockStart { get; set; }

    public string? LockUser { get; set; }

    public bool? Freigabe { get; set; }

    public bool? DruckKz { get; set; }

    public bool IsCancellationInvoice => ArstornoKng?.Equals("S") ?? false;
    public bool WasCanceled => ArstornoKng?.Equals("Y") ?? false;
    public bool IsInvoiceHolderForeigner => !ArlandRe?.Equals("Deutschland") ?? false;
}
