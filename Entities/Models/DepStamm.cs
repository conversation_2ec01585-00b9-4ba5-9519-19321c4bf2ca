namespace Entities.Models;

public partial class DepStamm
{
    public int? DepNr { get; set; }

    public string? Bezeichng { get; set; }

    public DateTime? Datum { get; set; }

    public long? KundNr { get; set; }

    public long? ArtNr { get; set; }

    public long? DpartFaktor { get; set; }

    public short? DpartFaktorwert { get; set; }

    public decimal? Dphlgewicht { get; set; }

    public string? DpmskNr { get; set; }

    public decimal? DpelgKosten { get; set; }

    public decimal? DpalgKosten { get; set; }

    public decimal? DpdlgKosten { get; set; }

    public string? Bemerkungen { get; set; }

    public decimal? KlmengZuschlEing { get; set; }

    public decimal? KlmengZuschlAus { get; set; }

    public decimal? KlmengEurEin { get; set; }

    public decimal? KlmengEurAus { get; set; }

    public decimal? KammVerwEin { get; set; }

    public decimal? KammVerwAus { get; set; }

    public DateTime? DirektUmschlagVon { get; set; }

    public DateTime? DirektUmschlagBis { get; set; }

    public bool? GetrKng { get; set; }

    public string? Kdsbg { get; set; }

    public string? Wbtext { get; set; }

    public long? FreistNr { get; set; }

    public int? DplfdNr { get; set; }

    public decimal? SaldoAlt { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
