using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Entities.Models;

[Keyless]
[Table("VBF_to_VAS")]
public partial class VBF_to_VA
{
    [Column("Warenbewegung-ID")]
    public int? Warenbewegung_ID { get; set; }

    [Column("Warenbewegung-Typ")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Warenbewegung_Typ { get; set; }

    [Column("Warenbewegung-Linie")]
    public int? Warenbewegung_Linie { get; set; }

    [Column("Datum Wägung 1", TypeName = "datetime")]
    public DateTime? Datum_Wägung_1 { get; set; }

    [Column("Gewicht Wägung 1")]
    public double? Gewicht_Wägung_1 { get; set; }

    [Column("Datum Wägung 2", TypeName = "datetime")]
    public DateTime? Datum_Wägung_2 { get; set; }

    [Column("Gewicht Wägung 2")]
    public double? Gewicht_Wägung_2 { get; set; }

    [Column("Lagerstätte 1")]
    public int? Lagerstätte_1 { get; set; }

    [Column("Menge 1 kg")]
    public double? Menge_1_kg { get; set; }

    [Column("Lagerstätte 2")]
    public int? Lagerstätte_2 { get; set; }

    [Column("Menge 2 kg")]
    public double? Menge_2_kg { get; set; }

    [Column("Lagerstätte 3")]
    public int? Lagerstätte_3 { get; set; }

    [Column("Menge 3 kg")]
    public double? Menge_3_kg { get; set; }

    [Column("Artikel W.-Grp.")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_W__Grp_ { get; set; }

    [Column("Artikel Nummer")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_Nummer { get; set; }

    [Column("Artikel Bezeichnung")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_Bezeichnung { get; set; }

    [Column("Kunden-Nr.")]
    public int? Kunden_Nr_ { get; set; }

    [Column("Kunden-Name 1")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Name_1 { get; set; }

    [Column("Kunden-Name 2")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Name_2 { get; set; }

    [Column("Kunden-Straße")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Straße { get; set; }

    [Column("Kunden-PLZ")]
    [StringLength(10)]
    [Unicode(false)]
    public string? Kunden_PLZ { get; set; }

    [Column("Kunden-Ort")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Ort { get; set; }

    [Column("Spedition-Nr.")]
    public int? Spedition_Nr_ { get; set; }

    [Column("Spedition-Name 1")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Spedition_Name_1 { get; set; }

    [Column("Spedition-Name 2")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Spedition_Name_2 { get; set; }

    [Column("Spedition-Straße")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Spedition_Straße { get; set; }

    [Column("Spedition-PLZ")]
    [StringLength(10)]
    [Unicode(false)]
    public string? Spedition_PLZ { get; set; }

    [Column("Spedition-Ort")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Spedition_Ort { get; set; }

    [StringLength(255)]
    [Unicode(false)]
    public string? Kommentar { get; set; }

    [Column("Partie-Nr.")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Partie_Nr_ { get; set; }

    [StringLength(255)]
    [Unicode(false)]
    public string? Entladestelle { get; set; }

    [Column("LKW-Kennzeichen")]
    [StringLength(50)]
    [Unicode(false)]
    public string? LKW_Kennzeichen { get; set; }

    [Column("Anhänger-Kennzeichen")]
    [StringLength(50)]
    [Unicode(false)]
    public string? Anhänger_Kennzeichen { get; set; }

    [Column("LKW-Fahrer")]
    [StringLength(255)]
    [Unicode(false)]
    public string? LKW_Fahrer { get; set; }

    [Column("VBF-Bestell-ID")]
    public int? VBF_Bestell_ID { get; set; }

    [Column("VAS-Bestell-Referenz")]
    [StringLength(255)]
    [Unicode(false)]
    public string? VAS_Bestell_Referenz { get; set; }
}
