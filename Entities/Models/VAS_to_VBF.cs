using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Entities.Models;

[Keyless]
[Table("VAS_to_VBF")]
public partial class VAS_to_VBF
{
    [Column("VAS-Bestell-Referenz")]
    [StringLength(255)]
    [Unicode(false)]
    public string? VAS_Bestell_Referenz { get; set; }

    [Column("Kunden-Nr.")]
    public int? Kunden_Nr_ { get; set; }

    [Column("Kunden-Name 1")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Name_1 { get; set; }

    [Column("Kunden-Name 2")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Name_2 { get; set; }

    [Column("Kunden-Straße")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Straße { get; set; }

    [Column("Kunden-PLZ")]
    [StringLength(10)]
    [Unicode(false)]
    public string? Kunden_PLZ { get; set; }

    [Column("Kunden-Ort")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Ort { get; set; }

    [Column("Kunden-Entladestelle")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kunden_Entladestelle { get; set; }

    [Column("Sollmenge kg")]
    public double? Sollmenge_kg { get; set; }

    [Column("Artikel W.-Grp.")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_W__Grp_ { get; set; }

    [Column("Artikel Nummer")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_Nummer { get; set; }

    [Column("Artikel Bezeichnung")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Artikel_Bezeichnung { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? Ladedatum { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? Lieferdatum { get; set; }

    [Column("Kommentar Lieferschein")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kommentar_Lieferschein { get; set; }

    [Column("Kommentar Intern")]
    [StringLength(255)]
    [Unicode(false)]
    public string? Kommentar_Intern { get; set; }
}
