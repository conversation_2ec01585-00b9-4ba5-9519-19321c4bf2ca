namespace Entities.Models;

public partial class ArtKtoAbschl
{
    public string? Aka<PERSON>per { get; set; }

    public long AkabartNr { get; set; }

    public double? AkabaltKg { get; set; }

    public double? AkabaktKg { get; set; }

    public decimal? ArtKtoAbschl1 { get; set; }

    public decimal? AkabneuWert { get; set; }

    public decimal? AkabaltDp { get; set; }

    public decimal? AkabneuDp { get; set; }

    public decimal? AkabaltWert { get; set; }

    public int AkabfilNr { get; set; }

    public long Id { get; set; }
}
