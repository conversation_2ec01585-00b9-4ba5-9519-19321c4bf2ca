namespace Entities.Models;

public partial class SiloFach
{
    public int? SiloNr { get; set; }

    public long? ArtNr { get; set; }

    public long? KdNr { get; set; }

    public double? RaumInh { get; set; }

    public decimal? Hlgewicht { get; set; }

    public double? MaxInhalt { get; set; }

    public int? Bauform { get; set; }

    public int? SiloArt { get; set; }

    public int? ProfilNr { get; set; }

    public string? ProfilText { get; set; }

    public string? ProfilArtNr { get; set; }

    public string? SiloBez { get; set; }

    public DateTime? Datum { get; set; }

    public string? Bem { get; set; }

    public int? DepotNr1 { get; set; }

    public int? DepotNr2 { get; set; }

    public int? DepotNr3 { get; set; }

    public int? DepotNr4 { get; set; }

    public int? DepotNr5 { get; set; }

    public int? DepotNr6 { get; set; }

    public string? Reinigung { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public bool? Gesperrt { get; set; }
}
