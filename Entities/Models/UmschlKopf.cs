namespace Entities.Models;

public partial class UmschlKopf
{
    public long DepNr { get; set; }

    public string? Bezeichnung { get; set; }

    public long? KundenNr { get; set; }

    public string? KundenName1 { get; set; }

    public string? KundenName2 { get; set; }

    public string? KundenStrasse { get; set; }

    public string? KundenPlz { get; set; }

    public string? KundenOrt { get; set; }

    public long? ArtikelNr { get; set; }

    public string? ArtikelBez { get; set; }

    public string? ArtikelBez1 { get; set; }

    public decimal? SaldoAlt { get; set; }

    public decimal? SaldoNeu { get; set; }

    public string? ChargNr { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
