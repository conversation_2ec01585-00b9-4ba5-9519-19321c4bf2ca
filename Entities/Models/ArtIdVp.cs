namespace Entities.Models;

public partial class ArtIdVp
{
    public long ArtIdNr { get; set; }

    public int ArtIdPos { get; set; }

    public long? ArtIdArtNr { get; set; }

    public string? ArtIdBez1 { get; set; }

    public string? ArtIdBez2 { get; set; }

    public decimal? ArtIdMenge { get; set; }

    public string? ArtIdVpe { get; set; }

    public string? ArtIdChrgNr { get; set; }

    public long Id { get; set; }
}
