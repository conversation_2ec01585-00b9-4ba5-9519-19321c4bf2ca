namespace Entities.Models;

public partial class Elspo
{
    public long? Auftragsnummer { get; set; }

    public string? Status { get; set; }

    public int? GridPos { get; set; }

    public string? Erfassungsschema { get; set; }

    public long? ArtikelNummer { get; set; }

    public string? Bezeichnung { get; set; }

    public double? Anzahl { get; set; }

    public string? BezGr { get; set; }

    public decimal? GesStck { get; set; }

    public decimal? GesMenge { get; set; }

    public decimal? Epreis { get; set; }

    public decimal? Gpreis { get; set; }

    public decimal? MwSt { get; set; }

    public string? Faktor { get; set; }

    public int? MwStSchl { get; set; }

    public long? VerpckNr { get; set; }

    public long? ArtikelNummer2 { get; set; }

    public string? Selektionskennung { get; set; }

    public decimal? AlteMenge { get; set; }

    public long? KontraktNr { get; set; }

    public long? StreckenNr { get; set; }

    public short? EkaKu { get; set; }

    public DateTime? Mhd { get; set; }

    public string? ChargenNr { get; set; }

    public short? LagerNr { get; set; }

    public string? Nvonr { get; set; }

    public bool? Nvo { get; set; }

    public string? NvobilanzNr { get; set; }

    public bool? Nvobilanziert { get; set; }

    public long? Alsnummer { get; set; }

    public long? Alsartikel { get; set; }

    public decimal? Alsmenge { get; set; }

    public decimal? PakGewicht { get; set; }

    public long? KolliInh { get; set; }

    public long Id { get; set; }

    public string? BestNr { get; set; }

    public long? PktrNr { get; set; }
}
