namespace Entities.Models;

public partial class Umbpo
{
    public long? Ubnr { get; set; }

    public int? PosNr { get; set; }

    public long? ArtNr { get; set; }

    public string? Bez1 { get; set; }

    public string? Bez2 { get; set; }

    public string? BzgGr { get; set; }

    public string? Pm { get; set; }

    public decimal? Menge { get; set; }

    public decimal? Preis { get; set; }

    public string? Grund { get; set; }

    public int? LagerNr { get; set; }

    public string? ChargenNr { get; set; }

    public string? ProdNr { get; set; }

    public string? Keng { get; set; }

    public int? SiloNr { get; set; }

    public long Id { get; set; }

    public decimal? Stueck { get; set; }
}
