namespace Entities.Models;

public partial class Rgbvhpt
{
    public long? Gbnr { get; set; }

    public long? Gblfnr { get; set; }

    public DateTime? Gbdatum { get; set; }

    public string? GbgetrArt { get; set; }

    public decimal? Gbep { get; set; }

    public decimal? GbgesNetto { get; set; }

    public string? GbgetrKng { get; set; }

    public long? GbwgsNr { get; set; }

    public long? GbartNr { get; set; }

    public decimal? Gbfracht { get; set; }

    public long? GbkontrNr { get; set; }

    public string? Gbfiliale { get; set; }

    public string? GbstreckNr { get; set; }

    public string? Gbkonto { get; set; }

    public string? Gbkz { get; set; }

    public string? Gblkw { get; set; }

    public int? GbgridPos { get; set; }

    public long? Gbhartikel { get; set; }

    public string? Gbvznr { get; set; }

    public string? WgStrekNr { get; set; }

    public DateTime? MaabrDatum { get; set; }

    public double? GbstProz { get; set; }

    public bool Lkw { get; set; }

    public long? Kostentraeger { get; set; }

    public long? Kostenstelle { get; set; }

    public bool Tp { get; set; }

    public bool Nvo { get; set; }

    public decimal? BuchMenge { get; set; }

    public int? FilialNr { get; set; }

    public long Id { get; set; }
}
