namespace Entities.Models;

public partial class Agbvhpt
{
    public long? Gbnr { get; set; }

    public short? GbgridPos { get; set; }

    public string? ErfSchema { get; set; }

    public string? Gbtext { get; set; }

    public decimal? Gblbwert { get; set; }

    public string? Gblbeh { get; set; }

    public decimal? GbanlGew { get; set; }

    public decimal? GbabzGew { get; set; }

    public decimal? GbabrGew { get; set; }

    public decimal? Gbep { get; set; }

    public decimal? GbgesNetto { get; set; }

    public long? GbartNr { get; set; }

    public double? GbstProz { get; set; }

    public string? GbgetrKng { get; set; }

    public long? GbwgsNr { get; set; }

    public decimal? Gbfracht { get; set; }

    public long? GbkontrNr { get; set; }

    public long? GbstreckNr { get; set; }

    public string? Gbkonto { get; set; }

    public string? Gbkz { get; set; }

    public string? Gblkw { get; set; }

    public long? G<PERSON>rtikel { get; set; }

    public long? Gbvznr { get; set; }

    public bool? Lkw { get; set; }

    public long? Kostentraeger { get; set; }

    public long? Kostenstelle { get; set; }

    public bool? Tp { get; set; }

    public bool? Nvo { get; set; }

    public decimal? BuchMenge { get; set; }

    public string? Gbfiliale { get; set; }

    public long Id { get; set; }
}
