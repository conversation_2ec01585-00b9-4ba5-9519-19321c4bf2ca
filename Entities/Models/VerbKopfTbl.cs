using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Entities.Models;

[Table("VerbKopfTbl")]
public partial class VerbKopfTbl
{
    public long ERNummer { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ERDatum { get; set; }

    [StringLength(20)]
    public string? ERNrLf { get; set; }

    public long? LFNr { get; set; }

    public long? Artnr { get; set; }

    public bool? Send { get; set; }

    [StringLength(50)]
    public string? SysUser { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? SysTime { get; set; }

    [Key]
    public long ID { get; set; }
}
