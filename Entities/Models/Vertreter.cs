namespace Entities.Models;

public partial class Vertreter
{
    public long Vtnummer { get; set; }

    public string? Vtsbg { get; set; }

    public string? Vtanrede { get; set; }

    public string? Vtname1 { get; set; }

    public string? Vtname2 { get; set; }

    public string? Vtname3 { get; set; }

    public string? Vtstrasse { get; set; }

    public string? Vtplz { get; set; }

    public string? Vtort { get; set; }

    public string? Vtland { get; set; }

    public string? Vttelefon1 { get; set; }

    public string? Vttelefon2 { get; set; }

    public string? Vtfax { get; set; }

    public string? Vtmobil { get; set; }

    public string? Vtemail { get; set; }

    public short? VtstSchlüssel { get; set; }

    public decimal? VtmwSt { get; set; }

    public string? Vtberuf { get; set; }

    public string? Vtbundland { get; set; }

    public short? VtbldKn { get; set; }

    public short? VstNr { get; set; }

    public short? VtfilialNr { get; set; }

    public string? Vtblz { get; set; }

    public string? Vtkonto { get; set; }

    public string? Vtbank { get; set; }

    public string? Vtswift { get; set; }

    public string? Vtiban { get; set; }

    public string? Vtbemerk { get; set; }

    public bool? Send { get; set; }

    public bool? Vtsperr { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
