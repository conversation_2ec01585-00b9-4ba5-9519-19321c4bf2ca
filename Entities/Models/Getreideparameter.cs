namespace Entities.Models;

public partial class Getreideparameter
{
    public short? PosNr { get; set; }

    public string? Bezeichnung { get; set; }

    public string? Kennung { get; set; }

    public string? Wgs { get; set; }

    public string? Tk { get; set; }

    public string? Ft { get; set; }

    public decimal? Preis { get; set; }

    public int? ArtikelNr { get; set; }

    public string? Abrgesp { get; set; }

    public short? Konto1 { get; set; }

    public short? Konto2 { get; set; }

    public short? KontoHandel { get; set; }

    public short? Konto3 { get; set; }

    public string? Laborpos1 { get; set; }

    public string? Laborpos2 { get; set; }

    public string? Laborpos3 { get; set; }

    public string? Laborpos4 { get; set; }

    public string? Laborpos5 { get; set; }

    public string? Laborpos6 { get; set; }

    public string? Laborpos7 { get; set; }

    public string? Laborpos8 { get; set; }

    public string? Laborpos9 { get; set; }

    public string? Laborpos10 { get; set; }

    public string? Laborpos11 { get; set; }

    public string? Laborpos12 { get; set; }

    public string? WgsBezeichnung1 { get; set; }

    public string? WgsEinh1 { get; set; }

    public string? WgsBezeichnung2 { get; set; }

    public string? WgsEinh2 { get; set; }

    public string? WgsBezeichnung3 { get; set; }

    public string? WgsEinh3 { get; set; }

    public string? WgsBezeichnung4 { get; set; }

    public string? WgsEinh4 { get; set; }

    public string? WgsBezeichnung5 { get; set; }

    public string? WgsEinh5 { get; set; }

    public string? WgsBezeichnung6 { get; set; }

    public string? WgsEinh6 { get; set; }

    public string? WgsBezeichnung7 { get; set; }

    public string? WgsEinh7 { get; set; }

    public string? WgsBezeichnung8 { get; set; }

    public string? WgsEinh8 { get; set; }

    public string? WgsBezeichnung9 { get; set; }

    public string? WgsEinh9 { get; set; }

    public string? WgsBezeichnung10 { get; set; }

    public string? WgsEinh10 { get; set; }

    public string? WgsBezeichnung11 { get; set; }

    public string? WgsEinh11 { get; set; }

    public string? WgsBezeichnung12 { get; set; }

    public string? WgsEinh12 { get; set; }

    public decimal? Faktor1 { get; set; }

    public decimal? Faktor2 { get; set; }

    public decimal? Faktor3 { get; set; }

    public decimal? Faktor4 { get; set; }

    public decimal? Faktor5 { get; set; }

    public decimal? Faktor6 { get; set; }

    public decimal? Faktor7 { get; set; }

    public decimal? Faktor8 { get; set; }

    public decimal? Faktor9 { get; set; }

    public decimal? Faktor10 { get; set; }

    public decimal? Faktor11 { get; set; }

    public decimal? Faktor12 { get; set; }

    public decimal? Faktor13 { get; set; }

    public decimal? Faktor14 { get; set; }

    public decimal? Faktor15 { get; set; }

    public decimal? Faktor16 { get; set; }

    public decimal? Faktor17 { get; set; }

    public decimal? Faktor18 { get; set; }

    public decimal? Faktor19 { get; set; }

    public decimal? Faktor20 { get; set; }

    public decimal? Faktor21 { get; set; }

    public decimal? Faktor22 { get; set; }

    public decimal? Faktor23 { get; set; }

    public decimal? Faktor24 { get; set; }

    public decimal? Faktor25 { get; set; }

    public decimal? Faktor26 { get; set; }

    public decimal? Faktor27 { get; set; }

    public decimal? Faktor28 { get; set; }

    public decimal? Faktor29 { get; set; }

    public decimal? Faktor30 { get; set; }

    public decimal? Faktor31 { get; set; }

    public decimal? Faktor32 { get; set; }

    public decimal? Faktor33 { get; set; }

    public decimal? Faktor34 { get; set; }

    public decimal? Faktor35 { get; set; }

    public decimal? Faktor36 { get; set; }

    public string? Ntk { get; set; }

    public string? Nft { get; set; }

    public decimal? Npreis { get; set; }

    public string? Nlaborpos1 { get; set; }

    public string? Nlaborpos2 { get; set; }

    public string? Nlaborpos3 { get; set; }

    public string? Nlaborpos4 { get; set; }

    public string? Nlaborpos5 { get; set; }

    public string? Nlaborpos6 { get; set; }

    public string? Nlaborpos7 { get; set; }

    public string? Nlaborpos8 { get; set; }

    public string? Nlaborpos9 { get; set; }

    public string? Nlaborpos10 { get; set; }

    public string? Nlaborpos11 { get; set; }

    public string? Nlaborpos12 { get; set; }

    public decimal? Nfaktor1 { get; set; }

    public decimal? Nfaktor2 { get; set; }

    public decimal? Nfaktor3 { get; set; }

    public decimal? Nfaktor4 { get; set; }

    public decimal? Nfaktor5 { get; set; }

    public decimal? Nfaktor6 { get; set; }

    public decimal? Nfaktor7 { get; set; }

    public decimal? Nfaktor8 { get; set; }

    public decimal? Nfaktor9 { get; set; }

    public decimal? Nfaktor10 { get; set; }

    public decimal? Nfaktor11 { get; set; }

    public decimal? Nfaktor12 { get; set; }

    public decimal? Nfaktor13 { get; set; }

    public decimal? Nfaktor14 { get; set; }

    public decimal? Nfaktor15 { get; set; }

    public decimal? Nfaktor16 { get; set; }

    public decimal? Nfaktor17 { get; set; }

    public decimal? Nfaktor18 { get; set; }

    public decimal? Nfaktor19 { get; set; }

    public decimal? Nfaktor20 { get; set; }

    public decimal? Nfaktor21 { get; set; }

    public decimal? Nfaktor22 { get; set; }

    public decimal? Nfaktor23 { get; set; }

    public decimal? Nfaktor24 { get; set; }

    public decimal? Nfaktor25 { get; set; }

    public decimal? Nfaktor26 { get; set; }

    public decimal? Nfaktor27 { get; set; }

    public decimal? Nfaktor28 { get; set; }

    public decimal? Nfaktor29 { get; set; }

    public decimal? Nfaktor30 { get; set; }

    public decimal? Nfaktor31 { get; set; }

    public decimal? Nfaktor32 { get; set; }

    public decimal? Nfaktor33 { get; set; }

    public decimal? Nfaktor34 { get; set; }

    public decimal? Nfaktor35 { get; set; }

    public decimal? Nfaktor36 { get; set; }

    public string? Vtk { get; set; }

    public string? Vft { get; set; }

    public decimal? Vpreis { get; set; }

    public string? Vlaborpos1 { get; set; }

    public string? Vlaborpos2 { get; set; }

    public string? Vlaborpos3 { get; set; }

    public string? Vlaborpos4 { get; set; }

    public string? Vlaborpos5 { get; set; }

    public string? Vlaborpos6 { get; set; }

    public string? Vlaborpos7 { get; set; }

    public string? Vlaborpos8 { get; set; }

    public string? Vlaborpos9 { get; set; }

    public string? Vlaborpos10 { get; set; }

    public string? Vlaborpos11 { get; set; }

    public string? Vlaborpos12 { get; set; }

    public decimal? Vfaktor1 { get; set; }

    public decimal? Vfaktor2 { get; set; }

    public decimal? Vfaktor3 { get; set; }

    public decimal? Vfaktor4 { get; set; }

    public decimal? Vfaktor5 { get; set; }

    public decimal? Vfaktor6 { get; set; }

    public decimal? Vfaktor7 { get; set; }

    public decimal? Vfaktor8 { get; set; }

    public decimal? Vfaktor9 { get; set; }

    public decimal? Vfaktor10 { get; set; }

    public decimal? Vfaktor11 { get; set; }

    public decimal? Vfaktor12 { get; set; }

    public decimal? Vfaktor13 { get; set; }

    public decimal? Vfaktor14 { get; set; }

    public decimal? Vfaktor15 { get; set; }

    public decimal? Vfaktor16 { get; set; }

    public decimal? Vfaktor17 { get; set; }

    public decimal? Vfaktor18 { get; set; }

    public decimal? Vfaktor19 { get; set; }

    public decimal? Vfaktor20 { get; set; }

    public decimal? Vfaktor21 { get; set; }

    public decimal? Vfaktor22 { get; set; }

    public decimal? Vfaktor23 { get; set; }

    public decimal? Vfaktor24 { get; set; }

    public decimal? Vfaktor25 { get; set; }

    public decimal? Vfaktor26 { get; set; }

    public decimal? Vfaktor27 { get; set; }

    public decimal? Vfaktor28 { get; set; }

    public decimal? Vfaktor29 { get; set; }

    public decimal? Vfaktor30 { get; set; }

    public decimal? Vfaktor31 { get; set; }

    public decimal? Vfaktor32 { get; set; }

    public decimal? Vfaktor33 { get; set; }

    public decimal? Vfaktor34 { get; set; }

    public decimal? Vfaktor35 { get; set; }

    public decimal? Vfaktor36 { get; set; }

    public string? Wgskennung1 { get; set; }

    public string? Wgskennung2 { get; set; }

    public string? Wgskennung3 { get; set; }

    public string? Wgskennung4 { get; set; }

    public string? Wgskennung5 { get; set; }

    public string? Wgskennung6 { get; set; }

    public string? Wgskennung7 { get; set; }

    public string? Wgskennung8 { get; set; }

    public string? Wgskennung9 { get; set; }

    public string? Wgskennung10 { get; set; }

    public string? Wgskennung11 { get; set; }

    public string? Wgskennung12 { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }
}
