namespace Entities.Common;

public record PostingsSchema
{
    public const string PoString = "PO";
    public const string TeString = "TE";
    
    public static readonly PostingsSchema Po = new(PoString);
    public static readonly PostingsSchema Te = new(TeString);
    

    public PostingsSchema(string value)
    {
        Value = value;
    }

    public string Value { get; }

    public bool IsPo => Value == PoString;
    public bool IsText => Value == TeString;
}