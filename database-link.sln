
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application", "Application\Application.csproj", "{CEEB4799-97B0-41D0-ADBC-FA7914738569}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Contracts", "Contracts\Contracts.csproj", "{4382630A-12C2-4077-80A6-12B7EA67AEFF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggerService", "LoggerService\LoggerService.csproj", "{F7A96BEF-2EDB-427D-ACC3-116406FF9CDD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Entities", "Entities\Entities.csproj", "{5AABB74A-47DF-446D-A87A-B3F02F0CFD5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Repository", "Repository\Repository.csproj", "{F3AF03F4-B3B9-4265-959E-4BBAB6A85ABC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Service.Contracts", "Service.Contracts\Service.Contracts.csproj", "{197B77BA-6678-4BFF-AC3C-99B314BF16CF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Service", "Service\Service.csproj", "{3414A9CB-7617-4BA8-A5E2-4F17E2A832F9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WinGng.Presentation", "WinGng.Presentation\WinGng.Presentation.csproj", "{2090F418-7022-4C06-A2B2-C9490F1E8164}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared", "Shared\Shared.csproj", "{F6F20508-56AE-4897-BB6E-94992D11C26C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DatabaseSync", "DatabaseSync\DatabaseSync.csproj", "{B6135CF0-973A-4D73-A956-2487C2BCF790}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{58A5FF81-DE1C-4A33-9AAE-84EC26677D1C}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		docker-compose.windows.yml = docker-compose.windows.yml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CEEB4799-97B0-41D0-ADBC-FA7914738569}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEEB4799-97B0-41D0-ADBC-FA7914738569}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEEB4799-97B0-41D0-ADBC-FA7914738569}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEEB4799-97B0-41D0-ADBC-FA7914738569}.Release|Any CPU.Build.0 = Release|Any CPU
		{4382630A-12C2-4077-80A6-12B7EA67AEFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4382630A-12C2-4077-80A6-12B7EA67AEFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4382630A-12C2-4077-80A6-12B7EA67AEFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4382630A-12C2-4077-80A6-12B7EA67AEFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7A96BEF-2EDB-427D-ACC3-116406FF9CDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7A96BEF-2EDB-427D-ACC3-116406FF9CDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7A96BEF-2EDB-427D-ACC3-116406FF9CDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7A96BEF-2EDB-427D-ACC3-116406FF9CDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{5AABB74A-47DF-446D-A87A-B3F02F0CFD5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5AABB74A-47DF-446D-A87A-B3F02F0CFD5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5AABB74A-47DF-446D-A87A-B3F02F0CFD5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5AABB74A-47DF-446D-A87A-B3F02F0CFD5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3AF03F4-B3B9-4265-959E-4BBAB6A85ABC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3AF03F4-B3B9-4265-959E-4BBAB6A85ABC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3AF03F4-B3B9-4265-959E-4BBAB6A85ABC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3AF03F4-B3B9-4265-959E-4BBAB6A85ABC}.Release|Any CPU.Build.0 = Release|Any CPU
		{197B77BA-6678-4BFF-AC3C-99B314BF16CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{197B77BA-6678-4BFF-AC3C-99B314BF16CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{197B77BA-6678-4BFF-AC3C-99B314BF16CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{197B77BA-6678-4BFF-AC3C-99B314BF16CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3414A9CB-7617-4BA8-A5E2-4F17E2A832F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3414A9CB-7617-4BA8-A5E2-4F17E2A832F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3414A9CB-7617-4BA8-A5E2-4F17E2A832F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3414A9CB-7617-4BA8-A5E2-4F17E2A832F9}.Release|Any CPU.Build.0 = Release|Any CPU
		{2090F418-7022-4C06-A2B2-C9490F1E8164}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2090F418-7022-4C06-A2B2-C9490F1E8164}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2090F418-7022-4C06-A2B2-C9490F1E8164}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2090F418-7022-4C06-A2B2-C9490F1E8164}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6F20508-56AE-4897-BB6E-94992D11C26C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6F20508-56AE-4897-BB6E-94992D11C26C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6F20508-56AE-4897-BB6E-94992D11C26C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6F20508-56AE-4897-BB6E-94992D11C26C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6135CF0-973A-4D73-A956-2487C2BCF790}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6135CF0-973A-4D73-A956-2487C2BCF790}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6135CF0-973A-4D73-A956-2487C2BCF790}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6135CF0-973A-4D73-A956-2487C2BCF790}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
