namespace Service.Contracts;

public interface IServiceManager
{
    IAradressdatenService AradressdatenService { get; }
    IArhaupdateiService ArhauptdateiService { get; }
    IKundenService KundenService { get; }
    IAuftragskopfService AuftragskopfService { get; }
    IArtikelService ArtikelService { get; }
    IDatevExportService DatevExportService { get; }
    IKontraktService KontraktService { get; }
    IAuftragsposService AuftragsposService { get; }
    ITriggerLogService TriggerLogService { get; }
}