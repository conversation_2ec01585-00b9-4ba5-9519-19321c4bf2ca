using Entities.Models;
using Shared.DataTransferObjects;

namespace Service.Contracts;

public interface IAuftragskopfService
{
    void WriteOrderToDb(bool trackChanges, Auftragskopf order);

    IEnumerable<Auftragskopf>? GetAuftragskopfById(int orderId, bool trackChanges);
    
    List<long?> GetWatchedLieferscheine(bool trackChanges);

    void DisableWatcher(List<long?> watchList);
    
    string GetOrderIdByAuftragsnummer(long auftragsnummer, bool trackChanges);
    
    long? GetMaxAuftragsnummer();
}