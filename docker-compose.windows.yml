services:
  application:
    image: database-link:latest
    build:
      context: .
      dockerfile: Application/Dockerfile.windows
    container_name: nuetzel-database-link
    environment:
        - ASPNETCORE_ENVIRONMENT=Docker
        - ConnectionStrings__DefaultConnection=Data Source=localhost:1433;Initial Catalog=Nuetzel;TrustServerCertificate=True;User=vas;Password=vas
        #For SQL Server: - ConnectionStrings__DefaultConnection=Data Source=VM-VAS\VASEXPRESS;Initial Catalog=VASDB01;TrustServerCertificate=True;User=vas;Password=vas      
      #Add environment for URL, sync user login data as soon as this is configurable with the application 