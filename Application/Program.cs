using Application;
using Application.Extensions;
using Contracts;
using Microsoft.AspNetCore.HttpOverrides;
using Serilog;
using DatabaseSync;
using DatabaseSync.Quartz;
using Quartz;
using Service;
using System.Runtime.InteropServices;

// Windows API imports for disabling Quick Edit mode
[DllImport("kernel32.dll")]
static extern IntPtr GetConsoleWindow();

[DllImport("kernel32.dll")]
static extern bool GetConsoleMode(IntPtr hConsoleHandle, out uint lpMode);

[DllImport("kernel32.dll")]
static extern bool SetConsoleMode(IntPtr hConsoleHandle, uint dwMode);

[DllImport("kernel32.dll", SetLastError = true)]
static extern IntPtr GetStdHandle(int nStdHandle);

const int STD_INPUT_HANDLE = -10;
const uint ENABLE_QUICK_EDIT_MODE = 0x0040;
const uint ENABLE_EXTENDED_FLAGS = 0x0080;

// Disable Windows Quick Edit mode
try
{
    IntPtr consoleHandle = GetStdHandle(STD_INPUT_HANDLE);
    if (consoleHandle != IntPtr.Zero)
    {
        if (GetConsoleMode(consoleHandle, out uint consoleMode))
        {
            consoleMode &= ~ENABLE_QUICK_EDIT_MODE;
            consoleMode |= ENABLE_EXTENDED_FLAGS;
            SetConsoleMode(consoleHandle, consoleMode);
            Console.WriteLine("Quick Edit mode disabled successfully.");
        }
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Failed to disable Quick Edit mode: {ex.Message}");
}

var builder = WebApplication.CreateBuilder(args);
builder.Configuration.AddEnvironmentVariables();

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.ConfigureCors();
builder.Services
    .AddControllers()
    .AddApplicationPart(typeof(WinGng.Presentation.AssemblyReference).Assembly);
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
    loggerConfiguration.ReadFrom.Configuration(hostingContext.Configuration));
builder.Services.ConfigureLoggerService();
builder.Services.ConfigureSqlContext(builder.Configuration);
builder.Services.ConfigureRepositoryManager();
builder.Services.ConfigureServiceManager();
builder.Services.AddAutoMapper(typeof(Program));
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

builder.Services.AddHostedService<QuartzHostedService>();
//print comment
Console.WriteLine("Adding Quartz");
// Quartz configuration
builder.Services.ConfigureQuartzServices();

builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseExceptionHandler(opt => { });

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
else if (app.Environment.IsProduction())
{
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.All
});

app.UseCors("CorsPolicy");

app.UseAuthorization();

app.MapControllers();

app.Run();
