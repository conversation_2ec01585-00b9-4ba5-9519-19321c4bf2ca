using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Repository;

namespace Application.ContextFactory;

public class RepositoryContextFactory : IDesignTimeDbContextFactory<RepositoryContext>
{
    public RepositoryContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json").AddUserSecrets(Assembly.GetExecutingAssembly()).Build();
        var builder =
            new DbContextOptionsBuilder<RepositoryContext>().UseSqlServer(
                configuration.GetConnectionString("sqlConnection"));
        return new RepositoryContext(builder.Options);
    }
}