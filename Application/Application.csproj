<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
      <UserSecretsId>e51f85e1-f3d0-446d-ac84-8fe54e7f90c0</UserSecretsId>
      <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Quartz" Version="3.9.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DatabaseSync\DatabaseSync.csproj" />
      <ProjectReference Include="..\LoggerService\LoggerService.csproj" />
      <ProjectReference Include="..\Repository\Repository.csproj" />
      <ProjectReference Include="..\Service\Service.csproj" />
      <ProjectReference Include="..\WinGng.Presentation\WinGng.Presentation.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Update="Dockerfile.windows">
        <DependentUpon>Dockerfile</DependentUpon>
      </None>
    </ItemGroup>

</Project>
