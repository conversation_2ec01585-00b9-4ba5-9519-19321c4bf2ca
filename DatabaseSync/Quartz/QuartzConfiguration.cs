using Microsoft.Extensions.DependencyInjection;
using Quartz;
using DatabaseSync.Quartz.Jobs;
using Quartz.Impl;

namespace DatabaseSync.Quartz;

public static class QuartzConfiguration
{
    [Obsolete("Obsolete")]
    public static void ConfigureQuartzServices(this IServiceCollection services)
    {
        services.AddQuartz(q =>
        {
            q.UseMicrosoftDependencyInjectionJobFactory();

            // Register ADD_Customer_Job
            var addCustomerJobKey = new JobKey("addCustomerJob");
            q.AddJob<ADD_Customer_Job>(opts => opts.WithIdentity(addCustomerJobKey).StoreDurably());
            
            // Register ADD_Order_Job
            var addAllArticlesJobKey = new JobKey("addAllArticlesJob");
            q.AddJob<ADD_AllArticles_Job>(opts => opts.WithIdentity(addAllArticlesJobKey).StoreDurably());
            
            // Register ADD_Article_Job
            var addArticleJobKey = new JobKey("addArticleJob");
            q.AddJob<ADD_Article_Job>(opts => opts.WithIdentity(addArticleJobKey).StoreDurably());
            
            // Register ADD_Contract_Job
            var addContractJobKey = new JobKey("addContractJob");
            q.AddJob<ADD_Contract_Job>(opts => opts.WithIdentity(addContractJobKey).StoreDurably());

            // Register AnotherJob
            var getOrdersJobKey = new JobKey("getOrdersJob");
            q.AddJob<GET_Orders_Job>(opts => opts.WithIdentity(getOrdersJobKey).StoreDurably());
            
            // Register GET_NewCustomers_Job
            var getNewCustomersJobKey = new JobKey("getNewCustomersJob");
            q.AddJob<GET_NewCustomers_Job>(opts => opts.WithIdentity(getNewCustomersJobKey).StoreDurably());
            
            // Register GET_Customer_Job
            var getCustomerJobKey = new JobKey("getCustomerJob");
            q.AddJob<GET_Customer_Job>(opts => opts.WithIdentity(getCustomerJobKey).StoreDurably());
            
            // Register DELETE_Customer_Job
            var deleteCustomerJobKey = new JobKey("deleteCustomerJob");
            q.AddJob<DELETE_Customer_Job>(opts => opts.WithIdentity(deleteCustomerJobKey).StoreDurably());
            
            // Register PATCH_Article_Job
            var patchArticleJobKey = new JobKey("patchArticleJob");
            q.AddJob<PATCH_Article_Job>(opts => opts.WithIdentity(patchArticleJobKey).StoreDurably());
            
            //Register PATCH_Contract_Job
            var patchContractJobKey = new JobKey("patchContractJob");
            q.AddJob<PATCH_Contract_Job>(opts => opts.WithIdentity(patchContractJobKey).StoreDurably());
            
            // Register PATCH_Customer_Job
            var patchCustomerJobKey = new JobKey("patchCustomerJob");
            q.AddJob<PATCH_Customer_Job>(opts => opts.WithIdentity(patchCustomerJobKey).StoreDurably());
            
            // Register PATCH_Order_Job
            var patchOrderJobKey = new JobKey("patchOrderJob");
            q.AddJob<PATCH_Order_Job>(opts => opts.WithIdentity(patchOrderJobKey).StoreDurably());
            
            // Register Runner_JOB for every 30 sec
            var runnerJobKey = new JobKey("runnerJob");
            q.AddJob<Runner_JOB>(opts => opts.WithIdentity(runnerJobKey).StoreDurably());
            q.AddTrigger(opts => opts
                .ForJob(runnerJobKey)
                .WithIdentity("runnerJob-trigger")
                .WithCronSchedule("0/30 * * * * ?"));
        });
        
        services.AddSingleton<IScheduler>(provider =>
        {
            var schedulerFactory = new StdSchedulerFactory();
            var scheduler = schedulerFactory.GetScheduler().Result;
            scheduler.Start();
            return scheduler;
        });
        
        services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
    }
}