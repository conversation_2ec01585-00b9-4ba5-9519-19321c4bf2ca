using Quartz;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Repository.ModelRepositories;
using Service.Contracts;
using Order = DatabaseSync.ReceiverClasses.Order;
using OrderItem = DatabaseSync.ReceiverClasses.OrderItem;

namespace DatabaseSync.Quartz.Jobs
{
    public class GET_Orders_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<GET_Orders_Job> _logger;
        
        public GET_Orders_Job(IServiceManager service, ILogger<GET_Orders_Job> logger)
        {
            _service = service;
            _logger = logger;
        }
        
        public async Task Execute(IJobExecutionContext context)
        {
            GET_Orders getOrders = new GET_Orders(_service);
            
            Console.WriteLine("Looking for watched Lieferscheine...");
            var watchedLieferscheine = _service.AuftragskopfService.GetWatchedLieferscheine(false);
            if (!watchedLieferscheine.Any())
            {
                Console.WriteLine("No watched Lieferscheine found.");
            }
            else
            {
                foreach (long? l in watchedLieferscheine)
                {
                    if (l == null) continue;
                    var orderId = _service.AuftragskopfService.GetOrderIdByAuftragsnummer((long)l, false);
                    if (orderId == "") continue;
                    if (getOrders.PushOrderDoneStateAsync(orderId, (long)l).GetAwaiter().GetResult())
                    {
                        Console.WriteLine($"Order {l} marked as done successfully.");
                    }
                    else
                    {
                        Console.WriteLine($"Failed to mark order {l} as done.");
                    }
                }
                
                
                
                Console.WriteLine("Disabling watcher for watched Lieferscheine...");
                _service.AuftragskopfService.DisableWatcher(watchedLieferscheine);
                Console.WriteLine("Watcher disabled.");
            }
            
            
            
            
            await Task.Delay(TimeSpan.FromSeconds(1));
            IEnumerable<Order> orders = null;
            try
            {
                
                Console.WriteLine("Retrieving new order data...");
                orders = await getOrders.GetOrdersAsync();
                if (!orders.Any())
                {
                    Console.WriteLine("Orders not found.");
                    return;
                }
                else
                {
                    foreach (Order o in orders)
                    {
                        Kunden tempKunde = (_service.KundenService.GetKundeById((int)o.CustomerId, false)).FirstOrDefault();
                        Auftragskopf auftragskopf = new Auftragskopf
                        {
                            Auftragsnummer = _service.AuftragskopfService.GetMaxAuftragsnummer() + 1,
                            Auftrag = true,
                            KundenNrWe = o.CustomerId,
                            KundenNrRe = o.CustomerId,
                            Auftragsdatum = o.CreatedAt,
                            Bestellnr = o.InternalOrderNumber,
                            LvonDatum = o.ProcessingStartDate,
                            LbisDatum = o.ProcessingEndDate,
                            Eldatum = o.DeliveryDate,
                            Name1We = tempKunde?.Kdname1,
                            StrasseWe = tempKunde?.Kdstrasse,
                            Plzwe = tempKunde?.Kdplz,
                            OrtWe = tempKunde?.Kdort,
                            Name1Re = tempKunde?.Kdname1,
                            StrasseRe = tempKunde?.Kdstrasse,
                            Plzre = tempKunde?.Kdplz,
                            OrtRe = tempKunde?.Kdort,
                            Bearbeiter = "WEBSHOP",
                            Sbgwe = tempKunde?.Kdsbg,
                            Sbgre = tempKunde?.Kdsbg,
                            AnredeWe = tempKunde?.Kdanrede,
                            AnredeRe = tempKunde?.Kdanrede,
                            LandWe = tempKunde?.Kdland,
                            LandRe = tempKunde?.Kdland,
                            AbrKennzeichen = "F",
                            Arvnummer = 0,
                            LsabrKng = true,
                            TelefonWe = tempKunde?.Kdtelefon1,
                            TelefonRe = tempKunde?.Kdtelefon1,
                            FaxWe = tempKunde?.Kdtelefax,
                            FaxRe = tempKunde?.Kdtelefax,
                            SysTime = o.UpdatedAt,
                            WatcherStatus = o.OrderId.ToString(),
                            
                        };
                        if (o.Items != null)
                        {
                            int index = 1;
                            foreach (OrderItem i in o.Items)
                            {
                                Artikel tempArtikel = (_service.ArtikelService.GetArtikelById((long)i.ArticleId, false)).FirstOrDefault();
                                Auftragspo auftragspos = new Auftragspo
                                {
                                    Auftragsnummer = (long)auftragskopf.Auftragsnummer,
                                    Status = "A",
                                    GridPos = index++,
                                    Erfassungsschema = "PO",
                                    ArtikelNummer = i.ArticleId,
                                    ArtikelNummer2 = tempArtikel?.Hartikel,
                                    Bezeichnung = tempArtikel?.ArtBezText1,
                                    BezGr = tempArtikel?.ArtKolliInhalt > 0 ? (tempArtikel?.ArtKolliInhalt).ToString() : tempArtikel?.ArtBezugsgr,
                                    Anzahl = (double?)(tempArtikel?.ArtKolliInhalt > 0 ? i.Quantity : 0),
                                    GesMenge = tempArtikel?.ArtKolliInhalt == 0 ? i.Quantity : i.Quantity * tempArtikel?.ArtKolliInhalt,
                                    Gpreis = null,
                                    Eannr = tempArtikel?.ArtEan,
                                    Mwst = tempArtikel?.ArtMwSt,
                                    Faktor = (tempArtikel?.ArtFaktor).ToString(),
                                    KontraktNr = i.ContractId
                                };
                                if (tempArtikel?.ArtKolliInhalt > 0)
                                {
                                    auftragskopf.Palettenware = true;
                                }
                                Console.WriteLine("Writing orderpos "+ index + " of order "+ o.OrderNumber + " to database...");
                                _service.AuftragsposService.WriteOrderItemToDb(false, auftragspos);
                                Kontrakt tempKontrakt = (_service.KontraktService.GetKontraktById((int)i.ContractId, false)).FirstOrDefault();
                                Auftragspo kontraktLine = new Auftragspo
                                {
                                    Auftragsnummer = (long)auftragskopf.Auftragsnummer,
                                    Status = "A",
                                    GridPos = index++,
                                    Erfassungsschema = "TE",
                                    ArtikelNummer = 0,
                                    ArtikelNummer2 = 0,
                                    Bezeichnung = "Kontrakt Nr. " + i.ContractId + ",vom " + tempKontrakt?.Ktstart?.ToString("dd.MM.yyyy"),
                                    BezGr = "",
                                    Anzahl = 0,
                                    GesMenge = 0,
                                    Gpreis = 0,
                                    Eannr = "",
                                    Mwst = 0,
                                    Faktor = "",
                                    KontraktNr = i.ContractId
                                };
                                Console.WriteLine("Writing kontraktpos "+ index + " of order "+ o.OrderNumber + " to database...");
                                _service.AuftragsposService.WriteOrderItemToDb(false, kontraktLine);
                                
                                
                                
                                PATCH_Contract patchContract = new PATCH_Contract(_service);
                                
                                if (tempKontrakt != null)
                                {
                                    if (auftragspos.GesMenge != null)
                                    {
                                        Console.WriteLine("Updating contract " + tempKontrakt.Ktnr + " in local database... by amount " + (float)(auftragspos.GesMenge / 1000));
                                        _service.KontraktService.ChangeKontraktAmount(tempKontrakt, (float)(auftragspos.GesMenge / 1000));

                                        // Re-fetch the contract to get the updated KtrestMenge
                                        Kontrakt updatedKontrakt = (_service.KontraktService.GetKontraktById((int)tempKontrakt.Ktnr, false)).FirstOrDefault()!;

                                        if (updatedKontrakt != null)
                                        {
                                            var contractDto = new Contract()
                                            {
                                                ContractId = updatedKontrakt.Ktnr,
                                                CustomerId = updatedKontrakt.KtliefNr,
                                                StartDate = updatedKontrakt.Ktstart,
                                                EndDate = updatedKontrakt.Ktende,
                                                StartQuantity = updatedKontrakt.KtstartMenge,
                                                RestQuantity = updatedKontrakt.KtrestMenge,
                                                BasePrice = updatedKontrakt.Ktbasispreis,
                                                ArticleNumber = updatedKontrakt.KtartikelNr
                                            };
                                            Console.WriteLine("Attempting to update contract " + contractDto.ContractId + " (RestQuantity: " + contractDto.RestQuantity + ") in webserver database...");
                                            await patchContract.UpdateContractAsync(updatedKontrakt.Ktnr, contractDto); // Update the contract()
                                        }
                                        else
                                        {
                                            Console.WriteLine($"Error: Could not re-fetch contract {tempKontrakt.Ktnr} after updating its amount in the local DB.");
                                        }
                                    }
                                }
                            }
                        }

                        Console.WriteLine("Writing order " + o.OrderNumber + " to database...");
                        _service.AuftragskopfService.WriteOrderToDb(false, auftragskopf);
                        if (getOrders.PushOrderInProcessStateAsync(o.OrderId, (long)auftragskopf.Auftragsnummer).GetAwaiter().GetResult())
                        {
                            Console.WriteLine($"Order {o.OrderNumber} marked as done successfully.");
                        }
                        else
                        {
                            Console.WriteLine($"Failed to mark order {o.OrderNumber} as done.");
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }
        }
    }
}
