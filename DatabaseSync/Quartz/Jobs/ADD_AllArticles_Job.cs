using System.Globalization;
using System.Text.RegularExpressions;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;
using System.Linq;

namespace DatabaseSync.Quartz.Jobs
{
    public class ADD_AllArticles_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<ADD_AllArticles_Job> _logger;

        public ADD_AllArticles_Job(IServiceManager service, ILogger<ADD_AllArticles_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Console.WriteLine("ADD_AllArticles_Job started.");
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            IEnumerable<Artikel> artikels = null;
            try
            {
                artikels = _service.ArtikelService.GetAllArtikel( false);
                if (artikels == null)
                {
                    Console.WriteLine("Articles not found.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find any articles. Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }

            double ExtractFirstDouble(string input)
            {
                // Replace "lose" with "0"
                input = input.Replace("lose", "0");

                // Find the first match that could represent a number
                Regex regex = new Regex(@"-?\d+(\.\d+)?|-?\d+,\d+");
                Match match = regex.Match(input);

                if (match.Success)
                {
                    // Replace commas with dots for correct parsing
                    string valueString = match.Value.Replace(',', '.');
                    if (double.TryParse(valueString, NumberStyles.Any, CultureInfo.InvariantCulture, out double value))
                    {
                        return value;
                    }
                }

                return 0; // Return 0 if no valid number is found
            }

            double SafeExtractFirstDouble(string? input)
            {
                if (input != null)
                {
                    return ExtractFirstDouble(input);
                }
                else
                {
                    return 0;
                }
            }

            
            string ReplaceSpecialCharacters(string input)
            {
                return input.Replace("ä", "ae")
                    .Replace("ö", "oe")
                    .Replace("ü", "ue")
                    .Replace("Ä", "Ae")
                    .Replace("Ö", "Oe")
                    .Replace("Ü", "Ue");
            }
            
            

            Article[] allArticles = artikels.Select(a => new Article
            {
                ArticleId = a.Artikelnr,
                Name = string.IsNullOrEmpty(a.ArtBezText1) 
                    ? "Name_MISSING" 
                    : ReplaceSpecialCharacters(a.ArtBezText1).TrimEnd().Replace(" ", " "),
                MatchCode = string.IsNullOrEmpty(a.ArtSbg) 
                    ? "MatchCode_MISSING" 
                    : ReplaceSpecialCharacters(a.ArtSbg).TrimEnd().Replace(" ", ""),
                ReferenceValue = string.IsNullOrEmpty(a.ArtBezugsgr) 
                    ? "Reference_MISSING" 
                    : ReplaceSpecialCharacters(a.ArtBezugsgr).TrimEnd().Replace(" ", ""),
                Price = SafeExtractFirstDouble(string.IsNullOrEmpty(a.ArtVkpreis.ToString()) ? "0" : a.ArtVkpreis.ToString())
            }).ToArray();




            
            
            Console.WriteLine("Adding " + allArticles.Length + " articles to remote database...");
            ADD_AllArticles addAllArticles = new ADD_AllArticles(_service);
            await addAllArticles.AddAllArticlesAsync(allArticles);
            await Task.Delay(TimeSpan.FromSeconds(60)); // Simulate some work
            Console.WriteLine("ALL_Articles added successfully.");
        }
    }
}