using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;
using Order = DatabaseSync.ReceiverClasses.Order;

namespace DatabaseSync.Quartz.Jobs
{
    public class PATCH_Order_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<PATCH_Order_Job> _logger;

        public PATCH_Order_Job(IServiceManager service, ILogger<PATCH_Order_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            var dataMap = context.MergedJobDataMap;
            var auftragskopfId = dataMap.GetInt("AuftragskopfId");
            IEnumerable<Auftragskopf> auftragskopfs = null;
            try
            {
                auftragskopfs = _service.AuftragskopfService.GetAuftragskopfById(auftragskopfId, false);
                if (auftragskopfs == null)
                {
                    Console.WriteLine("Order not found.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find order . Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }

            GET_Customer getCustomers = new GET_Customer(_service);
            Order order = new Order()
            {
                OrderNumber = auftragskopfs.First().Auftragsnummer,
                CustomerId = (long)auftragskopfs.First().KundenNrWe,
                Items = new List<OrderItem>(),
                DeliveryDate = auftragskopfs.First().LbisDatum,
                CreatedAt = auftragskopfs.First().Auftragsdatum,
                UpdatedAt = auftragskopfs.First().SysTime
                // Status?
            };
            
            _service.AuftragsposService.GetAuftragsposById(auftragskopfId, false).ToList().ForEach(auftragspos =>
            {
                ADD_Contract addContract = new ADD_Contract(_service);
                order.Items.Add(new OrderItem()
                {
                    Quantity = auftragspos.GesMenge,
                    Price = auftragspos.Gpreis,
                    ContractId = auftragspos.KontraktNr,
                    ArticleId = auftragspos.ArtikelNummer
                });
            });
            
            Console.WriteLine("Updating order...");
            PATCH_Order patchOrder = new PATCH_Order(_service);
            await patchOrder.UpdateOrderAsync((int)auftragskopfs.First().Auftragsnummer, order);
        }
    }
}