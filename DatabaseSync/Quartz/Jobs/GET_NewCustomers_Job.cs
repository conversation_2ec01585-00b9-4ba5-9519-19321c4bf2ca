using Quartz;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Repository.ModelRepositories;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class GET_NewCustomers_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<GET_NewCustomers_Job> _logger;
        
        public GET_NewCustomers_Job(IServiceManager service, ILogger<GET_NewCustomers_Job> logger)
        {
            _service = service;
            _logger = logger;
        }
        
        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1));
            try
            {
                GET_NewCustomers getNewCustomers = new GET_NewCustomers(_service);
                ADD_Contract addContract = new ADD_Contract(_service);
                ADD_Customer addCustomer = new ADD_Customer(_service);
                Console.WriteLine("Retrieving new customer data...");
                List<long> newCustomerIds = await getNewCustomers.GetCustomersAsync() ?? throw new KundenNotFoundException();
                if (newCustomerIds.Count == 0)
                {
                    Console.WriteLine("No new customers found.");
                    return;
                }
                else
                {
                    foreach (long newCustomerId in newCustomerIds)
                    {
                        Kunden kunde = (_service.KundenService.GetKundeById((int)newCustomerId, false)).FirstOrDefault();
                        if (kunde == null)
                        {
                            Console.WriteLine("For customer created on webshop, no corresponding customer found in database.");
                            return;
                        }

                        // Create the DTO with the new structure
                        CustomerDto customerDto = new CustomerDto()
                        {
                            CustomerId = kunde.Kdnummer,
                            Name = kunde.Kdname1,
                            Street = kunde.Kdstrasse,
                            City = kunde.Kdort,
                            ZipCode = kunde.Kdplz,
                            Country = kunde.Kdland,
                            Email = kunde.Kdemail,
                            InvoiceReceiverId = null, // Update this if you have the information
                            InvoiceReceiver = null  // Update this if you have the information
                        };

                        Console.WriteLine("Adding new customer to database...");
                        await addCustomer.AddCustomerAsync(customerDto);
                        
                        IEnumerable<Kontrakt> kontrakte = _service.KontraktService.GetKontrakteByKundeId((int)kunde.Kdnummer, false);
                        foreach (Kontrakt k in kontrakte)
                        {
                            Contract contract = new Contract()
                            {
                                ContractId = k.Ktnr,
                                CustomerId = k.KtliefNr,
                                StartDate = k.KtabDatum,
                                EndDate = k.KtbisDatum,
                                StartQuantity = k.KtstartMenge,
                                RestQuantity = k.KtrestMenge,
                                BasePrice = k.Ktbasispreis,
                                ArticleNumber = k.KtartikelNr
                            };
                            Console.WriteLine("Adding new contract to database...");
                            await addContract.AddContractAsync(contract);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }
        }
    }
    public class CustomerDto
    {
        public long CustomerId { get; set; }
        public string Name { get; set; }
        public string Street { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string Country { get; set; }
        public string Email { get; set; }
        public long? InvoiceReceiverId { get; set; }
        public string InvoiceReceiver { get; set; }
    }
}
