using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class ADD_Contract_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<ADD_Contract_Job> _logger;

        public ADD_Contract_Job(IServiceManager service, ILogger<ADD_Contract_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            var dataMap = context.MergedJobDataMap;
            int kontraktId = dataMap.GetInt("KontraktId");
            _logger.LogInformation($"Attempting to find contract with ID: {kontraktId}");
            
            IEnumerable<Kontrakt> kontrakt = null;
            try
            {
                kontrakt = _service.KontraktService.GetKontraktById(kontraktId, false);
                _logger.LogInformation($"GetKontraktById returned: {(kontrakt != null ? (kontrakt.Any() ? "data" : "empty collection") : "null")}");
                
                if (kontrakt == null || !kontrakt.Any())
                {
                    _logger.LogError($"Contract with ID {kontraktId} not found or empty result returned.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find contract with ID {kontraktId}. Error: {ex.Message}");
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
                return;
            }

            var kontraktItem = kontrakt.FirstOrDefault();
            if (kontraktItem == null)
            {
                _logger.LogError($"Contract with ID {kontraktId} returned null item.");
                return;
            }
            
            try
            {
                var artikel = _service.ArtikelService.GetArtikelById(kontraktItem.KtartikelNr, false);
                var artikelItem = artikel != null && artikel.Any() ? artikel.FirstOrDefault() : null;
                
                Contract contract = new Contract
                {
                    ContractId = kontraktItem.Ktnr,
                    CustomerId = kontraktItem.KtliefNr,
                    Customer = _service.KundenService.GetKundeById((kontraktItem.KtliefNr), false),
                    StartDate = kontraktItem.Ktstart,
                    EndDate = kontraktItem.Ktende,
                    StartQuantity = kontraktItem.KtstartMenge,
                    RestQuantity = kontraktItem.KtrestMenge,
                    BasePrice = kontraktItem.Ktbasispreis,
                    ArticleNumber = kontraktItem.KtartikelNr,
                    Article = artikelItem,
                    OrderItems = null
                };
                
                Console.WriteLine("Adding contract...");
                ADD_Contract addContract = new ADD_Contract(_service);
                await addContract.AddContractAsync(contract);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error creating or adding contract: {ex.Message}");
            }
        }
    }
}