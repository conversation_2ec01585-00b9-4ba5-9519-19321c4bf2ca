using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs;

public class QuartzController
{
    private readonly ISchedulerFactory factory;
    private readonly ILogger<QuartzController> _logger;
    
    public QuartzController(ISchedulerFactory factory, ILogger<QuartzController> logger)
    {
        _logger = logger;
        this.factory = factory;
    }
    
    public async Task Start()
    {
        IScheduler scheduler = await factory.GetScheduler();
        
        await scheduler.TriggerJob(new JobKey("addCustomerJob"));
    }
}