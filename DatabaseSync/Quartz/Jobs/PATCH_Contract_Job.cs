using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class PATCH_Contract_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<PATCH_Contract_Job> _logger;

        public PATCH_Contract_Job(IServiceManager service, ILogger<PATCH_Contract_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            var dataMap = context.MergedJobDataMap;
            int kontraktId = dataMap.GetInt("KontraktId");
            IEnumerable<Kontrakt> kontrakt = null;
            try
            {
                kontrakt = _service.KontraktService.GetKontraktById(kontraktId, false);
                if (kontrakt == null)
                {
                    Console.WriteLine("Contract not found.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find contract. Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }

            Contract contract = new Contract
            {
                ContractId = kontrakt.First().Ktnr,
                CustomerId = kontrakt.First().KtliefNr,
                Customer = _service.KundenService.GetKundeById((int)(kontrakt.First().KtliefNr), false),
                StartDate = kontrakt.First().Ktstart,
                EndDate = kontrakt.First().Ktende,
                StartQuantity = kontrakt.First().KtstartMenge,
                RestQuantity = kontrakt.First().KtrestMenge,
                BasePrice = kontrakt.First().Ktbasispreis,
                ArticleNumber = kontrakt.First().KtartikelNr,
                Article = _service.ArtikelService.GetArtikelById((int)(kontrakt.First().KtartikelNr), false).First(),
                OrderItems = null
            };
            Console.WriteLine("Updating contract...");
            PATCH_Contract patchContract = new PATCH_Contract(_service);
            await patchContract.UpdateContractAsync(kontraktId, contract);
        }
    }
}