using Quartz;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Repository.ModelRepositories;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class GET_Customer_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<GET_Customer_Job> _logger;
        
        public GET_Customer_Job(IServiceManager service, ILogger<GET_Customer_Job> logger)
        {
            _service = service;
            _logger = logger;
        }
        
        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1));
            var dataMap = context.MergedJobDataMap;
            int customerId = dataMap.GetInt("CustomerId");
            IEnumerable<Customer> customers = null;
            try
            {
                GET_Customer getCustomers = new GET_Customer(_service);
                Console.WriteLine("Retrieving customer data...");
                customers = await getCustomers.GetCustomersAsync(customerId);
                if (!customers.Any())
                {
                    Console.WriteLine("No new customers found.");
                    return;
                }
                else
                {
                    foreach (Customer c in customers)
                    {
                        Kunden kunde = new Kunden()
                        {
                            Kdnummer = c.CustomerId,
                            Kdname1 = c.Name
                        };

                        Console.WriteLine("Writing new customer to database...");
                        _service.KundenService.WriteKundeToDb(false, kunde);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }
        }
    }
}