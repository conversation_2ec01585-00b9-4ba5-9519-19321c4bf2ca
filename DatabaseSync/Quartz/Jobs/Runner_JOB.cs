using System.Globalization;
using System.Text.RegularExpressions;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;
using System.IO;
using Newtonsoft.Json.Linq;

namespace DatabaseSync.Quartz.Jobs
{
    public class Runner_JOB : IJob
    {
        private readonly IServiceManager _service;
        private readonly ISchedulerFactory _factory;
        private readonly ILogger<Runner_JOB> _logger;
        private static IConfiguration _configuration;

        public Runner_JOB(ISchedulerFactory factory, IServiceManager service, ILogger<Runner_JOB> logger,
            IConfiguration configuration)
        {
            _factory = factory;
            _service = service;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task TriggerJobAsync(string jobName, string dataKey, object dataValue, IScheduler scheduler)
        {
            try
            {
                JobDataMap dataMap = new JobDataMap
                {
                    { dataKey, dataValue }
                };

                await scheduler.TriggerJob(new JobKey(jobName), dataMap);

                dataMap.Remove(dataKey);
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while triggering the job '{jobName}': {ex.Message}");
            }
        }

        public async Task TriggerJobAsync(string jobName, IScheduler scheduler)
        {
            try
            {
                await scheduler.TriggerJob(new JobKey(jobName));
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while triggering the job '{jobName}': {ex.Message}");
            }
        }

        private async Task HandleTriggerLogAsync(TriggerLog triggerLog, IScheduler scheduler)
        {
            switch (triggerLog.Action)
            {
                case "DELETE":
                    await HandleDeleteActionAsync(triggerLog, scheduler);
                    break;

                case "INSERT":
                    await HandleInsertActionAsync(triggerLog, scheduler);
                    break;

                case "UPDATE":
                    await HandleUpdateActionAsync(triggerLog, scheduler);
                    break;

                default:
                    _logger.LogWarning($"Unexpected action: {triggerLog.Action}");
                    break;
            }
        }

        private async Task HandleDeleteActionAsync(TriggerLog triggerLog, IScheduler scheduler)
        {
            switch (triggerLog.TableAffected)
            {
                case "Kunden":
                    _logger.LogInformation("Trying to: Delete Customer");
                    await TriggerJobAsync("deleteCustomerJob", "CustomerId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                default:
                    _logger.LogWarning($"Unexpected table for DELETE action: {triggerLog.TableAffected}");
                    break;
            }
        }

        private async Task HandleInsertActionAsync(TriggerLog triggerLog, IScheduler scheduler)
        {
            switch (triggerLog.TableAffected)
            {
                case "Artikel":
                    _logger.LogInformation("Trying to: Add Article");
                    await TriggerJobAsync("addArticleJob", "ArtikelId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                case "Kontrakt":
                    _logger.LogInformation("Trying to: Add Contract");
                    await TriggerJobAsync("addContractJob", "KontraktId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                case "Kunden":
                    //NOT SUPPOSED TO ACTUALLY ADD CUSTOMERS
                    //_logger.LogInformation("Trying to: Add Customer");
                    //await TriggerJobAsync("addCustomerJob", "KundenId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                default:
                    _logger.LogWarning($"Unexpected table for INSERT action: {triggerLog.TableAffected}");
                    break;
            }
        }

        private async Task HandleUpdateActionAsync(TriggerLog triggerLog, IScheduler scheduler)
        {
            switch (triggerLog.TableAffected)
            {
                case "Artikel":
                    _logger.LogInformation("Trying to: Update Article");
                    await TriggerJobAsync("patchArticleJob", "ArtikelId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                case "Kontrakt":
                    _logger.LogInformation("Trying to: Update Contract");
                    await TriggerJobAsync("patchContractJob", "KontraktId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                case "Kunden":
                    _logger.LogInformation("Trying to: Update Customer");
                    await TriggerJobAsync("patchCustomerJob", "CustomerId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                case "Auftragskopf":
                    _logger.LogInformation("Trying to: Update Order");
                    _logger.LogInformation("Temporarily disabled: Update Order");
                    //await TriggerJobAsync("patchOrderJob", "AuftragskopfId", triggerLog.EntryID, scheduler);
                    _service.TriggerLogService.SetAsDone(triggerLog);
                    break;

                default:
                    _logger.LogWarning($"Unexpected table for UPDATE action: {triggerLog.TableAffected}");
                    break;
            }
        }

        private void UpdateInitialStart(bool value)
        {
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
            var json = File.ReadAllText(filePath);
            dynamic jsonObj = JObject.Parse(json);
            jsonObj["InitialStart"] = value;
            string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(filePath, output);
        }

        private bool GetInitialStart()
        {
            return _configuration.GetValue<bool>("InitialStart");
        }

        public static String GetUrl()
        {
            return _configuration.GetValue<string>("URL") ??
                   throw new InvalidOperationException("URL not found in appsettings.json");
        }

        public static string GetValue(string key) => _configuration.GetValue<string>(key) ??
                                                     throw new InvalidOperationException(
                                                         $"{key} not found in environment settings");

        public async Task Execute(IJobExecutionContext context)
        {
            if (Sync.LoggedIn == false)
            {
                try
                {
                    await Sync.AuthenticateAsync();
                    Sync.LoggedIn = true;
                }
                catch (Exception e)
                {
                    _logger.LogError($"An error occurred while logging in: {e.Message}");
                    return;
                }
            }

            await Sync.refreshTokenAsync();

            IScheduler scheduler = await _factory.GetScheduler();

#if DEBUG
            if (Sync.debugPush)
            {
                TriggerJobAsync("addAllArticlesJob", scheduler).GetAwaiter().GetResult();
                Sync.debugPush = false;
            }
#endif
#if RELEASE
            if (GetInitialStart())
            {
                await TriggerJobAsync("addAllArticlesJob", scheduler);
                UpdateInitialStart(false);
            }
#endif

            //await TriggerJobAsync("addCustomerJob", "KundenId", 50001, scheduler);
            List<TriggerLog?> triggerLogs = _service.TriggerLogService.GetOpenTriggers(false).ToList();

            foreach (var triggerLog in triggerLogs)
            {
                if (triggerLog == null)
                {
                    _logger.LogInformation("No trigger log found.");
                    continue;
                }

                await HandleTriggerLogAsync(triggerLog, scheduler);
            }

            await TriggerJobAsync("getNewCustomersJob", scheduler);

            await TriggerJobAsync("getOrdersJob", scheduler);
        }
    }
}