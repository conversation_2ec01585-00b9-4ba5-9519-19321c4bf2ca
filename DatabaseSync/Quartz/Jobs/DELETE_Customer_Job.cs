using Quartz;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Repository.ModelRepositories;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class DELETE_Customer_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<DELETE_Customer_Job> _logger;
        
        public DELETE_Customer_Job(IServiceManager service, ILogger<DELETE_Customer_Job> logger)
        {
            _service = service;
            _logger = logger;
        }
        
        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1));
            var dataMap = context.MergedJobDataMap;
            int customerId = dataMap.GetInt("CustomerId");
            try
            {
                DELETE_Customer deleteCustomer = new DELETE_Customer(_service);
                Console.WriteLine("Deleting customer...");
                await deleteCustomer.DeleteCustomerAsync(customerId);
                Console.WriteLine("Customer deleted.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }
        }
    }
}