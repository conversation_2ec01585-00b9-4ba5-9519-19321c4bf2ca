using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class ADD_Customer_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<ADD_Customer_Job> _logger;

        public ADD_Customer_Job(IServiceManager service, ILogger<ADD_Customer_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            var dataMap = context.MergedJobDataMap;
            int kundenId = dataMap.GetInt("KundenId");
            IEnumerable<Kunden> kunden = null;
            try
            {
                kunden = _service.KundenService.GetKundeById(kundenId, false);
                if (kunden == null)
                {
                    Console.WriteLine("Customer not found.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find customer with Kdnummer 5001. Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }

            Customer customer = new Customer
            {
                CustomerId = kunden.First().Kdnummer,
                Name = kunden.First().Kdname1,
                Contracts = null,
                InvoiceReceiverId = null,
                InvoiceReceiver = null,
                Addresses = null,
                Orders = null
            };
            Console.WriteLine("Adding customer...");
            ADD_Customer addCustomer = new ADD_Customer(_service);
            //await addCustomer.AddCustomerAsync(customer);
        }
    }
}