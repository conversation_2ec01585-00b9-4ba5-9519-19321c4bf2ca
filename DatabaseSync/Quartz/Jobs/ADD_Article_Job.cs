using System.Globalization;
using System.Text.RegularExpressions;
using DatabaseSync.ReceiverClasses;
using DatabaseSync.Syncpoints;
using Entities.Exceptions;
using Entities.Models;
using Microsoft.Extensions.Logging;
using Quartz;
using Service.Contracts;

namespace DatabaseSync.Quartz.Jobs
{
    public class ADD_Article_Job : IJob
    {
        private readonly IServiceManager _service;
        private readonly ILogger<ADD_Article_Job> _logger;

        public ADD_Article_Job(IServiceManager service, ILogger<ADD_Article_Job> logger)
        {
            _service = service;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await Task.Delay(TimeSpan.FromSeconds(1)); // Simulate some work
            var dataMap = context.MergedJobDataMap;
            int artikelId = dataMap.GetInt("ArtikelId");
            IEnumerable<Artikel> artikels = null;
            try
            {
                artikels = _service.ArtikelService.GetArtikelById(artikelId, false);
                if (artikels == null)
                {
                    Console.WriteLine("Article not found.");
                    return;
                }

            }
            catch (KundenNotFoundException ex)
            {
                _logger.LogError($"Failed to find artikel with Kdnummer 5001. Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred: {ex.Message}");
            }

            double ExtractFirstDouble(string input)
            {
                // Replace "lose" with "0"
                input = input.Replace("lose", "0");

                // Find the first match that could represent a number
                Regex regex = new Regex(@"-?\d+(\.\d+)?|-?\d+,\d+");
                Match match = regex.Match(input);

                if (match.Success)
                {
                    // Replace commas with dots for correct parsing
                    string valueString = match.Value.Replace(',', '.');
                    if (double.TryParse(valueString, NumberStyles.Any, CultureInfo.InvariantCulture, out double value))
                    {
                        return value;
                    }
                }

                return 0; // Return 0 if no valid number is found
            }

            Article article = new Article
            {
                ArticleId = artikels.First().Artikelnr,
                Name = artikels.First().ArtBezText1,
                MatchCode = artikels.First().ArtSbg,
                ReferenceValue = artikels.First().ArtBezugsgr,
                Price = ExtractFirstDouble(artikels.First().ArtVkpreis.ToString()),
            };
            Console.WriteLine("Adding article...");
            ADD_Article addArticle = new ADD_Article(_service);
            await addArticle.AddArticleAsync(article);
        }
    }
}