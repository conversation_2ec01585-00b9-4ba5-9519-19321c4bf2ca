using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using Contracts;
using DatabaseSync.Quartz.Jobs;
using DatabaseSync.ReceiverClasses;
using Entities.Models;
using Service;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class ADD_Customer(IServiceManager service)
    {
        protected string baseUrl = Sync._url + "/customers"; // Base URL for customers

        // Method to add a customer using a Customer object
        public async Task AddCustomerAsync(CustomerDto customer)
        {
            var jsonContent = JsonSerializer.Serialize(customer);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPostAsync(baseUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Customer added successfully.");
                }
                else
                {
                    Console.WriteLine($"Failed to add customer. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        // To call the AddCustomerAsync method synchronously
        public void AddCustomer(int Kundennummer)
        {
        }
    }
}
