using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class ADD_AllArticles
    {
        protected static string baseUrl = Sync._url + "/articles"; // Base URL for articles

        private readonly IServiceManager _service;

        public ADD_AllArticles(IServiceManager service)
        {
            _service = service;
        }

        // Method to add an array of articles using an Article array
        public async Task AddAllArticlesAsync(Article[] articles)
        {
            var placeholder = "N/A"; // Define your placeholder value here

            var articleArray = articles.Select(article => new
            {
                articleId = article.ArticleId, // Assuming ArticleId is never an empty value and does not need a placeholder
                name = string.IsNullOrEmpty(article.Name) ? placeholder : article.Name,
                matchCode = string.IsNullOrEmpty(article.MatchCode) ? placeholder : article.MatchCode,
                referenceValue = string.IsNullOrEmpty(article.ReferenceValue) ? placeholder : article.ReferenceValue,
                price = article.Price > 0 ? article.Price : 0 // Set price to 0 if it is less than or equal to 0
            }).ToArray();

            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true
            };
            var formattedJsonContent = JsonSerializer.Serialize(articleArray, jsonOptions);
            Console.WriteLine(formattedJsonContent);

            // Serialize without indentation for sending
            var compactJsonContent = JsonSerializer.Serialize(articleArray);
            var content = new StringContent(compactJsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPostAsync(baseUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Articles added successfully.");
                }
                else
                {
                    Console.WriteLine($"Failed to add articles. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }
    }
}
