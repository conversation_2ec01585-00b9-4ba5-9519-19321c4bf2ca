using System;
using System.Globalization;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class ADD_Article
    {
        protected static string baseUrl = Sync._url + "/articles"; // Base URL for articles

        private readonly IServiceManager _service;

        public ADD_Article(IServiceManager service)
        {
            _service = service;
        }

        // Method to add an article using an Article object
        public async Task AddArticleAsync(Article article)
        {
            var placeholder = "N/A";
            // Create the custom JSON format
            var articleArray = new[]
            {
                new
                {
                    articleId = article.ArticleId, // Assuming ArticleId is never an empty value and does not need a placeholder
                    name = string.IsNullOrEmpty(article.Name) ? placeholder : article.Name,
                    matchCode = string.IsNullOrEmpty(article.MatchCode) ? placeholder : article.MatchCode,
                    referenceValue = string.IsNullOrEmpty(article.ReferenceValue) ? placeholder : article.ReferenceValue,
                    price = article.Price > 0 ? article.Price : 0 // Set price to 0 if it is less than or equal to 0
                }
            };

            var jsonContent = JsonSerializer.Serialize(articleArray);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPostAsync(baseUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Article added successfully.");
                }
                else
                {
                    Console.WriteLine($"Failed to add article. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }
    }
}
