using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class ADD_Contract
    {
        private static readonly string baseUrl = Sync._url + "/contracts"; // Base URL for contracts

        // DTO class for contract
        private class ContractDto
        {
            public long ContractNumber { get; set; }
            public long CustomerNumber { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public float? StartQuantity { get; set; }
            public float? RestQuantity { get; set; }
            public decimal? BasePrice { get; set; }
            public long? ArticleNumber { get; set; }
        }

        private readonly IServiceManager _service;

        public ADD_Contract(IServiceManager service)
        {
            _service = service;
        }

        // Method to add a contract using a Contract object
        public async Task AddContractAsync(Contract contract)
        {
            var contractDto = new ContractDto
            {
                ContractNumber = contract.ContractId,
                CustomerNumber = contract.CustomerId,
                StartDate = contract.StartDate,
                EndDate = contract.EndDate,
                StartQuantity = contract.StartQuantity,
                RestQuantity = contract.RestQuantity,
                BasePrice = contract.BasePrice,
                ArticleNumber = contract.ArticleNumber
            };

            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            var jsonContent = JsonSerializer.Serialize(contractDto, options);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPostAsync(baseUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Contract added successfully.");
                }
                else
                {
                    Console.WriteLine($"Failed to add contract. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }
    }
}
