using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class GET_NewCustomers(IServiceManager service)
    {
        protected static string url = Sync._url + "/customers/new";
        protected static string contractUrl = Sync._url + "/contracts";

        public async Task<List<long>?> GetCustomersAsync()
        {
            var requestUrl = url;

            try
            {
                var response = await Sync.AuthorizedGetAsync(requestUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                    Console.WriteLine(jsonResponse);
                    long[] customerIds = JsonSerializer.Deserialize<long[]>(jsonResponse);

                    Console.WriteLine("Customer data retrieved successfully.");

                    return new List<long>(customerIds);
                }
                else
                {
                    Console.WriteLine($"Failed to retrieve customer data. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return null;
        }
    }
}
