using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class GET_Orders
    {
        protected static string url = Sync._url + "/orders/accepted";
        protected static string updateUrl = Sync._url + "/orders";

        private readonly IServiceManager _service;

        public GET_Orders(IServiceManager service)
        {
            _service = service;
        }

        public async Task<List<Order>?> GetOrdersAsync()
        {
            var requestUrl = url;

            try
            {
                var response = await Sync.AuthorizedGetAsync(requestUrl);
                
                if (response.IsSuccessStatusCode)
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var orders = JsonSerializer.Deserialize<List<Order>>(jsonResponse, options);

                    Console.WriteLine("Order data retrieved successfully with" +
                                      $" {orders?.Count} orders.");

                    return orders;
                }
                else
                {
                    Console.WriteLine($"Failed to retrieve order data. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return null;
        }

        public async Task<bool> PushOrderDoneStateAsync(string? orderId, long orderNumber)
        {
            var requestUrl = $"{updateUrl}/{orderId}";

            var payload = new
            {
                orderNumber = orderNumber,
                status = "Done"
            };
            
            var jsonPayload = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Order {orderId} marked as Done on remote successfully.");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Failed to mark order {orderId} as Done on remote. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return false;
        }
        
        public async Task<bool> PushOrderInProcessStateAsync(Guid? orderId, long orderNumber)
        {
            var requestUrl = $"{updateUrl}/{orderId}";

            var payload = new
            {
                orderNumber = orderNumber,
                status = "InProcess"
            };
            
            var jsonPayload = JsonSerializer.Serialize(payload);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Order {orderId} marked as InProcess on remote successfully.");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Failed to mark order {orderId} as InProcess on remote. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return false;
        }
    }
}
