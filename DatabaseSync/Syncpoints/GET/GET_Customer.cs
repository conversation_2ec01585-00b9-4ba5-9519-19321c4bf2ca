using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class GET_Customer(IServiceManager service)
    {
        //TODO: Replace customerId with the actual customer ID
        private static string url = Sync._url + "/customers/";

        public async Task<List<Customer>?> GetCustomersAsync(int customerId)
        {
            var requestUrl = url + customerId.ToString();

            try
            {
                var response = await Sync.AuthorizedGetAsync(requestUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var customers = JsonSerializer.Deserialize<List<Customer>>(jsonResponse);

                    Console.WriteLine("Customer data retrieved successfully.");

                    return customers;
                }
                else
                {
                    Console.WriteLine($"Failed to retrieve customer data. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }

            return null;
        }
    }
}
