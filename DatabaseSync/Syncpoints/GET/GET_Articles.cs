using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;

namespace DatabaseSync.Syncpoints
{
    public class GET_Articles
    {
        protected static string url = Sync._url + "/articles/articleNumbers";

        public static async Task GetArticlesAsync()
        {
            var requestUrl = url;

            try
            {
                var response = await Sync.AuthorizedGetAsync(requestUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var articles = JsonSerializer.Deserialize<List<Article>>(jsonResponse);

                    Console.WriteLine("Article data retrieved successfully.");

                    // Placeholder: Insert logic to fit the returned values into the database
                    // Example: InsertArticlesDataIntoDatabase(articles);
                }
                else
                {
                    Console.WriteLine($"Failed to retrieve article data. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        // To call the GetArticlesAsync method synchronously
        public static void GetArticles()
        {
            GetArticlesAsync().GetAwaiter().GetResult();
        }
    }
}