using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Entities.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.Contracts;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace DatabaseSync.Syncpoints
{
    public class PATCH_Article(IServiceManager service)
    {
        protected static string baseUrl = Sync._url + "/articles/";

        public async Task UpdateArticleAsync(long articleId, Article update)
        {
            var requestUrl = baseUrl + articleId;
            var jsonContent = JsonSerializer.Serialize(update);
            JObject jsonObject = JObject.Parse(jsonContent);

            // Remove the Contracts property if it exists
            jsonObject.Property("Contracts")?.Remove();

            // Convert JObject back to JSON string
            string updatedJsonContent = jsonObject.ToString(Formatting.None);
            var content = new StringContent(updatedJsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Article successfully updated.");
                }
                else
                {
                    Console.WriteLine($"Failed to update article. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }
    }
}