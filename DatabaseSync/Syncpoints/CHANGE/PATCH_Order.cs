using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class PATCH_Order(IServiceManager service)
    {
        protected static string baseUrl = Sync._url + "/orders/";

        public async Task UpdateOrderAsync(int orderId, Order update)
        {
            var requestUrl = baseUrl + orderId;
            var jsonContent = JsonSerializer.Serialize(update, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Order successfully updated.");
                }
                else
                {
                    Console.WriteLine($"Failed to update order. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while attempting to update the order: {ex.Message}");
            }
        }
    }
}
