using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class PATCH_Contract
    {
        private static readonly string baseUrl = Sync._url + "/contracts/"; // Base URL for contracts

        // DTO class for contract update
        public class ContractUpdateDto
        {
            public long ContractNumber { get; set; }
            public long CustomerNumber { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public float? StartQuantity { get; set; }
            public float? RestQuantity { get; set; }
            public decimal? BasePrice { get; set; }
            public long? ArticleNumber { get; set; }
        }

        private readonly IServiceManager _service;

        public PATCH_Contract(IServiceManager service)
        {
            _service = service;
        }

        // Method to update a contract using a Contract object
        public async Task UpdateContractAsync(long contractId, Contract update)
        {
            var contractDto = new ContractUpdateDto
            {
                ContractNumber = update.ContractId,
                CustomerNumber = update.CustomerId,
                StartDate = update.StartDate,
                EndDate = update.EndDate,
                StartQuantity = update.StartQuantity,
                RestQuantity = update.RestQuantity,
                BasePrice = update.BasePrice,
                ArticleNumber = update.ArticleNumber
            };

            var requestUrl = baseUrl + contractId;
            var jsonContent = JsonSerializer.Serialize(contractDto);
            Console.WriteLine($"JSON sent to remote server for contract {contractId}: {jsonContent}"); // Log the JSON
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Contract successfully updated.");
                }
                else
                {
                    Console.WriteLine($"Failed to update contract. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while attempting to update the contract: {ex.Message}");
            }
        }
        
        public async Task UpdateContractAsyncDirect(long contractId, ContractUpdateDto update)
        {
            var requestUrl = baseUrl + contractId;
            var jsonContent = JsonSerializer.Serialize(update);
            Console.WriteLine($"JSON sent to remote server for contract {contractId}: {jsonContent}"); // Log the JSON
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Contract successfully updated.");
                }
                else
                {
                    Console.WriteLine($"Failed to update contract. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while attempting to update the contract: {ex.Message}");
            }
        }
    }
}
