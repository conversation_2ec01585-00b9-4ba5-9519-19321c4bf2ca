using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DatabaseSync.ReceiverClasses;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class PATCH_Customer(IServiceManager service)
    {
        protected static string baseUrl = Sync._url + "/customers/";

        public async Task UpdateCustomerAsync(int customerId, Customer update)
        {
            var requestUrl = baseUrl + customerId;
            var jsonContent = JsonSerializer.Serialize(update);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                var response = await Sync.AuthorizedPatchAsync(requestUrl, content);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Customer successfully updated.");
                }
                else
                {
                    Console.WriteLine($"Failed to update customer. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while attempting to update the customer: {ex.Message}");
            }
        }
    }

    public class CustomerUpdate
    {
        public int? CustomerId { get; set; } // Optional, usually not needed for a PATCH request
        public string? Name { get; set; }    // Optional
        public string? Street { get; set; }  // Optional
        public string? City { get; set; }    // Optional
        public string? ZipCode { get; set; } // Optional
        public string? Country { get; set; } // Optional
        public string? Email { get; set; }   // Optional
    }
}
