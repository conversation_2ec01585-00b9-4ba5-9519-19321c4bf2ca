using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Service.Contracts;

namespace DatabaseSync.Syncpoints
{
    public class DELETE_Customer(IServiceManager service)
    {
        protected static string baseUrl = Sync._url + "/customers/delete/";

        public async Task DeleteCustomerAsync(int customerId)
        {
            var requestUrl = baseUrl + customerId;

            try
            {
                var response = await Sync.AuthorizedDeleteAsync(requestUrl);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Customer "+customerId+"successfully deleted.");
                }
                else
                {
                    Console.WriteLine($"Failed to delete customer. Status Code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while attempting to delete the customer: {ex.Message}");
            }
        }
    }
}