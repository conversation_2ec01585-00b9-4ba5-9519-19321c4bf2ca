namespace DatabaseSync.ReceiverClasses;

public record Customer
{
    public required long CustomerId { get; set; }
    public required string Name { get; set; }
    public ICollection<Contract>? Contracts { get; set; }
    public long? InvoiceReceiverId { get; set; }
    public Customer? InvoiceReceiver { get; set; }
    public ICollection<Address>? Addresses { get; set; }
    public ICollection<Order>? Orders { get; set; }
}