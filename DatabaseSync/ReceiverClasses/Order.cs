
namespace DatabaseSync.ReceiverClasses;

public record Order
{
    public Guid? OrderId { get; set; }
    public long? OrderNumber { get; set; }
    public string? InternalOrderNumber { get; set; }
    public long CustomerId { get; set; }
    public ICollection<OrderItem>? Items { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public DateTime? ProcessingStartDate { get; set; }
    public DateTime? ProcessingEndDate { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    public int Status { get; set; } // 2= accepted, 3 = done
}