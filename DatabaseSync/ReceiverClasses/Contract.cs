using Entities.Models;

namespace DatabaseSync.ReceiverClasses;

public record Contract
{
    public long ContractId { get; set; }
    public long CustomerId { get; set; }
    public IEnumerable<Kunden>? Customer { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public float? StartQuantity { get; set; }
    public float? RestQuantity { get; set; }
    public decimal? BasePrice { get; set; }
    public Artikel? Article { get; set; }
    public ICollection<OrderItem>? OrderItems { get; set; }
    public long? ArticleNumber { get; set; }
}