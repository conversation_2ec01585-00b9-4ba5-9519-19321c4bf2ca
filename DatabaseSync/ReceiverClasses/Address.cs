using System.Text.Json.Serialization;

namespace DatabaseSync.ReceiverClasses;

public record Address
{
    public Guid AddressId { get; set; }
    public string? Street { get; set; }
    public string? City { get; set; }
    public string? ZipCode { get; set; }
    public string? Country { get; set; }
    public string? Email { get; set; }
    
    public long? CustomerId { get; set; }
    [JsonIgnore]
    public Customer? Customer { get; set; }
}