using System.Diagnostics;
using Service.Contracts;
using System.Text;
using DatabaseSync.Quartz.Jobs;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;


namespace DatabaseSync;

public class Sync(IServiceManager service)
{
    protected internal static bool LoggedIn = false;
    protected internal static string _url; //"http://localhost:5000/api";
    public static string accessToken = "";
    public static string refreshToken = "";
    public static DateTime tokenExpiration;
    private static string _username; // = "sync";
    private static string _password; // = "Geheim2024!";
    private static readonly HttpClient client = new HttpClient();
    public static bool debugPush = true;
    
    
    public void SyncData()
    {
        Console.WriteLine("Syncing data...start");
        try
        {
            SyncChain();
            
            Console.WriteLine("Syncing data...end");
        }
        catch (Exception syncProblem)
        {
            Console.WriteLine("Syncing data...failed" + syncProblem.Message);
            throw;
        }
    }
    
    
    
    
    
    
    private void SyncChain()
    {
    }
    
    
    public class AuthenticationRequest
    {
        
        [JsonProperty("username")]
        public string Username { get; internal set; }
    
        [JsonProperty("password")]
        public string Password { get; internal set; }

        public AuthenticationRequest(string username, string password)
        {
            Username = username;
            Password = password;
        }
    }

    public class AuthenticationResponse
    {
        [JsonProperty("accessToken")]
        public string accessToken { get; internal set; }
    
        [JsonProperty("refreshToken")]
        public string refreshToken { get; internal set; }

        public AuthenticationResponse(string accessToken, string refreshToken)
        {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
        }
    }
    
    public static async Task AuthenticateAsync()
    {
        _url = Runner_JOB.GetUrl()+"/api";
        _username = Runner_JOB.GetValue("SyncUser:Login");
        _password = Runner_JOB.GetValue("SyncUser:Password");
        
        Console.WriteLine("Connecting to URL: " + _url);
        
        var requestUrl = _url + "/authentication/login";
        
        var authRequest = new AuthenticationRequest(_username, _password);
        
        var json = JsonConvert.SerializeObject(authRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        Console.WriteLine("Authenticating...");
        //Console.WriteLine(json);
        //Console.WriteLine(content);
        
        var response = await client.PostAsync(requestUrl, content);
        
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            var authResponse = JsonConvert.DeserializeObject<AuthenticationResponse>(responseString);
            
            accessToken = authResponse.accessToken;
            refreshToken = authResponse.refreshToken;
            tokenExpiration = DateTime.Now.AddMinutes(4); 

            Console.WriteLine("Authentication successful.");
            //Console.WriteLine($"Access Token: {accessToken}");
            //Console.WriteLine($"Refresh Token: {refreshToken}");
        }
        else
        {
            Console.WriteLine((string?)$"Error: {response.StatusCode}");
            var errorString = await response.Content.ReadAsStringAsync();
            Console.WriteLine(errorString);
        }
    }
    
    public static async Task refreshTokenAsync()
    {
        
        if (DateTime.Now < tokenExpiration)
        {
            return;
        }
        var requestUrl = _url + "/authentication/refresh";
        
        var refreshRequest = new AuthenticationResponse(accessToken, refreshToken);
        
        var json = JsonConvert.SerializeObject(refreshRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        Console.WriteLine("Refreshing token...");
        Console.WriteLine(json);
        Console.WriteLine(content);
        
        var response = await client.PostAsync(requestUrl, content);
        
        if (response.IsSuccessStatusCode)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            var refreshResponse = JsonConvert.DeserializeObject<AuthenticationResponse>(responseString);
            
            accessToken = refreshResponse.accessToken;
            refreshToken = refreshResponse.refreshToken;
            tokenExpiration = DateTime.Now.AddMinutes(4);

            Console.WriteLine("Token refresh successful.");
            Console.WriteLine($"Access Token: {accessToken}");
            Console.WriteLine($"Refresh Token: {refreshToken}");
        }
        else
        {
            Console.WriteLine((string?)$"Token refresh failed with status: {response.StatusCode}");
            var errorString = await response.Content.ReadAsStringAsync();
            Console.WriteLine(errorString);
            
            // If refresh fails due to authorization issues, perform fresh login
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                Console.WriteLine("Authorization failed during token refresh. Performing fresh login...");
                await HandleAuthorizationFailureAsync();
            }
        }
    }
    
    /// <summary>
    /// Handles authorization failures by performing a fresh login
    /// </summary>
    public static async Task HandleAuthorizationFailureAsync()
    {
        Console.WriteLine("Handling authorization failure - performing fresh login...");
        LoggedIn = false;
        accessToken = "";
        refreshToken = "";
        tokenExpiration = DateTime.MinValue;
        
        await AuthenticateAsync();
        
        if (!string.IsNullOrEmpty(accessToken))
        {
            LoggedIn = true;
            Console.WriteLine("Fresh login completed successfully.");
        }
        else
        {
            Console.WriteLine("Fresh login failed.");
        }
    }
    
    /// <summary>
    /// Checks if a response indicates authorization failure and handles it automatically
    /// </summary>
    /// <param name="response">The HTTP response to check</param>
    /// <returns>True if authorization failure was handled, false otherwise</returns>
    public static async Task<bool> HandleResponseAuthorizationAsync(HttpResponseMessage response)
    {
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            Console.WriteLine("Authorization failure detected. Performing fresh login...");
            await HandleAuthorizationFailureAsync();
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// Performs an HTTP GET request with automatic authorization retry
    /// </summary>
    /// <param name="requestUrl">The URL to send the GET request to</param>
    /// <returns>The HTTP response message</returns>
    public static async Task<HttpResponseMessage> AuthorizedGetAsync(string requestUrl)
    {
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            
            var response = await client.GetAsync(requestUrl);
            
            // Handle authorization failure and retry if needed
            if (await HandleResponseAuthorizationAsync(response))
            {
                // Update authorization header with new token and retry
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                response = await client.GetAsync(requestUrl);
            }
            
            return response;
        }
    }
    
    /// <summary>
    /// Performs an HTTP POST request with automatic authorization retry
    /// </summary>
    /// <param name="requestUrl">The URL to send the POST request to</param>
    /// <param name="content">The content to send in the POST request</param>
    /// <returns>The HTTP response message</returns>
    public static async Task<HttpResponseMessage> AuthorizedPostAsync(string requestUrl, HttpContent content)
    {
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            
            var response = await client.PostAsync(requestUrl, content);
            
            // Handle authorization failure and retry if needed
            if (await HandleResponseAuthorizationAsync(response))
            {
                // Update authorization header with new token and retry
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                response = await client.PostAsync(requestUrl, content);
            }
            
            return response;
        }
    }
    
    /// <summary>
    /// Performs an HTTP PATCH request with automatic authorization retry
    /// </summary>
    /// <param name="requestUrl">The URL to send the PATCH request to</param>
    /// <param name="content">The content to send in the PATCH request</param>
    /// <returns>The HTTP response message</returns>
    public static async Task<HttpResponseMessage> AuthorizedPatchAsync(string requestUrl, HttpContent content)
    {
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            
            var response = await client.PatchAsync(requestUrl, content);
            
            // Handle authorization failure and retry if needed
            if (await HandleResponseAuthorizationAsync(response))
            {
                // Update authorization header with new token and retry
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                response = await client.PatchAsync(requestUrl, content);
            }
            
            return response;
        }
    }
    
    /// <summary>
    /// Performs an HTTP DELETE request with automatic authorization retry
    /// </summary>
    /// <param name="requestUrl">The URL to send the DELETE request to</param>
    /// <returns>The HTTP response message</returns>
    public static async Task<HttpResponseMessage> AuthorizedDeleteAsync(string requestUrl)
    {
        using (var client = new HttpClient())
        {
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            
            var response = await client.DeleteAsync(requestUrl);
            
            // Handle authorization failure and retry if needed
            if (await HandleResponseAuthorizationAsync(response))
            {
                // Update authorization header with new token and retry
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                response = await client.DeleteAsync(requestUrl);
            }
            
            return response;
        }
    }
}