<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Quartz" Version="3.9.0" />
      <PackageReference Include="Quartz.Extensions.Hosting" Version="3.9.0" />
      <PackageReference Include="Quartz.Jobs" Version="3.9.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Contracts\Contracts.csproj" />
      <ProjectReference Include="..\Entities\Entities.csproj" />
      <ProjectReference Include="..\LoggerService\LoggerService.csproj" />
      <ProjectReference Include="..\Repository\Repository.csproj" />
      <ProjectReference Include="..\Service.Contracts\Service.Contracts.csproj" />
      <ProjectReference Include="..\Service\Service.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Quartz\QuartzHostedService.cs" />
    </ItemGroup>

</Project>
