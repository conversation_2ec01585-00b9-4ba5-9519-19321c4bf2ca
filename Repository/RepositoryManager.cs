using Contracts;
using Contracts.IModels;
using Entities.Models;
using Repository.ModelRepositories;

namespace Repository;

public sealed class RepositoryManager:IRepositoryManager
{
    private readonly RepositoryContext _repositoryContext;
    private readonly Lazy<IAradressdatenRepository> _aradressdatenRepository;
    private readonly Lazy<IArhauptdateiRepository> _arhaupdateiRepository;
    private readonly Lazy<IKundenRepository> _kundenRepository;
    private Lazy<IAuftragskopfRepository> _auftragskopfRepository;
    private Lazy<IArtikelRepository> _artikelRepository;
    private Lazy<IKontraktRepository> _kontraktRepository;
    private Lazy<IAuftragsposRepository> _auftragsposRepository;
    private Lazy<ITriggerLogRepository> _triggerLogRepository;

    public RepositoryManager(RepositoryContext repositoryContext)
    {
        _repositoryContext = repositoryContext;
        _aradressdatenRepository = new Lazy<IAradressdatenRepository>(() => new AradressdatenRepository(repositoryContext));
        _arhaupdateiRepository = new Lazy<IArhauptdateiRepository>(() => new ArhauptdateiRepository(repositoryContext));
        _kundenRepository = new Lazy<IKundenRepository>(() => new KundenRepository(repositoryContext));
        _auftragskopfRepository = new Lazy<IAuftragskopfRepository>(() => new AuftragskopfRepository(repositoryContext));
        _artikelRepository = new Lazy<IArtikelRepository>(() => new ArtikelRepository(repositoryContext));
        _kontraktRepository = new Lazy<IKontraktRepository>(() => new KontraktRepository(repositoryContext));
        _auftragsposRepository = new Lazy<IAuftragsposRepository>(() => new AuftragsposRepository(repositoryContext));
        _triggerLogRepository = new Lazy<ITriggerLogRepository>(() => new TriggerLogRepository(repositoryContext));
    }

    public IAradressdatenRepository Aradressdaten => _aradressdatenRepository.Value;
    public IArhauptdateiRepository Arhauptdatei => _arhaupdateiRepository.Value;
    public IKundenRepository Kunden => _kundenRepository.Value;
    public IArtikelRepository Artikel => _artikelRepository.Value;
    public IKontraktRepository Kontrakt => _kontraktRepository.Value;
    public IAuftragsposRepository Auftragspos => _auftragsposRepository.Value;
    public IAuftragskopfRepository Auftragskopf
    {
        get => _auftragskopfRepository.Value;
        set => _auftragskopfRepository = new Lazy<IAuftragskopfRepository>(() => value);
    }

    public ITriggerLogRepository TriggerLog
    {
        get => _triggerLogRepository.Value;
        set => _triggerLogRepository = new Lazy<ITriggerLogRepository>(() => value);
    }

    public void Save() => _repositoryContext.SaveChanges();
}