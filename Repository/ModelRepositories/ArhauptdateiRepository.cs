using Contracts.IModels;
using Entities.Common;
using Entities.Models;

namespace Repository.ModelRepositories;

public class ArhauptdateiRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Arhauptdatei>(repositoryContext), IArhauptdateiRepository
{
    public IEnumerable<Arhauptdatei> GetAllArhauptdateiFromInvoice( bool trackChanges,
                                                                    long invoiceNumber,
                                                                    PostingsSchema? schema) =>
                FindAll(trackChanges)
                        .Where(p => p.Renummer == invoiceNumber && 
                                    (schema == null || p.ErfSchema == schema));

}