using Contracts.IModels;
using Entities.Models;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Repository.ModelRepositories;

public class AuftragskopfRepository : RepositoryBase<Auftragskopf>, IAuftragskopfRepository
{
    private RepositoryContext _repositoryContext;

    public AuftragskopfRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
        _repositoryContext = repositoryContext;
        
    }

    public void WriteOrderToDb(bool trackChanges, Auftragskopf order)
    {
        try
        {
            // Disable triggers on Auftragskopfs table
            _repositoryContext.Database.ExecuteSqlRaw("DISABLE TRIGGER ALL ON Auftragskopf");

            if (trackChanges)
            {
                _repositoryContext.Auftragskopfs.Update(order);
            }
            else
            {
                _repositoryContext.Auftragskopfs.Add(order);
            }

            _repositoryContext.SaveChanges();
        }
        finally
        {
            // Re-enable triggers on Auftragskopfs table
            _repositoryContext.Database.ExecuteSqlRaw("ENABLE TRIGGER ALL ON Auftragskopf");
        }
    }


    public IEnumerable<Auftragskopf>? GetAuftragskopfById(int orderId, bool trackChanges)
    {
        return FindByCondition(c => c.Auftragsnummer.Equals(orderId), trackChanges)
            .ToList();
    }

    public List<long?> GetWatchedLieferscheine(bool trackChanges)
    {
        return FindByCondition(c => c.WatcherStatus != null 
                                    && c.WatcherStatus != "" 
                                    && c.Lieferschein == true, trackChanges

            )
            .Select(c => c.Auftragsnummer)
            .ToList();
    }
    
    public string GetOrderIdByAuftragsnummer(long auftragsnummer, bool trackChanges)
    {
        return FindByCondition(c => c.Auftragsnummer.Equals(auftragsnummer), trackChanges)
            .Select(c => c.WatcherStatus)
            .FirstOrDefault() ?? "";
    }

    public void DisableWatcher(List<long?> watchList)
    {
        if (watchList == null || !watchList.Any())
        {
            throw new ArgumentException("Watch list cannot be null or empty. Tried to disable watchers on no orders", nameof(watchList));
        }

        // Build a parameterized query
        var parameterizedQuery = "UPDATE Auftragskopf SET WatcherStatus = '' WHERE Auftragsnummer IN (" +
                                 string.Join(", ", watchList.Select((_, i) => $"@p{i}")) + ")";

        // Create parameters dynamically
        var parameters = watchList.Select((value, index) => 
            new SqlParameter($"@p{index}", value)).ToArray();

        // Execute the raw SQL query
        _repositoryContext.Database.ExecuteSqlRaw(parameterizedQuery, parameters);
    }

    public long? GetMaxAuftragsnummer()
    {
        return _repositoryContext.Auftragskopfs.Max(c => c.Auftragsnummer);
    }
}