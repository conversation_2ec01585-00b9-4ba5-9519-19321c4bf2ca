using Contracts.IModels;
using Entities.Models;

namespace Repository.ModelRepositories;

public class KundenRepository : RepositoryBase<Kunden>, IKundenRepository
{
    public RepositoryContext _repositoryContext;
    
    public KundenRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
    }

    public IEnumerable<Kunden> GetAllKunden(bool trackChanges) =>
        FindAll(trackChanges)
            .OrderBy(c => c.Kdname1)
            .ToList();
    
    public IEnumerable<Kunden> GetKundeById(long kundennummer, bool trackChanges) =>
        FindByCondition(c => c.Kdnummer.Equals(kundennummer), trackChanges)
            .ToList();

    public void WriteOrderToDb(bool trackChanges, Kunden kunde)
    {
        if (trackChanges)
        {
            _repositoryContext.Kundens.Update(kunde);
        }
        else
        {
            _repositoryContext.Kundens.Add(kunde);
        }

        _repositoryContext.SaveChanges();
    }
}