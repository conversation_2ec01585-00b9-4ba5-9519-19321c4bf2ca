using Contracts.IModels;
using Entities.Models;

namespace Repository.ModelRepositories;

public class AradressdatenRepository : RepositoryBase<Aradressdaten>, IAradressdatenRepository
{
    public AradressdatenRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
    }

    public IEnumerable<Aradressdaten> GetAllAradressdaten(bool trackChanges) =>
        FindAll(trackChanges)
            .OrderBy(c => c.Ardatum);
    
    public IEnumerable<Aradressdaten> GetAllAradressdaten(bool trackChanges, DateTime fromDate, DateTime toDate) =>
            FindAll(trackChanges)
                .Where(i=> i.Ardatum >= fromDate && i.Ardatum <= toDate)
                .OrderBy(c => c.Ardatum);
}