using System.Collections;
using Contracts.IModels;
using Entities.Models;

namespace Repository.ModelRepositories;

public class TriggerLogRepository: RepositoryBase<TriggerLog>, ITriggerLogRepository
{
    private readonly RepositoryContext _repositoryContext;
    public TriggerLogRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
        _repositoryContext = repositoryContext;
    }

    public TriggerLog? GetOldestTrigger(bool trackChanges)
    {
        return FindAll(trackChanges)
            .OrderBy(c => c.Timestamp)
            .FirstOrDefault(c => (bool)(!c.Done)!);
    }

    
    public TriggerLog? GetNewestTrigger(bool trackChanges)
    {
        return FindAll(trackChanges)
            .OrderByDescending(c => c.Timestamp)
            .FirstOrDefault(c => (bool)(!c.Done)!);
    }
    
    public void SetAsDone(TriggerLog triggerLog)
    {
        triggerLog.Done = true;
        Update(triggerLog);
        _repositoryContext.SaveChanges();
    }

    public IEnumerable<TriggerLog?> GetOpenTriggers(bool trackChanges)
    {
        return FindAll(trackChanges)
            .Where(c => (bool)(!c.Done)!);
    }
}