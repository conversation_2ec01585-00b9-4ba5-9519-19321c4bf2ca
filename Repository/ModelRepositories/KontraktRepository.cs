using Contracts.IModels;
using Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace Repository.ModelRepositories;

public class KontraktRepository : RepositoryBase<Kontrakt>, IKontraktRepository
{
    public RepositoryContext _repositoryContext;
    
    public KontraktRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
        _repositoryContext = repositoryContext;
    }
    
    public IEnumerable<Kontrakt> GetKontraktById(int kontraktId, bool trackChanges)
    {
        Console.WriteLine($"KontraktRepository: Searching for contract with ID {kontraktId}");
        
        var result = FindByCondition(c => c.Ktnr.Equals(kontraktId), trackChanges).ToList();
        
        Console.WriteLine($"KontraktRepository: Found {result.Count} contracts with ID {kontraktId}");
        
        return result;
    }

    public IEnumerable<Kontrakt> GetKontrakteByKundeId(int kundeId, bool trackChanges)
    {
        return FindByCondition(c => c.KtliefNr.Equals(kundeId), trackChanges)
            .Where(c => c.Ktek == false)
            .Where(c => c.Kterledigt == false)
            .ToList();
    }

    public void ChangeKontraktAmount(Kontrakt kontrakt, float amount)
    {
        _repositoryContext.Database.ExecuteSqlRaw("DISABLE TRIGGER ALL ON Kontrakt");
        
        Kontrakt kontraktToChange = GetKontraktById((int)kontrakt.Ktnr, true).FirstOrDefault();
        
        kontraktToChange.KtabgerMenge += amount;
        kontraktToChange.KtrestMenge -= amount;
        _repositoryContext.Kontrakts.Update(kontraktToChange);
        _repositoryContext.SaveChanges();
        
        _repositoryContext.Database.ExecuteSqlRaw("ENABLE TRIGGER ALL ON Kontrakt");
    }
}