using Contracts.IModels;
using Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace Repository.ModelRepositories;

public class AuftragsposRepository : RepositoryBase<Auftragspo>, IAuftragsposRepository
{
    private RepositoryContext _repositoryContext;

    public AuftragsposRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
        _repositoryContext = repositoryContext;
        
    }

    public IEnumerable<Auftragspo>? GetAuftragsposById(int orderId, bool trackChanges)
    {
        return FindByCondition(c => c.Auftragsnummer.Equals(orderId), trackChanges)
            .ToList();
    }

    public void WriteOrderItemToDb(bool trackChanges, Auftragspo item)
    {
        try
        {
            // Disable triggers on Auftragspos table
            _repositoryContext.Database.ExecuteSqlRaw("DISABLE TRIGGER ALL ON Auftragspos");

            if (trackChanges)
            {
                _repositoryContext.Auftragspos.Update(item);
            }
            else
            {
                _repositoryContext.Auftragspos.Add(item);
            }

            _repositoryContext.SaveChanges();
        }
        finally
        {
            // Re-enable triggers on Auftragspos table
            _repositoryContext.Database.ExecuteSqlRaw("ENABLE TRIGGER ALL ON Auftragspos");
        }
    }

}