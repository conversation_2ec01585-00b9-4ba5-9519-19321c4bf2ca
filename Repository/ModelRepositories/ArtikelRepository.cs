using Contracts.IModels;
using Entities.Models;

namespace Repository.ModelRepositories;

public class ArtikelRepository : RepositoryBase<Artikel>, IArtikelRepository
{
    
    public RepositoryContext _repositoryContext;
    public ArtikelRepository(RepositoryContext repositoryContext) : base(repositoryContext)
    {
    }

    public IEnumerable<Artikel> GetArtikelById(long artikelnummer, bool trackBack)
    {
        return FindByCondition(c => c.Artikelnr.Equals(artikelnummer), trackBack)
            .ToList();
    }

    public IEnumerable<Artikel> GetAllArtikel(bool trackBack)
    {
        return FindAll(trackBack)
            .Where(a => !string.IsNullOrEmpty(a.ArtBezText1) && 
                        !string.IsNullOrEmpty(a.ArtSbg) && 
                        !string.IsNullOrEmpty(a.ArtBezugsgr) && 
                        a.ArtVkpreis.HasValue)
            .ToList();
    }

}