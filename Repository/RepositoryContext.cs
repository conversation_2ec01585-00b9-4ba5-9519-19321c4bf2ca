using Microsoft.EntityFrameworkCore;
using Entities.Models;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Entities.Common;

namespace Repository;

public partial class RepositoryContext : DbContext
{
    public RepositoryContext()
    {
    }

    public RepositoryContext(DbContextOptions<RepositoryContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AbrWeg> AbrWegs { get; set; }

    public virtual DbSet<Agbvadr> Agbvadrs { get; set; }

    public virtual DbSet<Agbvhpt> Agbvhpts { get; set; }

    public virtual DbSet<Agparam> Agparams { get; set; }

    public virtual DbSet<AgparamNo> AgparamNos { get; set; }

    public virtual DbSet<AgparamNz> AgparamNzs { get; set; }

    public virtual DbSet<AgparamVz> AgparamVzs { get; set; }

    public virtual DbSet<AnzHauptdatei> AnzHauptdateis { get; set; }

    public virtual DbSet<AnzV> AnzVs { get; set; }

    public virtual DbSet<Anzahlungskonto> Anzahlungskontos { get; set; }

    public virtual DbSet<Aradressdaten> Aradressdatens { get; set; }

    public virtual DbSet<ArechnSp> ArechnSps { get; set; }

    public virtual DbSet<Argbvadr> Argbvadrs { get; set; }

    public virtual DbSet<Argbvhpt> Argbvhpts { get; set; }

    public virtual DbSet<Arhauptdatei> Arhauptdateis { get; set; }

    public virtual DbSet<ArtGruppe> ArtGruppes { get; set; }

    public virtual DbSet<ArtIdProz> ArtIdProzs { get; set; }

    public virtual DbSet<ArtIdStamm> ArtIdStamms { get; set; }

    public virtual DbSet<ArtIdVp> ArtIdVps { get; set; }

    public virtual DbSet<ArtInv> ArtInvs { get; set; }

    public virtual DbSet<ArtKtoAbschl> ArtKtoAbschls { get; set; }

    public virtual DbSet<ArtKtoDt> ArtKtoDts { get; set; }

    public virtual DbSet<ArtKtoE> ArtKtoEs { get; set; }

    public virtual DbSet<ArtKtoNr> ArtKtoNrs { get; set; }

    public virtual DbSet<ArtLager> ArtLagers { get; set; }

    public virtual DbSet<ArtVorgAlt> ArtVorgAlts { get; set; }

    public virtual DbSet<ArtVorgang> ArtVorgangs { get; set; }

    public virtual DbSet<Artikel> Artikels { get; set; }

    public virtual DbSet<Arvrabatt> Arvrabatts { get; set; }

    public virtual DbSet<AuftragsSp> AuftragsSps { get; set; }

    public virtual DbSet<Auftragskopf> Auftragskopfs { get; set; }

    public virtual DbSet<Auftragspo> Auftragspos { get; set; }

    public virtual DbSet<Awg> Awgs { get; set; }

    public virtual DbSet<Awgslab> Awgslabs { get; set; }

    public virtual DbSet<Bank> Banks { get; set; }

    public virtual DbSet<Blskopf> Blskopfs { get; set; }

    public virtual DbSet<Blspo> Blspos { get; set; }

    public virtual DbSet<ChargTbl> ChargTbls { get; set; }

    public virtual DbSet<Chargen> Chargens { get; set; }

    public virtual DbSet<Dbfstat> Dbfstats { get; set; }

    public virtual DbSet<DepStamm> DepStamms { get; set; }

    public virtual DbSet<Display> Displays { get; set; }

    public virtual DbSet<Dpfreist> Dpfreists { get; set; }

    public virtual DbSet<Elskopf> Elskopfs { get; set; }

    public virtual DbSet<Elspo> Elspos { get; set; }

    public virtual DbSet<Erhauptdatei> Erhauptdateis { get; set; }

    public virtual DbSet<Erv> Ervs { get; set; }

    public virtual DbSet<Estrecke> Estreckes { get; set; }

    public virtual DbSet<Etabelle> Etabelles { get; set; }

    public virtual DbSet<Ezg> Ezgs { get; set; }

    public virtual DbSet<FbezKonto> FbezKontos { get; set; }

    public virtual DbSet<Fbkonto> Fbkontos { get; set; }

    public virtual DbSet<Frachtzonen> Frachtzonens { get; set; }

    public virtual DbSet<Gbvadr> Gbvadrs { get; set; }

    public virtual DbSet<Gbvhpt> Gbvhpts { get; set; }

    public virtual DbSet<Getreideparameter> Getreideparameters { get; set; }

    public virtual DbSet<Gparam> Gparams { get; set; }

    public virtual DbSet<GparamNo> GparamNos { get; set; }

    public virtual DbSet<GparamNz> GparamNzs { get; set; }

    public virtual DbSet<GparamVz> GparamVzs { get; set; }

    public virtual DbSet<GparamWg> GparamWgs { get; set; }

    public virtual DbSet<Gpfad> Gpfads { get; set; }

    public virtual DbSet<Gtabelle> Gtabelles { get; set; }

    public virtual DbSet<IntraStat> IntraStats { get; set; }

    public virtual DbSet<Kanalyse> Kanalyses { get; set; }

    public virtual DbSet<KasAb> KasAbs { get; set; }

    public virtual DbSet<KasHpt> KasHpts { get; set; }

    public virtual DbSet<KasHptAb> KasHptAbs { get; set; }

    public virtual DbSet<KasHptTse> KasHptTses { get; set; }

    public virtual DbSet<KassHandArtikel> KassHandArtikels { get; set; }

    public virtual DbSet<Kbetreuer> Kbetreuers { get; set; }

    public virtual DbSet<KdbranchenKennung> KdbranchenKennungs { get; set; }

    public virtual DbSet<Kdlprei> Kdlpreis { get; set; }

    public virtual DbSet<KdstatEuroRe> KdstatEuroRes { get; set; }

    public virtual DbSet<KdstatEuroWe> KdstatEuroWes { get; set; }

    public virtual DbSet<KontrZu> KontrZus { get; set; }

    public virtual DbSet<Kontrakt> Kontrakts { get; set; }

    public virtual DbSet<Kpartner> Kpartners { get; set; }

    public virtual DbSet<Kprotokoll> Kprotokolls { get; set; }

    public virtual DbSet<Ktexte> Ktextes { get; set; }

    public virtual DbSet<KtrEk> KtrEks { get; set; }

    public virtual DbSet<KtrMail> KtrMails { get; set; }

    public virtual DbSet<KtrVk> KtrVks { get; set; }

    public virtual DbSet<KundInfo> KundInfos { get; set; }

    public virtual DbSet<KundRabatte> KundRabattes { get; set; }

    public virtual DbSet<KundStatJahrRe> KundStatJahrRes { get; set; }

    public virtual DbSet<KundStatJahrWe> KundStatJahrWes { get; set; }

    public virtual DbSet<Kunden> Kundens { get; set; }

    public virtual DbSet<Lagerbestand> Lagerbestands { get; set; }

    public virtual DbSet<Lagerstelle> Lagerstelles { get; set; }

    public virtual DbSet<Leergut> Leerguts { get; set; }

    public virtual DbSet<Lfartikel> Lfartikels { get; set; }

    public virtual DbSet<LfgetrPrei> LfgetrPreis { get; set; }

    public virtual DbSet<LfpreisOffert> LfpreisOfferts { get; set; }

    public virtual DbSet<Lieferanten> Lieferantens { get; set; }

    public virtual DbSet<MengenstaffelKu> MengenstaffelKus { get; set; }

    public virtual DbSet<MibuProz> MibuProzs { get; set; }

    public virtual DbSet<MibuStamm> MibuStamms { get; set; }

    public virtual DbSet<MibuVp> MibuVps { get; set; }

    public virtual DbSet<MstPo> MstPos { get; set; }

    public virtual DbSet<Mstrecke> Mstreckes { get; set; }

    public virtual DbSet<Nvoartikel> Nvoartikels { get; set; }

    public virtual DbSet<Nvobilanz> Nvobilanzs { get; set; }

    public virtual DbSet<NvodepotBilanz> NvodepotBilanzs { get; set; }

    public virtual DbSet<Nvokonto> Nvokontos { get; set; }

    public virtual DbSet<OptLexware> OptLexwares { get; set; }

    public virtual DbSet<Optionen> Optionens { get; set; }

    public virtual DbSet<Optionen2> Optionen2s { get; set; }

    public virtual DbSet<Order> Orders { get; set; }

    public virtual DbSet<Preislisten> Preislistens { get; set; }

    public virtual DbSet<Prlkopf> Prlkopfs { get; set; }

    public virtual DbSet<ProdSpez> ProdSpezs { get; set; }

    public virtual DbSet<ProdSpezAnalyse> ProdSpezAnalyses { get; set; }

    public virtual DbSet<ProdSpezNaehrwert> ProdSpezNaehrwerts { get; set; }

    public virtual DbSet<RabTab> RabTabs { get; set; }

    public virtual DbSet<Rgbvadr> Rgbvadrs { get; set; }

    public virtual DbSet<Rgbvhpt> Rgbvhpts { get; set; }

    public virtual DbSet<Satlog> Satlogs { get; set; }

    public virtual DbSet<SiloAutomatik> SiloAutomatiks { get; set; }

    public virtual DbSet<SiloFach> SiloFaches { get; set; }

    public virtual DbSet<SiloLab> SiloLabs { get; set; }

    public virtual DbSet<SiloProfil> SiloProfils { get; set; }

    public virtual DbSet<SiloRezept> SiloRezepts { get; set; }

    public virtual DbSet<SiloUmb> SiloUmbs { get; set; }

    public virtual DbSet<SiloVorgAlt> SiloVorgAlts { get; set; }

    public virtual DbSet<SiloVorgang> SiloVorgangs { get; set; }

    public virtual DbSet<SiloWg> SiloWgs { get; set; }

    public virtual DbSet<SiloWgsent> SiloWgsents { get; set; }

    public virtual DbSet<Spezialrabatte> Spezialrabattes { get; set; }

    public virtual DbSet<TabIndex> TabIndices { get; set; }

    public virtual DbSet<Tabellenkopf> Tabellenkopfs { get; set; }

    public virtual DbSet<TblGutschein> TblGutscheins { get; set; }
    
    public virtual DbSet<TriggerLog> TriggerLogs { get; set; }

    public virtual DbSet<TseDatum> TseData { get; set; }

    public virtual DbSet<TseDevice> TseDevices { get; set; }

    public virtual DbSet<TseTransakt> TseTransakts { get; set; }

    public virtual DbSet<UmbListe> UmbListes { get; set; }

    public virtual DbSet<Umbkopf> Umbkopfs { get; set; }

    public virtual DbSet<Umbpo> Umbpos { get; set; }

    public virtual DbSet<UmschlAnz> UmschlAnzs { get; set; }

    public virtual DbSet<UmschlKopf> UmschlKopfs { get; set; }

    public virtual DbSet<Vertreter> Vertreters { get; set; }

    public virtual DbSet<Vstaffel> Vstaffels { get; set; }

    public virtual DbSet<Wg> Wgs { get; set; }

    public virtual DbSet<WgschargTbl> WgschargTbls { get; set; }

    public virtual DbSet<Wgslab> Wgslabs { get; set; }

    public virtual DbSet<Wrkopf> Wrkopfs { get; set; }

    public virtual DbSet<Wrpo> Wrpos { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("Latin1_General_CI_AS");

        modelBuilder.Entity<AbrWeg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AbrWeg$ID");

            entity.ToTable("AbrWeg");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AbrWeg1)
                .HasMaxLength(60)
                .HasColumnName("AbrWeg");
            entity.Property(e => e.AbrWegNr)
                .HasDefaultValue((short)0)
                .HasColumnName("AbrWegNR");
        });

        modelBuilder.Entity<Agbvadr>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGBVADR$ID");

            entity.ToTable("AGBVADR");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AnzBrutto).HasColumnType("money");
            entity.Property(e => e.AnzNetto).HasColumnType("money");
            entity.Property(e => e.AnzRestOffen).HasColumnType("money");
            entity.Property(e => e.AnzStartBrutto).HasColumnType("money");
            entity.Property(e => e.AnzStartNetto).HasColumnType("money");
            entity.Property(e => e.BioNr).HasMaxLength(50);
            entity.Property(e => e.Gbabfall)
                .HasColumnType("money")
                .HasColumnName("GBAbfall");
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzgBetr1)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr1");
            entity.Property(e => e.GbabzgBetr2)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr2");
            entity.Property(e => e.GbabzgBetr3)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr3");
            entity.Property(e => e.GbabzgBetr4)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr4");
            entity.Property(e => e.GbabzgBetr5)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr5");
            entity.Property(e => e.GbabzgText1)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText1");
            entity.Property(e => e.GbabzgText2)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText2");
            entity.Property(e => e.GbabzgText3)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText3");
            entity.Property(e => e.GbabzgText4)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText4");
            entity.Property(e => e.GbabzgText5)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText5");
            entity.Property(e => e.GbabzgVz1)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ1");
            entity.Property(e => e.GbabzgVz2)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ2");
            entity.Property(e => e.GbabzgVz3)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ3");
            entity.Property(e => e.GbabzgVz4)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ4");
            entity.Property(e => e.GbabzgVz5)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ5");
            entity.Property(e => e.Gbabzug)
                .HasColumnType("money")
                .HasColumnName("GBAbzug");
            entity.Property(e => e.Gbaltnr).HasColumnName("GBAltnr");
            entity.Property(e => e.GbangAbfall)
                .HasColumnType("money")
                .HasColumnName("GBAngAbfall");
            entity.Property(e => e.Gbanlfnr).HasColumnName("GBANLFNR");
            entity.Property(e => e.Gbanrede)
                .HasMaxLength(30)
                .HasColumnName("GBAnrede");
            entity.Property(e => e.Gbart).HasColumnName("GBArt");
            entity.Property(e => e.Gbasld).HasColumnName("GBASLD");
            entity.Property(e => e.Gbbank)
                .HasMaxLength(30)
                .HasColumnName("GBBank");
            entity.Property(e => e.Gbbld)
                .HasMaxLength(30)
                .HasColumnName("GBBLD");
            entity.Property(e => e.Gbbldkng).HasColumnName("GBBLDKng");
            entity.Property(e => e.Gbblz)
                .HasMaxLength(10)
                .HasColumnName("GBBLZ");
            entity.Property(e => e.Gbbrutto)
                .HasColumnType("money")
                .HasColumnName("GBBrutto");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.GbdruckKz)
                .HasMaxLength(1)
                .HasColumnName("GBDruckKz");
            entity.Property(e => e.GbentfKl)
                .HasMaxLength(2)
                .HasColumnName("GBEntfKL");
            entity.Property(e => e.GbentfKlzuEur)
                .HasColumnType("money")
                .HasColumnName("GBEntfKLZuEUR");
            entity.Property(e => e.Gbezg)
                .HasDefaultValue(false)
                .HasColumnName("GBEZG");
            entity.Property(e => e.Gbezgbetr)
                .HasColumnType("money")
                .HasColumnName("GBEZGBetr");
            entity.Property(e => e.Gbfax)
                .HasMaxLength(20)
                .HasColumnName("GBFax");
            entity.Property(e => e.Gbfibu2)
                .HasDefaultValue(false)
                .HasColumnName("GBFibu2");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.GbgesMenge)
                .HasColumnType("money")
                .HasColumnName("GBGesMenge");
            entity.Property(e => e.Gbhaendler)
                .HasDefaultValue(false)
                .HasColumnName("GBHaendler");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(20)
                .HasColumnName("GBKonto");
            entity.Property(e => e.Gbktnr).HasColumnName("GBKTNr");
            entity.Property(e => e.GbktrNrLf)
                .HasMaxLength(10)
                .HasColumnName("GBKtrNrLF");
            entity.Property(e => e.Gbland)
                .HasMaxLength(30)
                .HasColumnName("GBLand");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblfstatus)
                .HasMaxLength(15)
                .HasColumnName("GBLFStatus");
            entity.Property(e => e.GbmitText1)
                .HasMaxLength(60)
                .HasColumnName("GBMitText1");
            entity.Property(e => e.GbmitText2)
                .HasMaxLength(60)
                .HasColumnName("GBMitText2");
            entity.Property(e => e.Gbmwst)
                .HasColumnType("money")
                .HasColumnName("GBMwst");
            entity.Property(e => e.Gbnachzhlg)
                .HasMaxLength(1)
                .HasColumnName("GBNachzhlg");
            entity.Property(e => e.Gbname1)
                .HasMaxLength(30)
                .HasColumnName("GBName1");
            entity.Property(e => e.Gbname2)
                .HasMaxLength(30)
                .HasColumnName("GBName2");
            entity.Property(e => e.Gbname3)
                .HasMaxLength(30)
                .HasColumnName("GBName3");
            entity.Property(e => e.Gbnetto)
                .HasColumnType("money")
                .HasColumnName("GBNetto");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.Gbort)
                .HasMaxLength(30)
                .HasColumnName("GBOrt");
            entity.Property(e => e.Gbplz)
                .HasMaxLength(10)
                .HasColumnName("GBPLZ");
            entity.Property(e => e.GbreNrLf)
                .HasMaxLength(25)
                .HasColumnName("GBReNrLF");
            entity.Property(e => e.Gbsbg)
                .HasMaxLength(10)
                .HasColumnName("GBSBG");
            entity.Property(e => e.GbstNr)
                .HasMaxLength(20)
                .HasColumnName("GBStNr");
            entity.Property(e => e.GbstProz)
                .HasColumnType("money")
                .HasColumnName("GBStProz");
            entity.Property(e => e.GbstProz2).HasColumnName("GBStProz2");
            entity.Property(e => e.GbstSchl).HasColumnName("GBStSchl");
            entity.Property(e => e.Gbstorno)
                .HasMaxLength(1)
                .HasColumnName("GBStorno");
            entity.Property(e => e.Gbstrasse)
                .HasMaxLength(30)
                .HasColumnName("GBStrasse");
            entity.Property(e => e.Gbtelefon)
                .HasMaxLength(20)
                .HasColumnName("GBTelefon");
            entity.Property(e => e.Gbustidnr)
                .HasMaxLength(18)
                .HasColumnName("GBUSTIDNR");
            entity.Property(e => e.Gbvaluta)
                .HasMaxLength(60)
                .HasColumnName("GBValuta");
            entity.Property(e => e.GbvalutaDatum)
                .HasColumnType("datetime")
                .HasColumnName("GBValutaDatum");
            entity.Property(e => e.Gbvwhrg)
                .HasMaxLength(4)
                .HasColumnName("GBVWhrg");
            entity.Property(e => e.GbvwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("GBVWhrgKurs");
            entity.Property(e => e.GbzahlBetrag)
                .HasColumnType("money")
                .HasColumnName("GBZahlBetrag");
            entity.Property(e => e.GbzuschlEur)
                .HasColumnType("money")
                .HasColumnName("GBZuschlEUR");
            entity.Property(e => e.GbzuschlSumme)
                .HasColumnType("money")
                .HasColumnName("GBZuschlSumme");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Agbvhpt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGBVHPT$ID");

            entity.ToTable("AGBVHPT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BuchMenge).HasColumnType("money");
            entity.Property(e => e.ErfSchema).HasMaxLength(2);
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzGew)
                .HasColumnType("money")
                .HasColumnName("GBAbzGew");
            entity.Property(e => e.GbanlGew)
                .HasColumnType("money")
                .HasColumnName("GBAnlGew");
            entity.Property(e => e.GbartNr).HasColumnName("GBArtNr");
            entity.Property(e => e.Gbep)
                .HasColumnType("money")
                .HasColumnName("GBEP");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.Gbfracht)
                .HasColumnType("money")
                .HasColumnName("GBFracht");
            entity.Property(e => e.GbgesNetto)
                .HasColumnType("money")
                .HasColumnName("GBGesNetto");
            entity.Property(e => e.GbgetrKng)
                .HasMaxLength(3)
                .HasColumnName("GBGetrKng");
            entity.Property(e => e.GbgridPos).HasColumnName("GBGridPos");
            entity.Property(e => e.Gbhartikel).HasColumnName("GBHArtikel");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(10)
                .HasColumnName("GBKonto");
            entity.Property(e => e.GbkontrNr).HasColumnName("GBKontrNr");
            entity.Property(e => e.Gbkz)
                .HasMaxLength(10)
                .HasColumnName("GBKz");
            entity.Property(e => e.Gblbeh)
                .HasMaxLength(5)
                .HasColumnName("GBLBEH");
            entity.Property(e => e.Gblbwert)
                .HasColumnType("money")
                .HasColumnName("GBLBWert");
            entity.Property(e => e.Gblkw)
                .HasMaxLength(15)
                .HasColumnName("GBLKW");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.GbstProz).HasColumnName("GBStProz");
            entity.Property(e => e.GbstreckNr).HasColumnName("GBStreckNr");
            entity.Property(e => e.Gbtext)
                .HasMaxLength(255)
                .HasColumnName("GBText");
            entity.Property(e => e.Gbvznr).HasColumnName("GBVZNr");
            entity.Property(e => e.GbwgsNr).HasColumnName("GBWgsNr");
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.Tp)
                .HasDefaultValue(false)
                .HasColumnName("TP");
        });

        modelBuilder.Entity<Agparam>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGParam$ID");

            entity.ToTable("AGParam");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abrgesp).HasDefaultValue(false);
            entity.Property(e => e.Bezeichnung).HasMaxLength(18);
            entity.Property(e => e.Kennung).HasMaxLength(3);
            entity.Property(e => e.Wgs)
                .HasMaxLength(4)
                .HasColumnName("WGS");
            entity.Property(e => e.WgsBezeichnung1).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung10).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung11).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung12).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung2).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung3).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung4).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung5).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung6).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung7).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung8).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung9).HasMaxLength(18);
            entity.Property(e => e.WgsEinh1).HasMaxLength(3);
            entity.Property(e => e.WgsEinh10).HasMaxLength(3);
            entity.Property(e => e.WgsEinh11).HasMaxLength(3);
            entity.Property(e => e.WgsEinh12).HasMaxLength(3);
            entity.Property(e => e.WgsEinh2).HasMaxLength(3);
            entity.Property(e => e.WgsEinh3).HasMaxLength(3);
            entity.Property(e => e.WgsEinh4).HasMaxLength(3);
            entity.Property(e => e.WgsEinh5).HasMaxLength(3);
            entity.Property(e => e.WgsEinh6).HasMaxLength(3);
            entity.Property(e => e.WgsEinh7).HasMaxLength(3);
            entity.Property(e => e.WgsEinh8).HasMaxLength(3);
            entity.Property(e => e.WgsEinh9).HasMaxLength(3);
            entity.Property(e => e.Wgskennung1)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung1");
            entity.Property(e => e.Wgskennung10)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung10");
            entity.Property(e => e.Wgskennung11)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung11");
            entity.Property(e => e.Wgskennung12)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung12");
            entity.Property(e => e.Wgskennung2)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung2");
            entity.Property(e => e.Wgskennung3)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung3");
            entity.Property(e => e.Wgskennung4)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung4");
            entity.Property(e => e.Wgskennung5)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung5");
            entity.Property(e => e.Wgskennung6)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung6");
            entity.Property(e => e.Wgskennung7)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung7");
            entity.Property(e => e.Wgskennung8)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung8");
            entity.Property(e => e.Wgskennung9)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung9");
        });

        modelBuilder.Entity<AgparamNo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGParamNO$ID");

            entity.ToTable("AGParamNO");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<AgparamNz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGParamNZ$ID");

            entity.ToTable("AGParamNZ");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<AgparamVz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AGParamVZ$ID");

            entity.ToTable("AGParamVZ");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<AnzHauptdatei>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AnzHauptdatei$ID");

            entity.ToTable("AnzHauptdatei");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteElsmenge).HasColumnName("AlteELSMenge");
            entity.Property(e => e.AnzLspos).HasColumnName("AnzLSPos");
            entity.Property(e => e.Bezeichnung).HasMaxLength(255);
            entity.Property(e => e.Bld).HasColumnName("BLD");
            entity.Property(e => e.BzgGr).HasMaxLength(10);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Einzelpreis).HasColumnType("money");
            entity.Property(e => e.ErfSchema).HasMaxLength(2);
            entity.Property(e => e.ErkaKu)
                .HasColumnType("money")
                .HasColumnName("ERKaKu");
            entity.Property(e => e.Fracht).HasColumnType("money");
            entity.Property(e => e.Gesamtmenge).HasColumnType("money");
            entity.Property(e => e.Gesamtnetto).HasColumnType("money");
            entity.Property(e => e.Gr)
                .HasColumnType("money")
                .HasColumnName("GR");
            entity.Property(e => e.Hartikel).HasColumnName("HArtikel");
            entity.Property(e => e.KalkuKo).HasColumnType("money");
            entity.Property(e => e.KdNrWe).HasColumnName("KdNrWE");
            entity.Property(e => e.Kgrund)
                .HasMaxLength(10)
                .HasColumnName("KGrund");
            entity.Property(e => e.KnEzZuschlag).HasMaxLength(1);
            entity.Property(e => e.Konto).HasMaxLength(10);
            entity.Property(e => e.KontraktKng).HasMaxLength(1);
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.Lsnr).HasColumnName("LSNr");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.MwstProz).HasColumnType("money");
            entity.Property(e => e.Redatum)
                .HasColumnType("datetime")
                .HasColumnName("REDatum");
            entity.Property(e => e.Renummer).HasColumnName("RENummer");
            entity.Property(e => e.Status).HasMaxLength(1);
            entity.Property(e => e.Vdmproz)
                .HasMaxLength(1)
                .HasColumnName("VDMProz");
            entity.Property(e => e.Vez)
                .HasColumnType("money")
                .HasColumnName("VEZ");
            entity.Property(e => e.Vxz)
                .HasColumnType("money")
                .HasColumnName("VXZ");
            entity.Property(e => e.Zustellart).HasMaxLength(25);
        });

        modelBuilder.Entity<AnzV>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AnzV$ID");

            entity.ToTable("AnzV");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Anzahlung).HasDefaultValue(false);
            entity.Property(e => e.Eingang).HasDefaultValue(false);
            entity.Property(e => e.EralteNr).HasColumnName("ERAlteNr");
            entity.Property(e => e.Eranrede)
                .HasMaxLength(30)
                .HasColumnName("ERAnrede");
            entity.Property(e => e.Erasld)
                .HasMaxLength(2)
                .HasColumnName("ERASLD");
            entity.Property(e => e.Erbank)
                .HasMaxLength(30)
                .HasColumnName("ERBank");
            entity.Property(e => e.Erbemerkung).HasColumnName("ERBemerkung");
            entity.Property(e => e.Erbld)
                .HasMaxLength(21)
                .HasColumnName("ERBLD");
            entity.Property(e => e.Erbldnr).HasColumnName("ERBLDNr");
            entity.Property(e => e.Erblz)
                .HasMaxLength(8)
                .HasColumnName("ERBLZ");
            entity.Property(e => e.Erbrutto1)
                .HasColumnType("money")
                .HasColumnName("ERBrutto1");
            entity.Property(e => e.Erbrutto2)
                .HasColumnType("money")
                .HasColumnName("ERBrutto2");
            entity.Property(e => e.Erdatum)
                .HasColumnType("datetime")
                .HasColumnName("ERDatum");
            entity.Property(e => e.Erfibu2)
                .HasDefaultValue(false)
                .HasColumnName("ERFibu2");
            entity.Property(e => e.ErfrachtRe).HasColumnName("ERFrachtRE");
            entity.Property(e => e.ErgesBrutto)
                .HasColumnType("money")
                .HasColumnName("ERGesBrutto");
            entity.Property(e => e.ErgesMenge).HasColumnName("ERGesMenge");
            entity.Property(e => e.ErgesMwst)
                .HasColumnType("money")
                .HasColumnName("ERGesMwst");
            entity.Property(e => e.ErgesNetto)
                .HasColumnType("money")
                .HasColumnName("ERGesNetto");
            entity.Property(e => e.Erkontonr)
                .HasMaxLength(12)
                .HasColumnName("ERKontonr");
            entity.Property(e => e.Erland)
                .HasMaxLength(30)
                .HasColumnName("ERLand");
            entity.Property(e => e.Erlfnr).HasColumnName("ERLFNr");
            entity.Property(e => e.Ermwst1E)
                .HasColumnType("money")
                .HasColumnName("ERMwst1E");
            entity.Property(e => e.Ermwst1P)
                .HasColumnType("money")
                .HasColumnName("ERMwst1P");
            entity.Property(e => e.Ermwst2E)
                .HasColumnType("money")
                .HasColumnName("ERMwst2E");
            entity.Property(e => e.Ermwst2P)
                .HasColumnType("money")
                .HasColumnName("ERMwst2P");
            entity.Property(e => e.Ername1)
                .HasMaxLength(30)
                .HasColumnName("ERName1");
            entity.Property(e => e.Ername2)
                .HasMaxLength(30)
                .HasColumnName("ERName2");
            entity.Property(e => e.Ername3)
                .HasMaxLength(30)
                .HasColumnName("ERName3");
            entity.Property(e => e.Ernetto1)
                .HasColumnType("money")
                .HasColumnName("ERNetto1");
            entity.Property(e => e.Ernetto2)
                .HasColumnType("money")
                .HasColumnName("ERNetto2");
            entity.Property(e => e.ErnettoTage).HasColumnName("ERNettoTage");
            entity.Property(e => e.Ernummer).HasColumnName("ERNummer");
            entity.Property(e => e.Erort)
                .HasMaxLength(30)
                .HasColumnName("EROrt");
            entity.Property(e => e.Erplz)
                .HasMaxLength(10)
                .HasColumnName("ERPLZ");
            entity.Property(e => e.Errebetrag)
                .HasColumnType("money")
                .HasColumnName("ERREBetrag");
            entity.Property(e => e.Errelf)
                .HasMaxLength(20)
                .HasColumnName("ERRELF");
            entity.Property(e => e.Ersbg)
                .HasMaxLength(10)
                .HasColumnName("ERSBG");
            entity.Property(e => e.Erskto1E)
                .HasColumnType("money")
                .HasColumnName("ERSkto1E");
            entity.Property(e => e.Erskto1P)
                .HasColumnType("money")
                .HasColumnName("ERSkto1P");
            entity.Property(e => e.Erskto1T).HasColumnName("ERSkto1T");
            entity.Property(e => e.Erskto2E)
                .HasColumnType("money")
                .HasColumnName("ERSkto2E");
            entity.Property(e => e.Erskto2P)
                .HasColumnType("money")
                .HasColumnName("ERSkto2P");
            entity.Property(e => e.Erskto2T).HasColumnName("ERSkto2T");
            entity.Property(e => e.ErstNr)
                .HasMaxLength(20)
                .HasColumnName("ERStNr");
            entity.Property(e => e.Erstatus).HasColumnName("ERStatus");
            entity.Property(e => e.ErstornoKennung)
                .HasMaxLength(1)
                .HasColumnName("ERStornoKennung");
            entity.Property(e => e.Erstrasse)
                .HasMaxLength(30)
                .HasColumnName("ERStrasse");
            entity.Property(e => e.ErstreckenNr).HasColumnName("ERStreckenNr");
            entity.Property(e => e.ErumrFakt).HasColumnName("ERUmrFakt");
            entity.Property(e => e.Erustidnr)
                .HasMaxLength(20)
                .HasColumnName("ERUSTIDNR");
            entity.Property(e => e.ErvertrNr).HasColumnName("ERVertrNr");
            entity.Property(e => e.ErwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("ERWhrgKurs");
            entity.Property(e => e.Erwru)
                .HasMaxLength(4)
                .HasColumnName("ERWRU");
            entity.Property(e => e.Erzustellart)
                .HasMaxLength(25)
                .HasColumnName("ERZustellart");
            entity.Property(e => e.Erzz1)
                .HasMaxLength(60)
                .HasColumnName("ERZZ1");
            entity.Property(e => e.Erzz2)
                .HasMaxLength(60)
                .HasColumnName("ERZZ2");
            entity.Property(e => e.Erzz3)
                .HasMaxLength(60)
                .HasColumnName("ERZZ3");
            entity.Property(e => e.Iban)
                .HasMaxLength(30)
                .HasColumnName("IBAN");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.Swift)
                .HasMaxLength(30)
                .HasColumnName("SWIFT");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Anzahlungskonto>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Anzahlungskonto$ID");

            entity.ToTable("Anzahlungskonto");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AnzAktGuthaben).HasColumnType("money");
            entity.Property(e => e.AnzBrutto).HasColumnType("money");
            entity.Property(e => e.AnzDatum).HasColumnType("datetime");
            entity.Property(e => e.AnzEingang).HasDefaultValue(false);
            entity.Property(e => e.AnzLfnr).HasColumnName("AnzLFNr");
            entity.Property(e => e.AnzLfsnr).HasColumnName("AnzLFSNr");
            entity.Property(e => e.AnzStartGuthaben).HasColumnType("money");
            entity.Property(e => e.AnzUst)
                .HasColumnType("money")
                .HasColumnName("AnzUSt");
            entity.Property(e => e.Storno).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Aradressdaten>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ARAdressdaten$ID");

            entity.ToTable("ARAdressdaten");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AraltNr).HasColumnName("ARAltNr");
            entity.Property(e => e.AranredeRe)
                .HasMaxLength(30)
                .HasColumnName("ARAnredeRE");
            entity.Property(e => e.AranredeWe)
                .HasMaxLength(30)
                .HasColumnName("ARAnredeWE");
            entity.Property(e => e.Arasld).HasColumnName("ARASLD");
            entity.Property(e => e.Arbank)
                .HasMaxLength(40)
                .HasColumnName("ARBank");
            entity.Property(e => e.ArbankZuOrdn).HasColumnName("ARBankZuOrdn");
            entity.Property(e => e.Arbearbeiter)
                .HasMaxLength(20)
                .HasColumnName("ARBearbeiter");
            entity.Property(e => e.ArbestellNr)
                .HasMaxLength(30)
                .HasColumnName("ARBestellNr");
            entity.Property(e => e.Arbez).HasColumnName("ARBEZ");
            entity.Property(e => e.Arbldre).HasColumnName("ARBLDRE");
            entity.Property(e => e.Arbldwe).HasColumnName("ARBLDWE");
            entity.Property(e => e.Arblz)
                .HasMaxLength(10)
                .HasColumnName("ARBLZ");
            entity.Property(e => e.Ardatum)
                .HasColumnType("datetime")
                .HasColumnName("ARDatum");
            entity.Property(e => e.ArfbzgKng)
                .HasMaxLength(1)
                .HasColumnName("ARFbzgKng");
            entity.Property(e => e.Arfibu2)
                .HasDefaultValue(false)
                .HasColumnName("ARFibu2");
            entity.Property(e => e.Argblnr)
                .HasMaxLength(10)
                .HasColumnName("ARGBLNr");
            entity.Property(e => e.ArgesBrutto)
                .HasColumnType("money")
                .HasColumnName("ARGesBrutto");
            entity.Property(e => e.ArgesBrutto1)
                .HasColumnType("money")
                .HasColumnName("ARGesBrutto1");
            entity.Property(e => e.ArgesBrutto2)
                .HasColumnType("money")
                .HasColumnName("ARGesBrutto2");
            entity.Property(e => e.ArgesBrutto3)
                .HasColumnType("money")
                .HasColumnName("ARGesBrutto3");
            entity.Property(e => e.ArgesMenge).HasColumnName("ARGesMenge");
            entity.Property(e => e.ArgesMwst)
                .HasColumnType("money")
                .HasColumnName("ARGesMwst");
            entity.Property(e => e.ArgesNetto)
                .HasColumnType("money")
                .HasColumnName("ARGesNetto");
            entity.Property(e => e.ArgesRabatt)
                .HasColumnType("money")
                .HasColumnName("ARGesRabatt");
            entity.Property(e => e.ArkdgrabattD)
                .HasColumnType("money")
                .HasColumnName("ARKDGRabattD");
            entity.Property(e => e.ArkdgrabattP)
                .HasColumnType("money")
                .HasColumnName("ARKDGRabattP");
            entity.Property(e => e.ArkdlsEz)
                .HasMaxLength(1)
                .HasColumnName("ARKDLsEz");
            entity.Property(e => e.ArkdnrCl).HasColumnName("ARKDNrCL");
            entity.Property(e => e.ArkdnrRe).HasColumnName("ARKDNrRE");
            entity.Property(e => e.ArkdnrWe).HasColumnName("ARKDNrWE");
            entity.Property(e => e.Arkdsbgre)
                .HasMaxLength(10)
                .HasColumnName("ARKDSBGRE");
            entity.Property(e => e.Arkdsbgwe)
                .HasMaxLength(10)
                .HasColumnName("ARKDSBGWE");
            entity.Property(e => e.ArkontoNr)
                .HasMaxLength(20)
                .HasColumnName("ARKontoNr");
            entity.Property(e => e.Arkorrektur)
                .HasDefaultValue(false)
                .HasColumnName("ARKorrektur");
            entity.Property(e => e.ArlandRe)
                .HasMaxLength(30)
                .HasColumnName("ARLandRE");
            entity.Property(e => e.ArlandWe)
                .HasMaxLength(30)
                .HasColumnName("ARLandWE");
            entity.Property(e => e.ArlfnrWe)
                .HasMaxLength(20)
                .HasColumnName("ARLFNrWE");
            entity.Property(e => e.Arlkw)
                .HasMaxLength(25)
                .HasColumnName("ARLkw");
            entity.Property(e => e.ArmwSt1Proz)
                .HasColumnType("money")
                .HasColumnName("ARMwSt1Proz");
            entity.Property(e => e.ArmwSt2Proz)
                .HasColumnType("money")
                .HasColumnName("ARMwSt2Proz");
            entity.Property(e => e.ArmwSt3Proz)
                .HasColumnType("money")
                .HasColumnName("ARMwSt3Proz");
            entity.Property(e => e.Armwst1)
                .HasColumnType("money")
                .HasColumnName("ARMwst1");
            entity.Property(e => e.Armwst2)
                .HasColumnType("money")
                .HasColumnName("ARMwst2");
            entity.Property(e => e.Armwst3)
                .HasColumnType("money")
                .HasColumnName("ARMwst3");
            entity.Property(e => e.Arname1Re)
                .HasMaxLength(30)
                .HasColumnName("ARName1RE");
            entity.Property(e => e.Arname1We)
                .HasMaxLength(30)
                .HasColumnName("ARName1WE");
            entity.Property(e => e.Arname2Re)
                .HasMaxLength(30)
                .HasColumnName("ARName2RE");
            entity.Property(e => e.Arname2We)
                .HasMaxLength(30)
                .HasColumnName("ARName2WE");
            entity.Property(e => e.Arname3Re)
                .HasMaxLength(30)
                .HasColumnName("ARName3RE");
            entity.Property(e => e.Arname3We)
                .HasMaxLength(30)
                .HasColumnName("ARName3WE");
            entity.Property(e => e.Arnetto1)
                .HasColumnType("money")
                .HasColumnName("ARNetto1");
            entity.Property(e => e.Arnetto2)
                .HasColumnType("money")
                .HasColumnName("ARNetto2");
            entity.Property(e => e.Arnetto3)
                .HasColumnType("money")
                .HasColumnName("ARNetto3");
            entity.Property(e => e.ArnettoTage).HasColumnName("ARNettoTage");
            entity.Property(e => e.Arnummer).HasColumnName("ARNummer");
            entity.Property(e => e.ArortRe)
                .HasMaxLength(30)
                .HasColumnName("AROrtRE");
            entity.Property(e => e.ArortWe)
                .HasMaxLength(30)
                .HasColumnName("AROrtWE");
            entity.Property(e => e.ArpfachRe)
                .HasMaxLength(10)
                .HasColumnName("ARPFachRE");
            entity.Property(e => e.ArpfachWe)
                .HasMaxLength(10)
                .HasColumnName("ARPFachWE");
            entity.Property(e => e.Arplzre)
                .HasMaxLength(10)
                .HasColumnName("ARPLZRE");
            entity.Property(e => e.Arplzwe)
                .HasMaxLength(10)
                .HasColumnName("ARPLZWE");
            entity.Property(e => e.ArreBetrag)
                .HasColumnType("money")
                .HasColumnName("ARReBetrag");
            entity.Property(e => e.ArregIln)
                .HasMaxLength(255)
                .HasColumnName("ARRegILN");
            entity.Property(e => e.ArregioName)
                .HasMaxLength(30)
                .HasColumnName("ARRegioName");
            entity.Property(e => e.ArregioName2)
                .HasMaxLength(30)
                .HasColumnName("ARRegioName2");
            entity.Property(e => e.ArregioNr).HasColumnName("ARRegioNr");
            entity.Property(e => e.ArregioOrt)
                .HasMaxLength(30)
                .HasColumnName("ARRegioOrt");
            entity.Property(e => e.ArregioPlz)
                .HasMaxLength(10)
                .HasColumnName("ARRegioPLZ");
            entity.Property(e => e.ArregioStatus)
                .HasMaxLength(1)
                .HasColumnName("ARRegioStatus");
            entity.Property(e => e.ArregioStrasse)
                .HasMaxLength(30)
                .HasColumnName("ARRegioStrasse");
            entity.Property(e => e.Arskt1Betrag)
                .HasColumnType("money")
                .HasColumnName("ARSkt1Betrag");
            entity.Property(e => e.Arskt2Betrag)
                .HasColumnType("money")
                .HasColumnName("ARSkt2Betrag");
            entity.Property(e => e.Arskt3Betrag)
                .HasColumnType("money")
                .HasColumnName("ARSkt3Betrag");
            entity.Property(e => e.Arskto1P)
                .HasColumnType("money")
                .HasColumnName("ARSkto1P");
            entity.Property(e => e.Arskto1T).HasColumnName("ARSkto1T");
            entity.Property(e => e.Arskto2P)
                .HasColumnType("money")
                .HasColumnName("ARSkto2P");
            entity.Property(e => e.Arskto2T).HasColumnName("ARSkto2T");
            entity.Property(e => e.ArstatusGutschrift)
                .HasDefaultValue(false)
                .HasColumnName("ARStatusGutschrift");
            entity.Property(e => e.ArstatusRechnung)
                .HasDefaultValue(false)
                .HasColumnName("ARStatusRechnung");
            entity.Property(e => e.ArstatusStorno)
                .HasDefaultValue(false)
                .HasColumnName("ARStatusStorno");
            entity.Property(e => e.ArstornoKng)
                .HasMaxLength(1)
                .HasColumnName("ARStornoKng");
            entity.Property(e => e.ArstrasseRe)
                .HasMaxLength(30)
                .HasColumnName("ARStrasseRE");
            entity.Property(e => e.ArstrasseWe)
                .HasMaxLength(30)
                .HasColumnName("ARStrasseWE");
            entity.Property(e => e.ArstreckenNr).HasColumnName("ARStreckenNr");
            entity.Property(e => e.Aruhrzeit)
                .HasMaxLength(30)
                .HasColumnName("ARUhrzeit");
            entity.Property(e => e.ArustIdNrRe)
                .HasMaxLength(18)
                .HasColumnName("ARUstIdNrRE");
            entity.Property(e => e.ArvertrName)
                .HasMaxLength(30)
                .HasColumnName("ARVertrName");
            entity.Property(e => e.ArvertrNr).HasColumnName("ARVertrNr");
            entity.Property(e => e.ArvertrOrt)
                .HasMaxLength(30)
                .HasColumnName("ARVertrOrt");
            entity.Property(e => e.Arwashout)
                .HasDefaultValue(false)
                .HasColumnName("ARWashout");
            entity.Property(e => e.Arwhrg)
                .HasMaxLength(4)
                .HasColumnName("ARWhrg");
            entity.Property(e => e.ArwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("ARWhrgKurs");
            entity.Property(e => e.ArzahlZiel)
                .HasMaxLength(60)
                .HasColumnName("ARZahlZiel");
            entity.Property(e => e.ArzhlDruck)
                .HasMaxLength(255)
                .HasColumnName("ARZhlDruck");
            entity.Property(e => e.ArzhlDruck02)
                .HasMaxLength(255)
                .HasColumnName("ARZhlDruck02");
            entity.Property(e => e.ArzhlDruck03)
                .HasMaxLength(255)
                .HasColumnName("ARZhlDruck03");
            entity.Property(e => e.Bezahlt).HasDefaultValue(false);
            entity.Property(e => e.DruckKz)
                .HasDefaultValue(false)
                .HasColumnName("DruckKZ");
            entity.Property(e => e.Edifact).HasDefaultValue(false);
            entity.Property(e => e.Factoring).HasDefaultValue(false);
            entity.Property(e => e.Factoringv2).HasDefaultValue(false);
            entity.Property(e => e.Freigabe).HasDefaultValue(false);
            entity.Property(e => e.IlnnrRe)
                .HasMaxLength(30)
                .HasColumnName("ILNNrRE");
            entity.Property(e => e.IlnnrWe)
                .HasMaxLength(30)
                .HasColumnName("ILNNrWE");
            entity.Property(e => e.KdliefNr).HasColumnName("KDLiefNr");
            entity.Property(e => e.LockLast).HasColumnType("datetime");
            entity.Property(e => e.LockStart).HasColumnType("datetime");
            entity.Property(e => e.LockUser).HasMaxLength(50);
            entity.Property(e => e.Opre)
                .HasColumnType("money")
                .HasColumnName("OPRE");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.StornoBemerkung).HasMaxLength(255);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
            entity.Property(e => e.UhrzeitTxt).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
            entity.Property(e => e.Vdok)
                .HasMaxLength(1)
                .HasColumnName("VDOK");
            entity.Property(e => e.VvonrWe)
                .HasMaxLength(20)
                .HasColumnName("VVONrWE");
            entity.Property(e => e.WertGut).HasDefaultValue(false);
        });

        modelBuilder.Entity<ArechnSp>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ARechnSP");

            entity.Property(e => e.LockUser).HasMaxLength(100);
        });

        modelBuilder.Entity<Argbvadr>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ARGBVADR$ID");

            entity.ToTable("ARGBVADR");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AnzBrutto).HasColumnType("money");
            entity.Property(e => e.AnzNetto).HasColumnType("money");
            entity.Property(e => e.AnzRestOffen).HasColumnType("money");
            entity.Property(e => e.AnzStartBrutto).HasColumnType("money");
            entity.Property(e => e.AnzStartNetto).HasColumnType("money");
            entity.Property(e => e.BioNr).HasMaxLength(50);
            entity.Property(e => e.Gbabfall)
                .HasColumnType("money")
                .HasColumnName("GBAbfall");
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzgBetr1)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr1");
            entity.Property(e => e.GbabzgBetr2)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr2");
            entity.Property(e => e.GbabzgBetr3)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr3");
            entity.Property(e => e.GbabzgBetr4)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr4");
            entity.Property(e => e.GbabzgBetr5)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr5");
            entity.Property(e => e.GbabzgText1)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText1");
            entity.Property(e => e.GbabzgText2)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText2");
            entity.Property(e => e.GbabzgText3)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText3");
            entity.Property(e => e.GbabzgText4)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText4");
            entity.Property(e => e.GbabzgText5)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText5");
            entity.Property(e => e.GbabzgVz1)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ1");
            entity.Property(e => e.GbabzgVz2)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ2");
            entity.Property(e => e.GbabzgVz3)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ3");
            entity.Property(e => e.GbabzgVz4)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ4");
            entity.Property(e => e.GbabzgVz5)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ5");
            entity.Property(e => e.Gbabzug)
                .HasColumnType("money")
                .HasColumnName("GBAbzug");
            entity.Property(e => e.Gbaltnr).HasColumnName("GBAltnr");
            entity.Property(e => e.GbangAbfall)
                .HasColumnType("money")
                .HasColumnName("GBAngAbfall");
            entity.Property(e => e.GbanlGew)
                .HasColumnType("money")
                .HasColumnName("GBAnlGew");
            entity.Property(e => e.Gbanlfnr).HasColumnName("GBANLFNR");
            entity.Property(e => e.Gbanrede)
                .HasMaxLength(30)
                .HasColumnName("GBAnrede");
            entity.Property(e => e.Gbart).HasColumnName("GBArt");
            entity.Property(e => e.Gbasld).HasColumnName("GBASLD");
            entity.Property(e => e.Gbbank)
                .HasMaxLength(30)
                .HasColumnName("GBBank");
            entity.Property(e => e.Gbbld)
                .HasMaxLength(30)
                .HasColumnName("GBBLD");
            entity.Property(e => e.Gbbldkng).HasColumnName("GBBLDKng");
            entity.Property(e => e.Gbblz)
                .HasMaxLength(10)
                .HasColumnName("GBBLZ");
            entity.Property(e => e.Gbbrutto)
                .HasColumnType("money")
                .HasColumnName("GBBrutto");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.GbdruckKz)
                .HasMaxLength(1)
                .HasColumnName("GBDruckKz");
            entity.Property(e => e.GbentfKl)
                .HasMaxLength(2)
                .HasColumnName("GBEntfKL");
            entity.Property(e => e.GbentfKlzuEur)
                .HasColumnType("money")
                .HasColumnName("GBEntfKLZuEUR");
            entity.Property(e => e.Gbezg)
                .HasDefaultValue(false)
                .HasColumnName("GBEZG");
            entity.Property(e => e.Gbezgbetr)
                .HasColumnType("money")
                .HasColumnName("GBEZGBetr");
            entity.Property(e => e.Gbfax)
                .HasMaxLength(20)
                .HasColumnName("GBFax");
            entity.Property(e => e.Gbfibu2)
                .HasDefaultValue(false)
                .HasColumnName("GBFibu2");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.GbgesMenge)
                .HasColumnType("money")
                .HasColumnName("GBGesMenge");
            entity.Property(e => e.Gbhaendler)
                .HasDefaultValue(false)
                .HasColumnName("GBHaendler");
            entity.Property(e => e.Gbiban)
                .HasMaxLength(30)
                .HasColumnName("GBIBAN");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(20)
                .HasColumnName("GBKonto");
            entity.Property(e => e.Gbktnr).HasColumnName("GBKTNr");
            entity.Property(e => e.GbktrNrLf)
                .HasMaxLength(10)
                .HasColumnName("GBKtrNrLF");
            entity.Property(e => e.Gbland)
                .HasMaxLength(30)
                .HasColumnName("GBLand");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblfstatus)
                .HasMaxLength(15)
                .HasColumnName("GBLFStatus");
            entity.Property(e => e.GbmitText1)
                .HasMaxLength(60)
                .HasColumnName("GBMitText1");
            entity.Property(e => e.GbmitText2)
                .HasMaxLength(60)
                .HasColumnName("GBMitText2");
            entity.Property(e => e.Gbmwst)
                .HasColumnType("money")
                .HasColumnName("GBMwst");
            entity.Property(e => e.Gbnachzhlg)
                .HasMaxLength(1)
                .HasColumnName("GBNachzhlg");
            entity.Property(e => e.Gbname1)
                .HasMaxLength(30)
                .HasColumnName("GBName1");
            entity.Property(e => e.Gbname2)
                .HasMaxLength(30)
                .HasColumnName("GBName2");
            entity.Property(e => e.Gbname3)
                .HasMaxLength(30)
                .HasColumnName("GBName3");
            entity.Property(e => e.Gbnetto)
                .HasColumnType("money")
                .HasColumnName("GBNetto");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.Gbort)
                .HasMaxLength(30)
                .HasColumnName("GBOrt");
            entity.Property(e => e.Gbplz)
                .HasMaxLength(10)
                .HasColumnName("GBPLZ");
            entity.Property(e => e.GbreNrLf)
                .HasMaxLength(25)
                .HasColumnName("GBReNrLF");
            entity.Property(e => e.Gbsbg)
                .HasMaxLength(10)
                .HasColumnName("GBSBG");
            entity.Property(e => e.GbstNr)
                .HasMaxLength(20)
                .HasColumnName("GBStNr");
            entity.Property(e => e.GbstProz)
                .HasColumnType("money")
                .HasColumnName("GBStProz");
            entity.Property(e => e.GbstProz2).HasColumnName("GBStProz2");
            entity.Property(e => e.GbstSchl).HasColumnName("GBStSchl");
            entity.Property(e => e.Gbstorno)
                .HasMaxLength(1)
                .HasColumnName("GBStorno");
            entity.Property(e => e.Gbstrasse)
                .HasMaxLength(30)
                .HasColumnName("GBStrasse");
            entity.Property(e => e.Gbswift)
                .HasMaxLength(30)
                .HasColumnName("GBSWIFT");
            entity.Property(e => e.Gbtelefon)
                .HasMaxLength(20)
                .HasColumnName("GBTelefon");
            entity.Property(e => e.Gbustidnr)
                .HasMaxLength(18)
                .HasColumnName("GBUSTIDNR");
            entity.Property(e => e.Gbvaluta)
                .HasMaxLength(60)
                .HasColumnName("GBValuta");
            entity.Property(e => e.GbvalutaDatum)
                .HasColumnType("datetime")
                .HasColumnName("GBValutaDatum");
            entity.Property(e => e.Gbvwhrg)
                .HasMaxLength(4)
                .HasColumnName("GBVWhrg");
            entity.Property(e => e.GbvwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("GBVWhrgKurs");
            entity.Property(e => e.GbzahlBetrag)
                .HasColumnType("money")
                .HasColumnName("GBZahlBetrag");
            entity.Property(e => e.GbzuschlEur)
                .HasColumnType("money")
                .HasColumnName("GBZuschlEUR");
            entity.Property(e => e.GbzuschlSumme)
                .HasColumnType("money")
                .HasColumnName("GBZuschlSumme");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Argbvhpt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ARGBVHPT$ID");

            entity.ToTable("ARGBVHPT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BuchMenge).HasColumnType("money");
            entity.Property(e => e.GbartNr).HasColumnName("GBArtNr");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.Gbep)
                .HasColumnType("money")
                .HasColumnName("GBEP");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.Gbfracht)
                .HasColumnType("money")
                .HasColumnName("GBFracht");
            entity.Property(e => e.GbgesNetto)
                .HasColumnType("money")
                .HasColumnName("GBGesNetto");
            entity.Property(e => e.GbgetrArt)
                .HasMaxLength(70)
                .HasColumnName("GBGetrArt");
            entity.Property(e => e.GbgetrKng)
                .HasMaxLength(3)
                .HasColumnName("GBGetrKng");
            entity.Property(e => e.GbgridPos).HasColumnName("GBGridPos");
            entity.Property(e => e.Gbhartikel).HasColumnName("GBHArtikel");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(4)
                .HasColumnName("GBKonto");
            entity.Property(e => e.GbkontrNr).HasColumnName("GBKontrNr");
            entity.Property(e => e.Gbkz)
                .HasMaxLength(10)
                .HasColumnName("GBKz");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblkw)
                .HasMaxLength(10)
                .HasColumnName("GBLKW");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.GbstProz).HasColumnName("GBStProz");
            entity.Property(e => e.GbstreckNr)
                .HasMaxLength(12)
                .HasColumnName("GBStreckNr");
            entity.Property(e => e.Gbvznr)
                .HasMaxLength(10)
                .HasColumnName("GBVZNr");
            entity.Property(e => e.GbwgsNr).HasColumnName("GBWgsNr");
            entity.Property(e => e.Lkw).HasColumnName("LKW");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.Nvo).HasColumnName("NVO");
            entity.Property(e => e.Tp).HasColumnName("TP");
            entity.Property(e => e.WgStrekNr).HasMaxLength(12);
        });

        modelBuilder.Entity<Arhauptdatei>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ARHauptdatei$ID");

            entity.ToTable("ARHauptdatei");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteMenge).HasColumnType("money");
            entity.Property(e => e.AnzLspos).HasColumnName("AnzLSPos");
            entity.Property(e => e.ArtEan)
                .HasMaxLength(15)
                .HasColumnName("ArtEAN");
            entity.Property(e => e.ArtEinhVp)
                .HasColumnType("money")
                .HasColumnName("ArtEinhVP");
            entity.Property(e => e.Bezeichnung).HasMaxLength(255);
            entity.Property(e => e.Bld).HasColumnName("BLD");
            entity.Property(e => e.BzgGr).HasMaxLength(10);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.Einzelpreis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.EmpfBruttoVk)
                .HasColumnType("money")
                .HasColumnName("EmpfBruttoVK");
            entity.Property(e => e.ErfSchema).HasMaxLength(2);
            entity.Property(e => e.FbzgNr).HasColumnName("FBZgNr");
            entity.Property(e => e.Filiale).HasMaxLength(1);
            entity.Property(e => e.Fracht).HasColumnType("money");
            entity.Property(e => e.FrachtCheck).HasDefaultValue(false);
            entity.Property(e => e.GesStck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gesamtmenge)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gesamtnetto)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gr)
                .HasColumnType("money")
                .HasColumnName("GR");
            entity.Property(e => e.GrpSkonto).HasColumnType("money");
            entity.Property(e => e.Hartikel).HasColumnName("HArtikel");
            entity.Property(e => e.KdNrWe).HasColumnName("KdNrWE");
            entity.Property(e => e.KnEzZuschlag).HasMaxLength(1);
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.Konto).HasMaxLength(10);
            entity.Property(e => e.KontraktKng).HasMaxLength(1);
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.Lsnr).HasColumnName("LSNr");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.Mhd)
                .HasMaxLength(15)
                .HasColumnName("MHD");
            entity.Property(e => e.MwstProz).HasColumnType("money");
            entity.Property(e => e.NettoEk)
                .HasColumnType("money")
                .HasColumnName("NettoEK");
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.Pfracht)
                .HasColumnType("money")
                .HasColumnName("PFracht");
            entity.Property(e => e.Redatum)
                .HasColumnType("datetime")
                .HasColumnName("REDatum");
            entity.Property(e => e.Renummer).HasColumnName("RENummer");
            entity.Property(e => e.Sk1p)
                .HasColumnType("money")
                .HasColumnName("SK1P");
            entity.Property(e => e.Sk2p)
                .HasColumnType("money")
                .HasColumnName("SK2P");
            entity.Property(e => e.Status).HasMaxLength(1);
            entity.Property(e => e.Vdmproz)
                .HasMaxLength(1)
                .HasColumnName("VDMProz");
            entity.Property(e => e.VertrProv).HasColumnType("money");
            entity.Property(e => e.Zn)
                .HasColumnType("money")
                .HasColumnName("ZN");
            entity.Property(e => e.Zustellart).HasMaxLength(25);
        });

        modelBuilder.Entity<ArtGruppe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtGruppe$ID");

            entity.ToTable("ArtGruppe");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.GrpText).HasMaxLength(30);
        });

        modelBuilder.Entity<ArtIdProz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtIdProz$ID");

            entity.ToTable("ArtIdProz");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtIdBez1).HasMaxLength(30);
            entity.Property(e => e.ArtIdBez2).HasMaxLength(30);
            entity.Property(e => e.ArtIdChgNr).HasMaxLength(15);
            entity.Property(e => e.ArtIdEh).HasMaxLength(10);
            entity.Property(e => e.ArtIdSatz).HasColumnType("money");
        });

        modelBuilder.Entity<ArtIdStamm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtIdStamm$ID");

            entity.ToTable("ArtIdStamm");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtIdBem).HasMaxLength(255);
            entity.Property(e => e.ArtIdBez1).HasMaxLength(30);
            entity.Property(e => e.ArtIdBez2).HasMaxLength(30);
            entity.Property(e => e.ArtIdBez3).HasMaxLength(30);
            entity.Property(e => e.ArtIdBez4).HasMaxLength(30);
            entity.Property(e => e.ArtIdDatum).HasColumnType("datetime");
            entity.Property(e => e.ArtIdMatch).HasMaxLength(10);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<ArtIdVp>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtIdVP$ID");

            entity.ToTable("ArtIdVP");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtIdBez1).HasMaxLength(30);
            entity.Property(e => e.ArtIdBez2).HasMaxLength(30);
            entity.Property(e => e.ArtIdChrgNr).HasMaxLength(15);
            entity.Property(e => e.ArtIdMenge).HasColumnType("money");
            entity.Property(e => e.ArtIdVpe)
                .HasMaxLength(10)
                .HasColumnName("ArtIdVPE");
        });

        modelBuilder.Entity<ArtInv>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtInv$ID");

            entity.ToTable("ArtInv");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.InvBez1).HasMaxLength(30);
            entity.Property(e => e.InvBez2).HasMaxLength(30);
            entity.Property(e => e.InvBzgGr).HasMaxLength(10);
            entity.Property(e => e.InvDatum).HasColumnType("datetime");
            entity.Property(e => e.InvLab)
                .HasColumnType("money")
                .HasColumnName("InvLAb");
            entity.Property(e => e.InvLek)
                .HasColumnType("money")
                .HasColumnName("InvLEk");
            entity.Property(e => e.InvWert).HasColumnType("money");
            entity.Property(e => e.ScanDatum).HasColumnType("datetime");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<ArtKtoAbschl>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtKtoAbschl$ID");

            entity.ToTable("ArtKtoAbschl");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AkabaktKg).HasColumnName("AKABAktKG");
            entity.Property(e => e.AkabaltDp)
                .HasColumnType("money")
                .HasColumnName("AKABaltDP");
            entity.Property(e => e.AkabaltKg).HasColumnName("AKABaltKG");
            entity.Property(e => e.AkabaltWert)
                .HasColumnType("money")
                .HasColumnName("AKABaltWert");
            entity.Property(e => e.AkabartNr).HasColumnName("AKABArtNr");
            entity.Property(e => e.AkabfilNr).HasColumnName("AKABFilNr");
            entity.Property(e => e.AkabneuDp)
                .HasColumnType("money")
                .HasColumnName("AKABneuDP");
            entity.Property(e => e.AkabneuWert)
                .HasColumnType("money")
                .HasColumnName("AKABneuWert");
            entity.Property(e => e.Akabper)
                .HasMaxLength(7)
                .HasColumnName("AKABPer");
            entity.Property(e => e.ArtKtoAbschl1)
                .HasColumnType("money")
                .HasColumnName("ArtKtoAbschl");
        });

        modelBuilder.Entity<ArtKtoDt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtKtoDT$ID");

            entity.ToTable("ArtKtoDT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.DatBis).HasColumnType("datetime");
            entity.Property(e => e.DatVon).HasColumnType("datetime");
        });

        modelBuilder.Entity<ArtKtoE>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtKtoE$ID");

            entity.ToTable("ArtKtoE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Akab).HasColumnName("AKAb");
            entity.Property(e => e.AkabStck)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("AKAbStck");
            entity.Property(e => e.Akan)
                .HasColumnType("money")
                .HasColumnName("AKAN");
            entity.Property(e => e.Akartnr).HasColumnName("AKArtnr");
            entity.Property(e => e.Akblgnr).HasColumnName("AKBlgnr");
            entity.Property(e => e.Akbmge).HasColumnName("AKBMge");
            entity.Property(e => e.Akbp)
                .HasColumnType("money")
                .HasColumnName("AKBP");
            entity.Property(e => e.Akbstck)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("AKBStck");
            entity.Property(e => e.Akdatum)
                .HasColumnType("datetime")
                .HasColumnName("AKDatum");
            entity.Property(e => e.Akgrund)
                .HasMaxLength(10)
                .HasColumnName("AKGrund");
            entity.Property(e => e.Akkng)
                .HasMaxLength(4)
                .HasColumnName("AKKng");
            entity.Property(e => e.Akktnr).HasColumnName("AKKtnr");
            entity.Property(e => e.Akktonr).HasColumnName("AKKtonr");
            entity.Property(e => e.Aklgew).HasColumnName("AKLGew");
            entity.Property(e => e.Aklgst).HasColumnName("AKLgst");
            entity.Property(e => e.Aklstck)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("AKLStck");
            entity.Property(e => e.Akper)
                .HasMaxLength(7)
                .HasColumnName("AKPer");
            entity.Property(e => e.Akposnr).HasColumnName("AKPosnr");
            entity.Property(e => e.AkurBlgnr).HasColumnName("AKUrBlgnr");
            entity.Property(e => e.Akzn)
                .HasColumnType("money")
                .HasColumnName("AKZN");
            entity.Property(e => e.AkzuStck)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("AKZuStck");
            entity.Property(e => e.Akzug).HasColumnName("AKZug");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<ArtKtoNr>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtKtoNr$ID");

            entity.ToTable("ArtKtoNr");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AknartNr).HasColumnName("AKNArtNr");
            entity.Property(e => e.Aknnr).HasColumnName("AKNNr");
            entity.Property(e => e.Aknper)
                .HasMaxLength(7)
                .HasColumnName("AKNPer");
            entity.Property(e => e.Aknpos).HasColumnName("AKNPos");
        });

        modelBuilder.Entity<ArtLager>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtLager$ID");

            entity.ToTable("ArtLager");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtAkabaltDp).HasColumnName("ArtAKABaltDP");
            entity.Property(e => e.ArtAkabaltKg).HasColumnName("ArtAKABaltKG");
            entity.Property(e => e.ArtAkabaltStck)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("ArtAKABaltStck");
            entity.Property(e => e.ArtAkabaltWert).HasColumnName("ArtAKABaltWert");
            entity.Property(e => e.ArtDatum).HasColumnType("datetime");
            entity.Property(e => e.ArtDla).HasColumnName("ArtDLA");
            entity.Property(e => e.ArtGdp)
                .HasColumnType("money")
                .HasColumnName("ArtGDP");
            entity.Property(e => e.ArtLgm).HasColumnName("ArtLGM");
            entity.Property(e => e.ArtLgs).HasColumnName("ArtLGS");
            entity.Property(e => e.ArtLgstck)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("ArtLGStck");
            entity.Property(e => e.ArtLgw)
                .HasColumnType("money")
                .HasColumnName("ArtLGW");
            entity.Property(e => e.ArtMla).HasColumnName("ArtMLA");
            entity.Property(e => e.ArtNetto).HasColumnType("money");
            entity.Property(e => e.ArtOpt1).HasMaxLength(50);
            entity.Property(e => e.ArtSelP).HasColumnType("money");
            entity.Property(e => e.ArtStckLa)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("ArtStckLA");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<ArtVorgAlt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtVorgAlt$ID");

            entity.ToTable("ArtVorgAlt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abschl).HasDefaultValue(false);
            entity.Property(e => e.Bmenge)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("BMenge");
            entity.Property(e => e.Bstueck)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("BStueck");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Lagerstelle).HasDefaultValue((short)0);
            entity.Property(e => e.LscheinNr)
                .HasDefaultValue(0L)
                .HasColumnName("LScheinNr");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Stueck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(50);
        });

        modelBuilder.Entity<ArtVorgang>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ArtVorgang$ID");

            entity.ToTable("ArtVorgang");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abschl).HasDefaultValue(false);
            entity.Property(e => e.Bmenge)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("BMenge");
            entity.Property(e => e.Bstueck)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("BStueck");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Lagerstelle).HasDefaultValue((short)0);
            entity.Property(e => e.LscheinNr)
                .HasDefaultValue(0L)
                .HasColumnName("LScheinNr");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Stueck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(50);
        });

        modelBuilder.Entity<Artikel>(entity =>
        {
            entity.HasKey(e => new { e.Artikelnr, e.Id }).HasName("Artikel$ArtNr");

            entity.ToTable("Artikel");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.AktDtBis).HasColumnType("datetime");
            entity.Property(e => e.AktDtVon).HasColumnType("datetime");
            entity.Property(e => e.AktPreis).HasColumnType("money");
            entity.Property(e => e.ArtBestellNr).HasMaxLength(30);
            entity.Property(e => e.ArtBezText1).HasMaxLength(30);
            entity.Property(e => e.ArtBezText2).HasMaxLength(30);
            entity.Property(e => e.ArtBezText3).HasMaxLength(30);
            entity.Property(e => e.ArtBezText4).HasMaxLength(30);
            entity.Property(e => e.ArtBezugsgr).HasMaxLength(10);
            entity.Property(e => e.ArtChargenNr).HasMaxLength(20);
            entity.Property(e => e.ArtEan)
                .HasMaxLength(15)
                .HasColumnName("ArtEAN");
            entity.Property(e => e.ArtEinhVerp1).HasColumnType("money");
            entity.Property(e => e.ArtEinhVp)
                .HasColumnType("money")
                .HasColumnName("ArtEinhVP");
            entity.Property(e => e.ArtEkpreis)
                .HasColumnType("money")
                .HasColumnName("ArtEKPreis");
            entity.Property(e => e.ArtEkpreisKolli)
                .HasColumnType("money")
                .HasColumnName("ArtEKPreisKolli");
            entity.Property(e => e.ArtFrachtZuschlag).HasColumnType("money");
            entity.Property(e => e.ArtHaltbarkeit).HasMaxLength(255);
            entity.Property(e => e.ArtKassBrutto).HasColumnType("money");
            entity.Property(e => e.ArtKolliInhalt).HasDefaultValue(0L);
            entity.Property(e => e.ArtKonto1).HasMaxLength(10);
            entity.Property(e => e.ArtKonto2).HasMaxLength(10);
            entity.Property(e => e.ArtKonto3).HasMaxLength(10);
            entity.Property(e => e.ArtKonto4).HasMaxLength(10);
            entity.Property(e => e.ArtKonto5).HasMaxLength(10);
            entity.Property(e => e.ArtKonto6).HasMaxLength(10);
            entity.Property(e => e.ArtKtoZug).HasDefaultValue(false);
            entity.Property(e => e.ArtLagerstelle).HasMaxLength(25);
            entity.Property(e => e.ArtLetzteÄnderung).HasColumnType("datetime");
            entity.Property(e => e.ArtLkwtype).HasColumnName("ArtLKWType");
            entity.Property(e => e.ArtMwSt).HasColumnType("money");
            entity.Property(e => e.ArtPakGewicht).HasColumnType("money");
            entity.Property(e => e.ArtPfand).HasColumnType("money");
            entity.Property(e => e.ArtRabattfähig).HasDefaultValue(false);
            entity.Property(e => e.ArtSbg)
                .HasMaxLength(10)
                .HasColumnName("ArtSBG");
            entity.Property(e => e.ArtSkontierbar).HasDefaultValue(false);
            entity.Property(e => e.ArtSperr).HasDefaultValue(false);
            entity.Property(e => e.ArtText).HasMaxLength(255);
            entity.Property(e => e.ArtVkbrutto)
                .HasColumnType("money")
                .HasColumnName("ArtVKBrutto");
            entity.Property(e => e.ArtVkpreis)
                .HasColumnType("money")
                .HasColumnName("ArtVKPreis");
            entity.Property(e => e.ArtWnr)
                .HasMaxLength(10)
                .HasColumnName("ArtWNR");
            entity.Property(e => e.ArtZbem1)
                .HasMaxLength(30)
                .HasColumnName("ArtZBem1");
            entity.Property(e => e.ArtZbem2)
                .HasMaxLength(30)
                .HasColumnName("ArtZBem2");
            entity.Property(e => e.ArtZbem3)
                .HasMaxLength(30)
                .HasColumnName("ArtZBem3");
            entity.Property(e => e.ArtZbem4)
                .HasMaxLength(30)
                .HasColumnName("ArtZBem4");
            entity.Property(e => e.ArtZbem5)
                .HasMaxLength(30)
                .HasColumnName("ArtZBem5");
            entity.Property(e => e.ArtikelIdent).HasDefaultValue(0L);
            entity.Property(e => e.DisplayArtikel).HasDefaultValue(false);
            entity.Property(e => e.DruckForm).HasMaxLength(255);
            entity.Property(e => e.DruckFormSack).HasMaxLength(255);
            entity.Property(e => e.EmpfBruttoVk)
                .HasColumnType("money")
                .HasColumnName("EmpfBruttoVK");
            entity.Property(e => e.EnthEan)
                .HasMaxLength(15)
                .HasColumnName("EnthEAN");
            entity.Property(e => e.EtDruck).HasDefaultValue(false);
            entity.Property(e => e.Hartikel).HasColumnName("HArtikel");
            entity.Property(e => e.Hlgewicht).HasColumnName("HLGewicht");
            entity.Property(e => e.InhFeld).HasMaxLength(10);
            entity.Property(e => e.K)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.KolliAnzPal).HasMaxLength(10);
            entity.Property(e => e.KonsEan)
                .HasMaxLength(15)
                .HasColumnName("KonsEAN");
            entity.Property(e => e.Lgfaktor)
                .HasMaxLength(2)
                .HasColumnName("LGFaktor");
            entity.Property(e => e.Lgstatistik)
                .HasColumnType("money")
                .HasColumnName("LGStatistik");
            entity.Property(e => e.LieferantIln)
                .HasMaxLength(10)
                .HasColumnName("LieferantILN");
            entity.Property(e => e.MindBestandMge).HasDefaultValue(0L);
            entity.Property(e => e.N)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.NettoEk)
                .HasColumnType("money")
                .HasColumnName("NettoEK");
            entity.Property(e => e.P)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.P2o5)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("P2O5");
            entity.Property(e => e.Palette).HasDefaultValue(false);
            entity.Property(e => e.RezNrAbsMasch).HasDefaultValue(0L);
            entity.Property(e => e.RezNrPalettierer).HasDefaultValue(0L);
            entity.Property(e => e.RezNrStretcher).HasDefaultValue(0L);
            entity.Property(e => e.SchwerGetr).HasDefaultValue(false);
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
            entity.Property(e => e.VglMgeEh).HasMaxLength(10);
            entity.Property(e => e.VglPreis).HasColumnType("money");
        });

        modelBuilder.Entity<Arvrabatt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ARVRabatt$ID");

            entity.ToTable("ARVRabatt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.GesamtRab).HasDefaultValue(false);
            entity.Property(e => e.RabEur).HasColumnType("money");
            entity.Property(e => e.RabGes).HasColumnType("money");
            entity.Property(e => e.RabPm)
                .HasMaxLength(1)
                .HasColumnName("RabPM");
            entity.Property(e => e.RabProz).HasColumnType("money");
            entity.Property(e => e.RabText).HasMaxLength(30);
        });

        modelBuilder.Entity<AuftragsSp>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("AuftragsSP");

            entity.Property(e => e.LockUser).HasMaxLength(100);
        });

        modelBuilder.Entity<Auftragskopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Auftragskopf$ID");

            entity.ToTable("Auftragskopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abholung).HasDefaultValue(false);
            entity.Property(e => e.AbrKennzeichen).HasMaxLength(1);
            entity.Property(e => e.Angebot).HasDefaultValue(false);
            entity.Property(e => e.AnredeRe)
                .HasMaxLength(30)
                .HasColumnName("AnredeRE");
            entity.Property(e => e.AnredeWe)
                .HasMaxLength(30)
                .HasColumnName("AnredeWE");
            entity.Property(e => e.Arvnummer).HasColumnName("ARVNummer");
            entity.Property(e => e.Auftrag).HasDefaultValue(false);
            entity.Property(e => e.Auftragsdatum).HasColumnType("datetime");
            entity.Property(e => e.AuslansSchl).HasMaxLength(2);
            entity.Property(e => e.AvisSend).HasDefaultValue(false);
            entity.Property(e => e.Bearbeiter).HasMaxLength(15);
            entity.Property(e => e.Bestellnr).HasMaxLength(30);
            entity.Property(e => e.Bldre).HasColumnName("BLDRE");
            entity.Property(e => e.Bldwe).HasColumnName("BLDWE");
            entity.Property(e => e.Bstrecke).HasColumnName("BStrecke");
            entity.Property(e => e.Disponiert).HasMaxLength(1);
            entity.Property(e => e.Dkennung).HasColumnName("DKennung");
            entity.Property(e => e.Eldatum)
                .HasColumnType("datetime")
                .HasColumnName("ELDatum");
            entity.Property(e => e.ElscheinNr)
                .HasDefaultValue(0L)
                .HasColumnName("ELScheinNr");
            entity.Property(e => e.ErstArtikel).HasDefaultValue(0L);
            entity.Property(e => e.FahrNam).HasMaxLength(20);
            entity.Property(e => e.FahrerName).HasMaxLength(20);
            entity.Property(e => e.FaxRe)
                .HasMaxLength(20)
                .HasColumnName("FaxRE");
            entity.Property(e => e.FaxWe)
                .HasMaxLength(20)
                .HasColumnName("FaxWE");
            entity.Property(e => e.Fpreisliste).HasColumnName("FPreisliste");
            entity.Property(e => e.FrachtPreis).HasColumnType("money");
            entity.Property(e => e.FrachtScheinNr).HasDefaultValue(0L);
            entity.Property(e => e.GegBlgNr).HasMaxLength(15);
            entity.Property(e => e.Ilnnrre)
                .HasMaxLength(30)
                .HasColumnName("ILNNRRE");
            entity.Property(e => e.Ilnnrwe)
                .HasMaxLength(30)
                .HasColumnName("ILNNRWE");
            entity.Property(e => e.KundenNrRe).HasColumnName("KundenNrRE");
            entity.Property(e => e.KundenNrWe).HasColumnName("KundenNrWE");
            entity.Property(e => e.LadeDatum).HasColumnType("datetime");
            entity.Property(e => e.LadeUhrzeit).HasMaxLength(10);
            entity.Property(e => e.LandRe)
                .HasMaxLength(30)
                .HasColumnName("LandRE");
            entity.Property(e => e.LandWe)
                .HasMaxLength(30)
                .HasColumnName("LandWE");
            entity.Property(e => e.LbisDatum)
                .HasColumnType("datetime")
                .HasColumnName("LBisDatum");
            entity.Property(e => e.Lfrestr1)
                .HasMaxLength(70)
                .HasColumnName("LFRestr1");
            entity.Property(e => e.Lfrestr2)
                .HasMaxLength(70)
                .HasColumnName("LFRestr2");
            entity.Property(e => e.Lfrestr3)
                .HasMaxLength(70)
                .HasColumnName("LFRestr3");
            entity.Property(e => e.Lfrestr4)
                .HasMaxLength(70)
                .HasColumnName("LFRestr4");
            entity.Property(e => e.Lieferrest).HasMaxLength(70);
            entity.Property(e => e.Lieferschein).HasDefaultValue(false);
            entity.Property(e => e.LockLast).HasColumnType("datetime");
            entity.Property(e => e.LockStart).HasColumnType("datetime");
            entity.Property(e => e.LockUser).HasMaxLength(50);
            entity.Property(e => e.LoeschKng).HasDefaultValue(false);
            entity.Property(e => e.LsabrKng)
                .HasDefaultValue(false)
                .HasColumnName("LSAbrKng");
            entity.Property(e => e.Lsanliefer)
                .HasMaxLength(20)
                .HasColumnName("LSAnliefer");
            entity.Property(e => e.LsdepotKng).HasColumnName("LSDepotKng");
            entity.Property(e => e.LsdepotNr).HasColumnName("LSDepotNr");
            entity.Property(e => e.LsfremdNr)
                .HasMaxLength(255)
                .HasColumnName("LSFremdNr");
            entity.Property(e => e.LslfnrWe)
                .HasMaxLength(20)
                .HasColumnName("LSLFNrWE");
            entity.Property(e => e.LvonDatum)
                .HasColumnType("datetime")
                .HasColumnName("LVonDatum");
            entity.Property(e => e.MalzKng).HasDefaultValue(false);
            entity.Property(e => e.Mischpalette).HasDefaultValue(false);
            entity.Property(e => e.MitHänger)
                .HasDefaultValue(false)
                .HasColumnName("mitHänger");
            entity.Property(e => e.MobilRe)
                .HasMaxLength(20)
                .HasColumnName("MobilRE");
            entity.Property(e => e.MobilWe)
                .HasMaxLength(20)
                .HasColumnName("MobilWE");
            entity.Property(e => e.Name1Re)
                .HasMaxLength(30)
                .HasColumnName("Name1RE");
            entity.Property(e => e.Name1We)
                .HasMaxLength(30)
                .HasColumnName("Name1WE");
            entity.Property(e => e.Name2Re)
                .HasMaxLength(30)
                .HasColumnName("Name2RE");
            entity.Property(e => e.Name2We)
                .HasMaxLength(30)
                .HasColumnName("Name2WE");
            entity.Property(e => e.Name3Re)
                .HasMaxLength(30)
                .HasColumnName("Name3RE");
            entity.Property(e => e.Name3We)
                .HasMaxLength(30)
                .HasColumnName("Name3WE");
            entity.Property(e => e.NrtelefonRe)
                .HasMaxLength(20)
                .HasColumnName("NRTelefonRE");
            entity.Property(e => e.NrtelefonWe)
                .HasMaxLength(20)
                .HasColumnName("NRTelefonWE");
            entity.Property(e => e.OhneDisposition).HasDefaultValue(false);
            entity.Property(e => e.OrtRe)
                .HasMaxLength(30)
                .HasColumnName("OrtRE");
            entity.Property(e => e.OrtWe)
                .HasMaxLength(30)
                .HasColumnName("OrtWE");
            entity.Property(e => e.Packungsart).HasMaxLength(10);
            entity.Property(e => e.PalBez1).HasMaxLength(255);
            entity.Property(e => e.PalBez2).HasMaxLength(255);
            entity.Property(e => e.PalBez3).HasMaxLength(255);
            entity.Property(e => e.PalKlPck).HasDefaultValue(false);
            entity.Property(e => e.Palettenware).HasDefaultValue(false);
            entity.Property(e => e.PfachRe)
                .HasMaxLength(10)
                .HasColumnName("PFachRE");
            entity.Property(e => e.PfachWe)
                .HasMaxLength(10)
                .HasColumnName("PFachWE");
            entity.Property(e => e.PfadBitMap).HasMaxLength(100);
            entity.Property(e => e.Planenzug).HasDefaultValue(false);
            entity.Property(e => e.Plzre)
                .HasMaxLength(10)
                .HasColumnName("PLZRE");
            entity.Property(e => e.Plzwe)
                .HasMaxLength(10)
                .HasColumnName("PLZWE");
            entity.Property(e => e.RegioName1).HasMaxLength(30);
            entity.Property(e => e.RegioOrt).HasMaxLength(30);
            entity.Property(e => e.RegioStatus).HasMaxLength(1);
            entity.Property(e => e.Retoure).HasDefaultValue(false);
            entity.Property(e => e.Satlog)
                .HasDefaultValue(false)
                .HasColumnName("SATLog");
            entity.Property(e => e.Sbgre)
                .HasMaxLength(10)
                .HasColumnName("SBGRE");
            entity.Property(e => e.Sbgwe)
                .HasMaxLength(10)
                .HasColumnName("SBGWE");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SpedSend).HasDefaultValue(false);
            entity.Property(e => e.StrasseRe)
                .HasMaxLength(30)
                .HasColumnName("StrasseRE");
            entity.Property(e => e.StrasseWe)
                .HasMaxLength(30)
                .HasColumnName("StrasseWE");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Tankzug).HasDefaultValue(false);
            entity.Property(e => e.TelefonRe)
                .HasMaxLength(20)
                .HasColumnName("TelefonRE");
            entity.Property(e => e.TelefonWe)
                .HasMaxLength(20)
                .HasColumnName("TelefonWE");
            entity.Property(e => e.Transport).HasMaxLength(50);
            entity.Property(e => e.Uhrzeit).HasMaxLength(50);
            entity.Property(e => e.UhrzeitTxt).HasMaxLength(50);
            entity.Property(e => e.VertrNrRe).HasColumnName("VertrNrRE");
            entity.Property(e => e.VertrNrWe).HasColumnName("VertrNrWE");
            entity.Property(e => e.VvonrWe)
                .HasMaxLength(20)
                .HasColumnName("VVONrWE");
            entity.Property(e => e.Wgskng)
                .HasDefaultValue(false)
                .HasColumnName("WGSKng");
            entity.Property(e => e.Xlsexp)
                .HasDefaultValue(false)
                .HasColumnName("XLSExp");
            entity.Property(e => e.ZahlZiel).HasMaxLength(60);
            entity.Property(e => e.ZusTxtBez1).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez2).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez3).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez4).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez5).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez6).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh1).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh2).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh3).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh4).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh5).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh6).HasMaxLength(30);
            entity.Property(e => e.Zustellart).HasMaxLength(25);
            entity.Property(e => e.Zustellart2).HasMaxLength(25);
        });

        modelBuilder.Entity<Auftragspo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Auftragspos$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteMenge).HasColumnType("money");
            entity.Property(e => e.ArtEinhVp)
                .HasColumnType("money")
                .HasColumnName("ArtEinhVP");
            entity.Property(e => e.Baeckerzelle).HasMaxLength(10);
            entity.Property(e => e.BestNr).HasMaxLength(25);
            entity.Property(e => e.BezGr).HasMaxLength(10);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.Eannr)
                .HasMaxLength(14)
                .HasColumnName("EANNr");
            entity.Property(e => e.EdiQalifier).HasDefaultValue(0);
            entity.Property(e => e.Epreis)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("EPreis");
            entity.Property(e => e.Erfassungsschema).HasMaxLength(3);
            entity.Property(e => e.Faktor).HasMaxLength(4);
            entity.Property(e => e.Fbneu)
                .HasMaxLength(1)
                .HasColumnName("FBNeu");
            entity.Property(e => e.Fbzgnr).HasColumnName("FBZGNr");
            entity.Property(e => e.GesMenge)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.GesStck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gpreis)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("GPreis");
            entity.Property(e => e.GpreisRab)
                .HasColumnType("money")
                .HasColumnName("GPreisRab");
            entity.Property(e => e.KdtxTcheck)
                .HasDefaultValue(false)
                .HasColumnName("KDTxTCheck");
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.Lkwkammer)
                .HasMaxLength(15)
                .HasColumnName("LKWKammer");
            entity.Property(e => e.LoeschKng).HasDefaultValue(false);
            entity.Property(e => e.LsdepotNr).HasColumnName("LSDepotNr");
            entity.Property(e => e.Mhd)
                .HasMaxLength(15)
                .HasColumnName("MHD");
            entity.Property(e => e.Muehlenzelle).HasMaxLength(15);
            entity.Property(e => e.Mwst).HasColumnType("money");
            entity.Property(e => e.Mwst2).HasColumnType("money");
            entity.Property(e => e.NuebTexte)
                .HasMaxLength(255)
                .HasColumnName("NUebTexte");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.NvobilanzNr)
                .HasMaxLength(30)
                .HasColumnName("NVOBilanzNr");
            entity.Property(e => e.Nvobilanziert)
                .HasDefaultValue(false)
                .HasColumnName("NVOBilanziert");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.PalArt).HasMaxLength(30);
            entity.Property(e => e.PalGew).HasColumnType("money");
            entity.Property(e => e.Pfracht)
                .HasColumnType("money")
                .HasColumnName("PFracht");
            entity.Property(e => e.PosNrBest).HasMaxLength(25);
            entity.Property(e => e.ProduktionsvorgangId).HasColumnName("ProduktionsvorgangID");
            entity.Property(e => e.Rabatt).HasMaxLength(255);
            entity.Property(e => e.Selektionskennung).HasMaxLength(1);
            entity.Property(e => e.Status).HasMaxLength(1);
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
            entity.Property(e => e.ZtxtKng)
                .HasDefaultValue(false)
                .HasColumnName("ZTxtKng");
        });

        modelBuilder.Entity<Awg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AWGS$ID");

            entity.ToTable("AWGS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.GegBlgNr).HasMaxLength(15);
            entity.Property(e => e.Lkw2)
                .HasMaxLength(15)
                .HasColumnName("LKW2");
            entity.Property(e => e.LockLast).HasColumnType("datetime");
            entity.Property(e => e.LockStart).HasColumnType("datetime");
            entity.Property(e => e.LockUser).HasMaxLength(50);
            entity.Property(e => e.LoeschKng).HasDefaultValue(false);
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.NvobilanzNr)
                .HasMaxLength(30)
                .HasColumnName("NVOBilanzNr");
            entity.Property(e => e.Nvobilanziert)
                .HasDefaultValue(false)
                .HasColumnName("NVOBilanziert");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Verwieger).HasMaxLength(15);
            entity.Property(e => e.WgsabrNr).HasColumnName("WGSAbrNr");
            entity.Property(e => e.WgsanlGewicht)
                .HasColumnType("money")
                .HasColumnName("WGSAnlGewicht");
            entity.Property(e => e.WgsanlName2)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlName2");
            entity.Property(e => e.WgsanlName3)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlName3");
            entity.Property(e => e.WgsanlNr).HasColumnName("WGSAnlNr");
            entity.Property(e => e.WgsanlOrt)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlOrt");
            entity.Property(e => e.WgsanlPlz)
                .HasMaxLength(6)
                .HasColumnName("WGSAnlPLZ");
            entity.Property(e => e.WgsartNr).HasColumnName("WGSArtNr");
            entity.Property(e => e.Wgsasld).HasColumnName("WGSAsld");
            entity.Property(e => e.Wgsbemerkung)
                .HasMaxLength(255)
                .HasColumnName("WGSBemerkung");
            entity.Property(e => e.WgschargNr)
                .HasMaxLength(15)
                .HasColumnName("WGSChargNr");
            entity.Property(e => e.Wgsdatum)
                .HasColumnType("datetime")
                .HasColumnName("WGSDatum");
            entity.Property(e => e.Wgselnr)
                .HasMaxLength(4)
                .HasColumnName("WGSELNR");
            entity.Property(e => e.Wgsempfänger)
                .HasMaxLength(20)
                .HasColumnName("WGSEmpfänger");
            entity.Property(e => e.Wgsfeuchte)
                .HasColumnType("money")
                .HasColumnName("WGSFeuchte");
            entity.Property(e => e.WgsfilialNr).HasColumnName("WGSFilialNr");
            entity.Property(e => e.Wgsga)
                .HasMaxLength(1)
                .HasColumnName("WGSGA");
            entity.Property(e => e.WgsgetrArt)
                .HasMaxLength(18)
                .HasColumnName("WGSGetrArt");
            entity.Property(e => e.Wgsgkennung)
                .HasMaxLength(3)
                .HasColumnName("WGSGKennung");
            entity.Property(e => e.WgskontraktNr).HasColumnName("WGSKontraktNr");
            entity.Property(e => e.WgsktNrHandPartn)
                .HasMaxLength(15)
                .HasColumnName("WGSKtNrHandPartn");
            entity.Property(e => e.WgsktNrHdP)
                .HasMaxLength(20)
                .HasColumnName("WGSKtNrHdP");
            entity.Property(e => e.WgslagerortSkp)
                .HasMaxLength(10)
                .HasColumnName("WGSLagerortSKP");
            entity.Property(e => e.Wgsland)
                .HasMaxLength(30)
                .HasColumnName("WGSLand");
            entity.Property(e => e.Wgslfnr).HasColumnName("WGSLFNr");
            entity.Property(e => e.Wgslfstatus)
                .HasMaxLength(10)
                .HasColumnName("WGSLFStatus");
            entity.Property(e => e.Wgslkw2)
                .HasMaxLength(15)
                .HasColumnName("WGSLKW2");
            entity.Property(e => e.Wgslkwkennz)
                .HasMaxLength(10)
                .HasColumnName("WGSLKWKennz");
            entity.Property(e => e.WgslsNr)
                .HasMaxLength(10)
                .HasColumnName("WGSLsNr");
            entity.Property(e => e.WgsmaklerNr).HasColumnName("WGSMaklerNr");
            entity.Property(e => e.Wgsmenge)
                .HasColumnType("money")
                .HasColumnName("WGSMenge");
            entity.Property(e => e.Wgsname1)
                .HasMaxLength(30)
                .HasColumnName("WGSName1");
            entity.Property(e => e.Wgsname2)
                .HasMaxLength(30)
                .HasColumnName("WGSName2");
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
            entity.Property(e => e.Wgsort)
                .HasMaxLength(30)
                .HasColumnName("WGSOrt");
            entity.Property(e => e.WgspartieBlenr)
                .HasMaxLength(20)
                .HasColumnName("WGSPartieBLENr");
            entity.Property(e => e.Wgsplz)
                .HasMaxLength(30)
                .HasColumnName("WGSPLZ");
            entity.Property(e => e.Wgspreis)
                .HasColumnType("money")
                .HasColumnName("WGSPreis");
            entity.Property(e => e.Wgsrmnr).HasColumnName("WGSRMNr");
            entity.Property(e => e.WgssammelNr).HasColumnName("WGSSammelNr");
            entity.Property(e => e.Wgssbg)
                .HasMaxLength(10)
                .HasColumnName("WGSSBG");
            entity.Property(e => e.Wgsselektion)
                .HasMaxLength(1)
                .HasColumnName("WGSSelektion");
            entity.Property(e => e.Wgssorte)
                .HasMaxLength(20)
                .HasColumnName("WGSSorte");
            entity.Property(e => e.WgsspedFax)
                .HasMaxLength(20)
                .HasColumnName("WGSSpedFax");
            entity.Property(e => e.WgsspedName1)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedName1");
            entity.Property(e => e.WgsspedName2)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedName2");
            entity.Property(e => e.WgsspedNr).HasColumnName("WGSSpedNr");
            entity.Property(e => e.WgsspedOrt)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedOrt");
            entity.Property(e => e.WgsspedPlz)
                .HasMaxLength(6)
                .HasColumnName("WGSSpedPLZ");
            entity.Property(e => e.WgsspedSbg)
                .HasMaxLength(10)
                .HasColumnName("WGSSpedSBG");
            entity.Property(e => e.WgsspedStrasse)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedStrasse");
            entity.Property(e => e.WgsspedTelefon)
                .HasMaxLength(20)
                .HasColumnName("WGSSpedTelefon");
            entity.Property(e => e.Wgssperr).HasColumnName("WGSSperr");
            entity.Property(e => e.Wgsstrasse)
                .HasMaxLength(30)
                .HasColumnName("WGSStrasse");
            entity.Property(e => e.WgsstreckenNr).HasColumnName("WGSStreckenNr");
            entity.Property(e => e.Wgstelefon)
                .HasMaxLength(20)
                .HasColumnName("WGSTelefon");
            entity.Property(e => e.WgsvonGbvgesp)
                .HasMaxLength(1)
                .HasColumnName("WGSVonGBVgesp");
            entity.Property(e => e.Wgsvz)
                .HasColumnType("money")
                .HasColumnName("WGSVZ");
            entity.Property(e => e.Wgsvzdatum)
                .HasColumnType("datetime")
                .HasColumnName("WGSVZDatum");
            entity.Property(e => e.Wgsvznr).HasColumnName("WGSVZNr");
            entity.Property(e => e.WgswgNr)
                .HasMaxLength(15)
                .HasColumnName("WGSWgNr");
            entity.Property(e => e.WgszellenNr).HasColumnName("WGSZellenNr");
            entity.Property(e => e.Wiegezeit).HasColumnType("datetime");
        });

        modelBuilder.Entity<Awgslab>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("AWGSLAB$ID");

            entity.ToTable("AWGSLAB");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.WgslabBez1)
                .HasMaxLength(18)
                .HasColumnName("WGSLabBez1");
            entity.Property(e => e.WgslabBez10)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez10");
            entity.Property(e => e.WgslabBez11)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez11");
            entity.Property(e => e.WgslabBez12)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez12");
            entity.Property(e => e.WgslabBez2)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez2");
            entity.Property(e => e.WgslabBez3)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez3");
            entity.Property(e => e.WgslabBez4)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez4");
            entity.Property(e => e.WgslabBez5)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez5");
            entity.Property(e => e.WgslabBez6)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez6");
            entity.Property(e => e.WgslabBez7)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez7");
            entity.Property(e => e.WgslabBez8)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez8");
            entity.Property(e => e.WgslabBez9)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez9");
            entity.Property(e => e.WgslabEh1)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh1");
            entity.Property(e => e.WgslabEh10)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh10");
            entity.Property(e => e.WgslabEh11)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh11");
            entity.Property(e => e.WgslabEh12)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh12");
            entity.Property(e => e.WgslabEh2)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh2");
            entity.Property(e => e.WgslabEh3)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh3");
            entity.Property(e => e.WgslabEh4)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh4");
            entity.Property(e => e.WgslabEh5)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh5");
            entity.Property(e => e.WgslabEh6)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh6");
            entity.Property(e => e.WgslabEh7)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh7");
            entity.Property(e => e.WgslabEh8)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh8");
            entity.Property(e => e.WgslabEh9)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh9");
            entity.Property(e => e.WgslabWert1)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert1");
            entity.Property(e => e.WgslabWert10)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert10");
            entity.Property(e => e.WgslabWert11)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert11");
            entity.Property(e => e.WgslabWert12)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert12");
            entity.Property(e => e.WgslabWert2)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert2");
            entity.Property(e => e.WgslabWert3)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert3");
            entity.Property(e => e.WgslabWert4)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert4");
            entity.Property(e => e.WgslabWert5)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert5");
            entity.Property(e => e.WgslabWert6)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert6");
            entity.Property(e => e.WgslabWert7)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert7");
            entity.Property(e => e.WgslabWert8)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert8");
            entity.Property(e => e.WgslabWert9)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert9");
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        });

        modelBuilder.Entity<Bank>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Bank$ID");

            entity.ToTable("Bank");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Blz).HasColumnName("BLZ");
            entity.Property(e => e.Bname)
                .HasMaxLength(50)
                .HasColumnName("BName");
            entity.Property(e => e.Iban)
                .HasMaxLength(30)
                .HasColumnName("IBAN");
            entity.Property(e => e.KontoNr).HasMaxLength(15);
            entity.Property(e => e.PartnerNr).HasMaxLength(30);
        });

        modelBuilder.Entity<Blskopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("BLSKopf$ID");

            entity.ToTable("BLSKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abgeschlossen).HasDefaultValue(false);
            entity.Property(e => e.AbrKennzeichen).HasMaxLength(1);
            entity.Property(e => e.Alnr).HasColumnName("ALNr");
            entity.Property(e => e.AnredeAl)
                .HasMaxLength(30)
                .HasColumnName("AnredeAL");
            entity.Property(e => e.AnredeLf)
                .HasMaxLength(30)
                .HasColumnName("AnredeLF");
            entity.Property(e => e.AuslansSchl).HasMaxLength(2);
            entity.Property(e => e.Bearbeiter).HasMaxLength(15);
            entity.Property(e => e.Bldal).HasColumnName("BLDAL");
            entity.Property(e => e.Bldlf).HasColumnName("BLDLF");
            entity.Property(e => e.EigKdnr)
                .HasMaxLength(30)
                .HasColumnName("eigKDNr");
            entity.Property(e => e.Ervnummer).HasColumnName("ERVNummer");
            entity.Property(e => e.FaxAl)
                .HasMaxLength(20)
                .HasColumnName("FaxAL");
            entity.Property(e => e.FaxLf)
                .HasMaxLength(20)
                .HasColumnName("FaxLF");
            entity.Property(e => e.FrachtKng).HasDefaultValue(false);
            entity.Property(e => e.LandAl)
                .HasMaxLength(30)
                .HasColumnName("LandAL");
            entity.Property(e => e.LandLf)
                .HasMaxLength(30)
                .HasColumnName("LandLF");
            entity.Property(e => e.Ldatum)
                .HasColumnType("datetime")
                .HasColumnName("LDatum");
            entity.Property(e => e.Lfnr).HasColumnName("LFNr");
            entity.Property(e => e.Lfsbg)
                .HasMaxLength(10)
                .HasColumnName("LFSBG");
            entity.Property(e => e.LiefRestr).HasMaxLength(10);
            entity.Property(e => e.Lieferdatum).HasColumnType("datetime");
            entity.Property(e => e.Lieferschein).HasDefaultValue(false);
            entity.Property(e => e.LsnrLf)
                .HasMaxLength(20)
                .HasColumnName("LSNrLF");
            entity.Property(e => e.Name1Al)
                .HasMaxLength(30)
                .HasColumnName("Name1AL");
            entity.Property(e => e.Name1Lf)
                .HasMaxLength(30)
                .HasColumnName("Name1LF");
            entity.Property(e => e.Name2Al)
                .HasMaxLength(30)
                .HasColumnName("Name2AL");
            entity.Property(e => e.Name2Lf)
                .HasMaxLength(30)
                .HasColumnName("Name2LF");
            entity.Property(e => e.Name3Al)
                .HasMaxLength(30)
                .HasColumnName("Name3AL");
            entity.Property(e => e.Name3Lf)
                .HasMaxLength(30)
                .HasColumnName("Name3LF");
            entity.Property(e => e.OrtAl)
                .HasMaxLength(30)
                .HasColumnName("OrtAL");
            entity.Property(e => e.OrtLf)
                .HasMaxLength(30)
                .HasColumnName("OrtLF");
            entity.Property(e => e.Plzal)
                .HasMaxLength(10)
                .HasColumnName("PLZAL");
            entity.Property(e => e.Plzlf)
                .HasMaxLength(10)
                .HasColumnName("PLZLF");
            entity.Property(e => e.ProdAuftrag).HasDefaultValue(false);
            entity.Property(e => e.RenrLf)
                .HasMaxLength(20)
                .HasColumnName("RENrLF");
            entity.Property(e => e.StrasseAl)
                .HasMaxLength(30)
                .HasColumnName("StrasseAL");
            entity.Property(e => e.StrasseLf)
                .HasMaxLength(30)
                .HasColumnName("StrasseLF");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.TelefonAl)
                .HasMaxLength(20)
                .HasColumnName("TelefonAL");
            entity.Property(e => e.TelefonLf)
                .HasMaxLength(20)
                .HasColumnName("TelefonLF");
            entity.Property(e => e.Zustellart).HasMaxLength(25);
        });

        modelBuilder.Entity<Blspo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("BLSPos$ID");

            entity.ToTable("BLSPos");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteMenge).HasColumnType("money");
            entity.Property(e => e.ArtBestellNr).HasMaxLength(20);
            entity.Property(e => e.BezGr).HasMaxLength(10);
            entity.Property(e => e.Bezeichnung).HasMaxLength(255);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.EkaKu).HasColumnName("EKaKu");
            entity.Property(e => e.Epreis)
                .HasColumnType("money")
                .HasColumnName("EPreis");
            entity.Property(e => e.Erfassungsschema).HasMaxLength(3);
            entity.Property(e => e.Faktor).HasMaxLength(4);
            entity.Property(e => e.GesMenge).HasColumnType("money");
            entity.Property(e => e.GesStck).HasColumnType("money");
            entity.Property(e => e.Gpreis)
                .HasColumnType("money")
                .HasColumnName("GPreis");
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.Mhd)
                .HasColumnType("datetime")
                .HasColumnName("MHD");
            entity.Property(e => e.MinBmenge)
                .HasColumnType("money")
                .HasColumnName("MinBMenge");
            entity.Property(e => e.Mwst).HasColumnType("money");
            entity.Property(e => e.Mwst2).HasColumnType("money");
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.Selektionskennung).HasMaxLength(1);
            entity.Property(e => e.Status).HasMaxLength(1);
        });

        modelBuilder.Entity<ChargTbl>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ChargTbl$ID");

            entity.ToTable("ChargTbl");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Inhalt).HasMaxLength(255);
            entity.Property(e => e.Keng).HasMaxLength(15);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Chargen>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Chargen$ID");

            entity.ToTable("Chargen");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Adesc0)
                .HasMaxLength(5)
                .HasColumnName("ADesc0");
            entity.Property(e => e.Adesc1)
                .HasMaxLength(5)
                .HasColumnName("ADesc1");
            entity.Property(e => e.Adesc10)
                .HasMaxLength(5)
                .HasColumnName("ADesc10");
            entity.Property(e => e.Adesc11)
                .HasMaxLength(5)
                .HasColumnName("ADesc11");
            entity.Property(e => e.Adesc12)
                .HasMaxLength(5)
                .HasColumnName("ADesc12");
            entity.Property(e => e.Adesc13)
                .HasMaxLength(5)
                .HasColumnName("ADesc13");
            entity.Property(e => e.Adesc14)
                .HasMaxLength(5)
                .HasColumnName("ADesc14");
            entity.Property(e => e.Adesc15)
                .HasMaxLength(5)
                .HasColumnName("ADesc15");
            entity.Property(e => e.Adesc16)
                .HasMaxLength(5)
                .HasColumnName("ADesc16");
            entity.Property(e => e.Adesc17)
                .HasMaxLength(5)
                .HasColumnName("ADesc17");
            entity.Property(e => e.Adesc18)
                .HasMaxLength(5)
                .HasColumnName("ADesc18");
            entity.Property(e => e.Adesc19)
                .HasMaxLength(5)
                .HasColumnName("ADesc19");
            entity.Property(e => e.Adesc2)
                .HasMaxLength(5)
                .HasColumnName("ADesc2");
            entity.Property(e => e.Adesc20)
                .HasMaxLength(5)
                .HasColumnName("ADesc20");
            entity.Property(e => e.Adesc21)
                .HasMaxLength(5)
                .HasColumnName("ADesc21");
            entity.Property(e => e.Adesc22)
                .HasMaxLength(5)
                .HasColumnName("ADesc22");
            entity.Property(e => e.Adesc23)
                .HasMaxLength(5)
                .HasColumnName("ADesc23");
            entity.Property(e => e.Adesc24)
                .HasMaxLength(5)
                .HasColumnName("ADesc24");
            entity.Property(e => e.Adesc3)
                .HasMaxLength(5)
                .HasColumnName("ADesc3");
            entity.Property(e => e.Adesc4)
                .HasMaxLength(5)
                .HasColumnName("ADesc4");
            entity.Property(e => e.Adesc5)
                .HasMaxLength(5)
                .HasColumnName("ADesc5");
            entity.Property(e => e.Adesc6)
                .HasMaxLength(5)
                .HasColumnName("ADesc6");
            entity.Property(e => e.Adesc7)
                .HasMaxLength(5)
                .HasColumnName("ADesc7");
            entity.Property(e => e.Adesc8)
                .HasMaxLength(5)
                .HasColumnName("ADesc8");
            entity.Property(e => e.Adesc9)
                .HasMaxLength(5)
                .HasColumnName("ADesc9");
            entity.Property(e => e.Aeinheit0)
                .HasMaxLength(20)
                .HasColumnName("AEinheit0");
            entity.Property(e => e.Aeinheit1)
                .HasMaxLength(20)
                .HasColumnName("AEinheit1");
            entity.Property(e => e.Aeinheit10)
                .HasMaxLength(20)
                .HasColumnName("AEinheit10");
            entity.Property(e => e.Aeinheit11)
                .HasMaxLength(20)
                .HasColumnName("AEinheit11");
            entity.Property(e => e.Aeinheit12)
                .HasMaxLength(20)
                .HasColumnName("AEinheit12");
            entity.Property(e => e.Aeinheit13)
                .HasMaxLength(20)
                .HasColumnName("AEinheit13");
            entity.Property(e => e.Aeinheit14)
                .HasMaxLength(20)
                .HasColumnName("AEinheit14");
            entity.Property(e => e.Aeinheit15)
                .HasMaxLength(20)
                .HasColumnName("AEinheit15");
            entity.Property(e => e.Aeinheit16)
                .HasMaxLength(20)
                .HasColumnName("AEinheit16");
            entity.Property(e => e.Aeinheit17)
                .HasMaxLength(20)
                .HasColumnName("AEinheit17");
            entity.Property(e => e.Aeinheit18)
                .HasMaxLength(20)
                .HasColumnName("AEinheit18");
            entity.Property(e => e.Aeinheit19)
                .HasMaxLength(20)
                .HasColumnName("AEinheit19");
            entity.Property(e => e.Aeinheit2)
                .HasMaxLength(20)
                .HasColumnName("AEinheit2");
            entity.Property(e => e.Aeinheit20)
                .HasMaxLength(20)
                .HasColumnName("AEinheit20");
            entity.Property(e => e.Aeinheit21)
                .HasMaxLength(20)
                .HasColumnName("AEinheit21");
            entity.Property(e => e.Aeinheit22)
                .HasMaxLength(20)
                .HasColumnName("AEinheit22");
            entity.Property(e => e.Aeinheit23)
                .HasMaxLength(20)
                .HasColumnName("AEinheit23");
            entity.Property(e => e.Aeinheit24)
                .HasMaxLength(20)
                .HasColumnName("AEinheit24");
            entity.Property(e => e.Aeinheit3)
                .HasMaxLength(20)
                .HasColumnName("AEinheit3");
            entity.Property(e => e.Aeinheit4)
                .HasMaxLength(20)
                .HasColumnName("AEinheit4");
            entity.Property(e => e.Aeinheit5)
                .HasMaxLength(20)
                .HasColumnName("AEinheit5");
            entity.Property(e => e.Aeinheit6)
                .HasMaxLength(20)
                .HasColumnName("AEinheit6");
            entity.Property(e => e.Aeinheit7)
                .HasMaxLength(20)
                .HasColumnName("AEinheit7");
            entity.Property(e => e.Aeinheit8)
                .HasMaxLength(20)
                .HasColumnName("AEinheit8");
            entity.Property(e => e.Aeinheit9)
                .HasMaxLength(20)
                .HasColumnName("AEinheit9");
            entity.Property(e => e.ArtBez1).HasMaxLength(30);
            entity.Property(e => e.ArtBez2).HasMaxLength(30);
            entity.Property(e => e.Artikelzusatz).HasMaxLength(255);
            entity.Property(e => e.Atext0)
                .HasMaxLength(30)
                .HasColumnName("AText0");
            entity.Property(e => e.Atext1)
                .HasMaxLength(30)
                .HasColumnName("AText1");
            entity.Property(e => e.Atext10)
                .HasMaxLength(30)
                .HasColumnName("AText10");
            entity.Property(e => e.Atext11)
                .HasMaxLength(30)
                .HasColumnName("AText11");
            entity.Property(e => e.Atext12)
                .HasMaxLength(30)
                .HasColumnName("AText12");
            entity.Property(e => e.Atext13)
                .HasMaxLength(30)
                .HasColumnName("AText13");
            entity.Property(e => e.Atext14)
                .HasMaxLength(30)
                .HasColumnName("AText14");
            entity.Property(e => e.Atext15)
                .HasMaxLength(30)
                .HasColumnName("AText15");
            entity.Property(e => e.Atext16)
                .HasMaxLength(30)
                .HasColumnName("AText16");
            entity.Property(e => e.Atext17)
                .HasMaxLength(30)
                .HasColumnName("AText17");
            entity.Property(e => e.Atext18)
                .HasMaxLength(30)
                .HasColumnName("AText18");
            entity.Property(e => e.Atext19)
                .HasMaxLength(30)
                .HasColumnName("AText19");
            entity.Property(e => e.Atext2)
                .HasMaxLength(30)
                .HasColumnName("AText2");
            entity.Property(e => e.Atext20)
                .HasMaxLength(30)
                .HasColumnName("AText20");
            entity.Property(e => e.Atext21)
                .HasMaxLength(30)
                .HasColumnName("AText21");
            entity.Property(e => e.Atext22)
                .HasMaxLength(30)
                .HasColumnName("AText22");
            entity.Property(e => e.Atext23)
                .HasMaxLength(30)
                .HasColumnName("AText23");
            entity.Property(e => e.Atext24)
                .HasMaxLength(30)
                .HasColumnName("AText24");
            entity.Property(e => e.Atext3)
                .HasMaxLength(30)
                .HasColumnName("AText3");
            entity.Property(e => e.Atext4)
                .HasMaxLength(30)
                .HasColumnName("AText4");
            entity.Property(e => e.Atext5)
                .HasMaxLength(30)
                .HasColumnName("AText5");
            entity.Property(e => e.Atext6)
                .HasMaxLength(30)
                .HasColumnName("AText6");
            entity.Property(e => e.Atext7)
                .HasMaxLength(30)
                .HasColumnName("AText7");
            entity.Property(e => e.Atext8)
                .HasMaxLength(30)
                .HasColumnName("AText8");
            entity.Property(e => e.Atext9)
                .HasMaxLength(30)
                .HasColumnName("AText9");
            entity.Property(e => e.Aussehen).HasMaxLength(255);
            entity.Property(e => e.Awert0)
                .HasMaxLength(30)
                .HasColumnName("AWert0");
            entity.Property(e => e.Awert1)
                .HasMaxLength(30)
                .HasColumnName("AWert1");
            entity.Property(e => e.Awert10)
                .HasMaxLength(30)
                .HasColumnName("AWert10");
            entity.Property(e => e.Awert11)
                .HasMaxLength(30)
                .HasColumnName("AWert11");
            entity.Property(e => e.Awert12)
                .HasMaxLength(30)
                .HasColumnName("AWert12");
            entity.Property(e => e.Awert13)
                .HasMaxLength(30)
                .HasColumnName("AWert13");
            entity.Property(e => e.Awert14)
                .HasMaxLength(30)
                .HasColumnName("AWert14");
            entity.Property(e => e.Awert15)
                .HasMaxLength(30)
                .HasColumnName("AWert15");
            entity.Property(e => e.Awert16)
                .HasMaxLength(30)
                .HasColumnName("AWert16");
            entity.Property(e => e.Awert17)
                .HasMaxLength(30)
                .HasColumnName("AWert17");
            entity.Property(e => e.Awert18)
                .HasMaxLength(30)
                .HasColumnName("AWert18");
            entity.Property(e => e.Awert19)
                .HasMaxLength(30)
                .HasColumnName("AWert19");
            entity.Property(e => e.Awert2)
                .HasMaxLength(30)
                .HasColumnName("AWert2");
            entity.Property(e => e.Awert20)
                .HasMaxLength(30)
                .HasColumnName("AWert20");
            entity.Property(e => e.Awert21)
                .HasMaxLength(30)
                .HasColumnName("AWert21");
            entity.Property(e => e.Awert22)
                .HasMaxLength(30)
                .HasColumnName("AWert22");
            entity.Property(e => e.Awert23)
                .HasMaxLength(30)
                .HasColumnName("AWert23");
            entity.Property(e => e.Awert24)
                .HasMaxLength(30)
                .HasColumnName("AWert24");
            entity.Property(e => e.Awert3)
                .HasMaxLength(30)
                .HasColumnName("AWert3");
            entity.Property(e => e.Awert4)
                .HasMaxLength(30)
                .HasColumnName("AWert4");
            entity.Property(e => e.Awert5)
                .HasMaxLength(30)
                .HasColumnName("AWert5");
            entity.Property(e => e.Awert6)
                .HasMaxLength(30)
                .HasColumnName("AWert6");
            entity.Property(e => e.Awert7)
                .HasMaxLength(30)
                .HasColumnName("AWert7");
            entity.Property(e => e.Awert8)
                .HasMaxLength(30)
                .HasColumnName("AWert8");
            entity.Property(e => e.Awert9)
                .HasMaxLength(30)
                .HasColumnName("AWert9");
            entity.Property(e => e.Bediener).HasMaxLength(25);
            entity.Property(e => e.ChargNr).HasMaxLength(20);
            entity.Property(e => e.ChargTxt).HasMaxLength(15);
            entity.Property(e => e.Geruch).HasMaxLength(255);
            entity.Property(e => e.JobDatum).HasColumnType("datetime");
            entity.Property(e => e.Kdnr).HasColumnName("KDNr");
            entity.Property(e => e.Konsistenz).HasMaxLength(255);
            entity.Property(e => e.Mhd)
                .HasColumnType("datetime")
                .HasColumnName("MHD");
            entity.Property(e => e.ProtokollNr).HasMaxLength(50);
            entity.Property(e => e.Qualitaet).HasMaxLength(255);
            entity.Property(e => e.SiloProtokoll).HasMaxLength(255);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Dbfstat>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("DBFStat$ID");

            entity.ToTable("DBFSTAT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.DbfalsNr).HasColumnName("DBFAlsNr");
            entity.Property(e => e.DbfanlGewWgs)
                .HasColumnType("money")
                .HasColumnName("DBFAnlGewWGS");
            entity.Property(e => e.DbfartKtoKn)
                .HasMaxLength(1)
                .HasColumnName("DBFArtKtoKn");
            entity.Property(e => e.Dbfartikelgruppe).HasColumnName("DBFArtikelgruppe");
            entity.Property(e => e.Dbfartikelnummer).HasColumnName("DBFArtikelnummer");
            entity.Property(e => e.Dbfbelegnr).HasColumnName("DBFBelegnr");
            entity.Property(e => e.Dbfbundesland).HasColumnName("DBFBundesland");
            entity.Property(e => e.Dbfdatum)
                .HasColumnType("datetime")
                .HasColumnName("DBFDatum");
            entity.Property(e => e.Dbfekpreis)
                .HasColumnType("money")
                .HasColumnName("DBFEKPreis");
            entity.Property(e => e.DbfelsNr).HasColumnName("DBFElsNr");
            entity.Property(e => e.Dbffaktor).HasColumnName("DBFFaktor");
            entity.Property(e => e.DbffaktorWert).HasColumnName("DBFFaktorWert");
            entity.Property(e => e.Dbffiliale)
                .HasMaxLength(4)
                .HasColumnName("DBFFiliale");
            entity.Property(e => e.DbfgetrKng)
                .HasMaxLength(3)
                .HasColumnName("DBFGetrKng");
            entity.Property(e => e.DbfkaKu).HasColumnName("DBFKaKu");
            entity.Property(e => e.Dbfkdgrpcl).HasColumnName("DBFKDGRPCL");
            entity.Property(e => e.Dbfkdgrpre).HasColumnName("DBFKDGRPRE");
            entity.Property(e => e.Dbfkdgrprs).HasColumnName("DBFKDGRPRS");
            entity.Property(e => e.Dbfkdgrpwe).HasColumnName("DBFKDGRPWE");
            entity.Property(e => e.Dbfkdnrcl).HasColumnName("DBFKDNRCL");
            entity.Property(e => e.Dbfkdnrre).HasColumnName("DBFKDNRRE");
            entity.Property(e => e.Dbfkdnrrs).HasColumnName("DBFKDNRRS");
            entity.Property(e => e.Dbfkdnrwe).HasColumnName("DBFKDNRWE");
            entity.Property(e => e.Dbfkontraktnr).HasColumnName("DBFKontraktnr");
            entity.Property(e => e.Dbfkurs)
                .HasColumnType("money")
                .HasColumnName("DBFKurs");
            entity.Property(e => e.Dbflfdatum)
                .HasColumnType("datetime")
                .HasColumnName("DBFLFDatum");
            entity.Property(e => e.Dbfmenge)
                .HasColumnType("money")
                .HasColumnName("DBFMenge");
            entity.Property(e => e.Dbfnetto)
                .HasColumnType("money")
                .HasColumnName("DBFNetto");
            entity.Property(e => e.DbfprovArt)
                .HasMaxLength(4)
                .HasColumnName("DBFProvArt");
            entity.Property(e => e.DbfprovSatz)
                .HasColumnType("money")
                .HasColumnName("DBFProvSatz");
            entity.Property(e => e.Dbfredatum)
                .HasColumnType("datetime")
                .HasColumnName("DBFREDatum");
            entity.Property(e => e.Dbfstck)
                .HasColumnType("money")
                .HasColumnName("DBFStck");
            entity.Property(e => e.Dbfstorno)
                .HasMaxLength(1)
                .HasColumnName("DBFStorno");
            entity.Property(e => e.Dbfstreckennr)
                .HasMaxLength(15)
                .HasColumnName("DBFStreckennr");
            entity.Property(e => e.DbfumlArtikel).HasColumnName("DBFUmlArtikel");
            entity.Property(e => e.DbfursprBelegnr)
                .HasMaxLength(20)
                .HasColumnName("DBFUrsprBelegnr");
            entity.Property(e => e.Dbfvkpreis)
                .HasColumnType("money")
                .HasColumnName("DBFVKPreis");
            entity.Property(e => e.Dbfvorfall)
                .HasMaxLength(2)
                .HasColumnName("DBFVorfall");
            entity.Property(e => e.Dbfvtvnr).HasColumnName("DBFVTVNr");
            entity.Property(e => e.Dbfvtvstaffel).HasColumnName("DBFVTVStaffel");
            entity.Property(e => e.Dbfwgkng)
                .HasMaxLength(1)
                .HasColumnName("DBFWGKNG");
            entity.Property(e => e.DbfwgsNr).HasColumnName("DBFWgsNr");
            entity.Property(e => e.DbfwhgKn)
                .HasMaxLength(4)
                .HasColumnName("DBFWhgKn");
            entity.Property(e => e.Dbfzustellart)
                .HasMaxLength(25)
                .HasColumnName("DBFZustellart");
        });

        modelBuilder.Entity<DepStamm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("DepStamm$ID");

            entity.ToTable("DepStamm");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bezeichng).HasMaxLength(30);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.DirektUmschlagBis).HasColumnType("datetime");
            entity.Property(e => e.DirektUmschlagVon).HasColumnType("datetime");
            entity.Property(e => e.DpalgKosten)
                .HasColumnType("money")
                .HasColumnName("DPAlgKosten");
            entity.Property(e => e.DpartFaktor).HasColumnName("DPArtFaktor");
            entity.Property(e => e.DpartFaktorwert).HasColumnName("DPArtFaktorwert");
            entity.Property(e => e.DpdlgKosten)
                .HasColumnType("money")
                .HasColumnName("DPDlgKosten");
            entity.Property(e => e.DpelgKosten)
                .HasColumnType("money")
                .HasColumnName("DPElgKosten");
            entity.Property(e => e.Dphlgewicht)
                .HasColumnType("money")
                .HasColumnName("DPHLGewicht");
            entity.Property(e => e.DplfdNr).HasColumnName("DPlfdNr");
            entity.Property(e => e.DpmskNr)
                .HasMaxLength(4)
                .HasColumnName("DPMskNr");
            entity.Property(e => e.GetrKng).HasDefaultValue(false);
            entity.Property(e => e.KammVerwAus).HasColumnType("money");
            entity.Property(e => e.KammVerwEin).HasColumnType("money");
            entity.Property(e => e.Kdsbg)
                .HasMaxLength(10)
                .HasColumnName("KDSBG");
            entity.Property(e => e.KlmengEurAus)
                .HasColumnType("money")
                .HasColumnName("KLMengEurAus");
            entity.Property(e => e.KlmengEurEin)
                .HasColumnType("money")
                .HasColumnName("KLMengEurEin");
            entity.Property(e => e.KlmengZuschlAus)
                .HasColumnType("money")
                .HasColumnName("KLMengZuschlAus");
            entity.Property(e => e.KlmengZuschlEing)
                .HasColumnType("money")
                .HasColumnName("KLMengZuschlEing");
            entity.Property(e => e.SaldoAlt).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Wbtext)
                .HasMaxLength(255)
                .HasColumnName("WBText");
        });

        modelBuilder.Entity<Display>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Display$ID");

            entity.ToTable("Display");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Anzahl).HasDefaultValue(0L);
            entity.Property(e => e.ArtBez).HasMaxLength(50);
            entity.Property(e => e.Artikelnummer).HasDefaultValue(0L);
            entity.Property(e => e.BzgGr).HasMaxLength(50);
            entity.Property(e => e.DartNr)
                .HasDefaultValue(0L)
                .HasColumnName("DArtNr");
            entity.Property(e => e.Ean)
                .HasDefaultValue(0L)
                .HasColumnName("EAN");
        });

        modelBuilder.Entity<Dpfreist>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("DPFreist$ID");

            entity.ToTable("DPFreist");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Dpnr).HasColumnName("DPNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Elskopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ELSKopf$ID");

            entity.ToTable("ELSKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AbrKennzeichen).HasMaxLength(1);
            entity.Property(e => e.Alnr)
                .HasMaxLength(20)
                .HasColumnName("ALNr");
            entity.Property(e => e.AnredeAl)
                .HasMaxLength(30)
                .HasColumnName("AnredeAL");
            entity.Property(e => e.AnredeLf)
                .HasMaxLength(30)
                .HasColumnName("AnredeLF");
            entity.Property(e => e.AusgangsLfnr).HasColumnName("AusgangsLFNr");
            entity.Property(e => e.AuslansSchl).HasMaxLength(2);
            entity.Property(e => e.Bldal).HasColumnName("BLDAL");
            entity.Property(e => e.Bldlf).HasColumnName("BLDLF");
            entity.Property(e => e.Dkennung).HasColumnName("DKennung");
            entity.Property(e => e.Ervnummer).HasColumnName("ERVNummer");
            entity.Property(e => e.FaxAl)
                .HasMaxLength(20)
                .HasColumnName("FaxAL");
            entity.Property(e => e.FaxLf)
                .HasMaxLength(20)
                .HasColumnName("FaxLF");
            entity.Property(e => e.Fpreisliste).HasColumnName("FPreisliste");
            entity.Property(e => e.FrachtKng).HasDefaultValue(false);
            entity.Property(e => e.LandAl)
                .HasMaxLength(30)
                .HasColumnName("LandAL");
            entity.Property(e => e.LandLf)
                .HasMaxLength(30)
                .HasColumnName("LandLF");
            entity.Property(e => e.Ldatum)
                .HasColumnType("datetime")
                .HasColumnName("LDatum");
            entity.Property(e => e.Lfnr).HasColumnName("LFNr");
            entity.Property(e => e.Lfsbg)
                .HasMaxLength(10)
                .HasColumnName("LFSBG");
            entity.Property(e => e.Lieferschein).HasDefaultValue(false);
            entity.Property(e => e.LockLast).HasColumnType("datetime");
            entity.Property(e => e.LockStart).HasColumnType("datetime");
            entity.Property(e => e.LockUser).HasMaxLength(50);
            entity.Property(e => e.LsnrLf)
                .HasMaxLength(20)
                .HasColumnName("LSNrLF");
            entity.Property(e => e.Name1Al)
                .HasMaxLength(30)
                .HasColumnName("Name1AL");
            entity.Property(e => e.Name1Lf)
                .HasMaxLength(30)
                .HasColumnName("Name1LF");
            entity.Property(e => e.Name2Al)
                .HasMaxLength(30)
                .HasColumnName("Name2AL");
            entity.Property(e => e.Name2Lf)
                .HasMaxLength(30)
                .HasColumnName("Name2LF");
            entity.Property(e => e.Name3Al)
                .HasMaxLength(30)
                .HasColumnName("Name3AL");
            entity.Property(e => e.Name3Lf)
                .HasMaxLength(30)
                .HasColumnName("Name3LF");
            entity.Property(e => e.Optionsfeld1).HasMaxLength(1);
            entity.Property(e => e.OrtAl)
                .HasMaxLength(30)
                .HasColumnName("OrtAL");
            entity.Property(e => e.OrtLf)
                .HasMaxLength(30)
                .HasColumnName("OrtLF");
            entity.Property(e => e.Plzal)
                .HasMaxLength(10)
                .HasColumnName("PLZAL");
            entity.Property(e => e.Plzlf)
                .HasMaxLength(10)
                .HasColumnName("PLZLF");
            entity.Property(e => e.ProdAuftrag).HasDefaultValue(false);
            entity.Property(e => e.RenrLf)
                .HasMaxLength(20)
                .HasColumnName("RENrLF");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.StrasseAl)
                .HasMaxLength(30)
                .HasColumnName("StrasseAL");
            entity.Property(e => e.StrasseLf)
                .HasMaxLength(30)
                .HasColumnName("StrasseLF");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.TelefonAl)
                .HasMaxLength(20)
                .HasColumnName("TelefonAL");
            entity.Property(e => e.TelefonLf)
                .HasMaxLength(20)
                .HasColumnName("TelefonLF");
            entity.Property(e => e.Zustellart).HasMaxLength(25);
        });

        modelBuilder.Entity<Elspo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ELSPos$ID");

            entity.ToTable("ELSPos");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Alsartikel).HasColumnName("ALSArtikel");
            entity.Property(e => e.Alsmenge)
                .HasColumnType("money")
                .HasColumnName("ALSMenge");
            entity.Property(e => e.Alsnummer).HasColumnName("ALSNummer");
            entity.Property(e => e.AlteMenge).HasColumnType("money");
            entity.Property(e => e.BestNr).HasMaxLength(30);
            entity.Property(e => e.BezGr).HasMaxLength(10);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.EkaKu).HasColumnName("EKaKu");
            entity.Property(e => e.Epreis)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("EPreis");
            entity.Property(e => e.Erfassungsschema).HasMaxLength(3);
            entity.Property(e => e.Faktor).HasMaxLength(4);
            entity.Property(e => e.GesMenge)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.GesStck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gpreis)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("GPreis");
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.Mhd)
                .HasColumnType("datetime")
                .HasColumnName("MHD");
            entity.Property(e => e.MwSt).HasColumnType("money");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.NvobilanzNr)
                .HasMaxLength(30)
                .HasColumnName("NVOBilanzNr");
            entity.Property(e => e.Nvobilanziert)
                .HasDefaultValue(false)
                .HasColumnName("NVOBilanziert");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
            entity.Property(e => e.Selektionskennung).HasMaxLength(1);
            entity.Property(e => e.Status).HasMaxLength(1);
        });

        modelBuilder.Entity<Erhauptdatei>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ERHauptdatei$ID");

            entity.ToTable("ERHauptdatei");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteElsmenge)
                .HasDefaultValue(0.0)
                .HasColumnName("AlteELSMenge");
            entity.Property(e => e.AnzLspos)
                .HasDefaultValue((short)0)
                .HasColumnName("AnzLSPos");
            entity.Property(e => e.ArtGruppe).HasDefaultValue((short)0);
            entity.Property(e => e.Bezeichnung).HasMaxLength(255);
            entity.Property(e => e.Bld)
                .HasDefaultValue((short)0)
                .HasColumnName("BLD");
            entity.Property(e => e.BzgGr).HasMaxLength(10);
            entity.Property(e => e.Einzelpreis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.ErfSchema).HasMaxLength(2);
            entity.Property(e => e.ErkaKu)
                .HasDefaultValue((short)0)
                .HasColumnName("ERKaKu");
            entity.Property(e => e.Faktor).HasDefaultValue((short)0);
            entity.Property(e => e.Filiale).HasMaxLength(1);
            entity.Property(e => e.Fracht)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.GesStck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gesamtmenge)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gesamtnetto)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Gr)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("GR");
            entity.Property(e => e.Hartikel)
                .HasDefaultValue(0L)
                .HasColumnName("HArtikel");
            entity.Property(e => e.KalkuKo)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.KdNrWe)
                .HasDefaultValue(0L)
                .HasColumnName("KdNrWE");
            entity.Property(e => e.Kgrund)
                .HasMaxLength(10)
                .HasColumnName("KGrund");
            entity.Property(e => e.KnEzZuschlag).HasMaxLength(1);
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.Konto).HasMaxLength(10);
            entity.Property(e => e.KontraktKng).HasMaxLength(1);
            entity.Property(e => e.KontraktNr).HasDefaultValue(0L);
            entity.Property(e => e.Kostenstelle).HasDefaultValue(0L);
            entity.Property(e => e.Kostentraeger).HasDefaultValue(0L);
            entity.Property(e => e.LagerNr).HasDefaultValue((short)0);
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.Lsnr)
                .HasDefaultValue(0L)
                .HasColumnName("LSNr");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.MwstProz)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.MwstSchl).HasDefaultValue((short)0);
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.Redatum)
                .HasColumnType("datetime")
                .HasColumnName("REDatum");
            entity.Property(e => e.Renummer).HasColumnName("RENummer");
            entity.Property(e => e.Status).HasMaxLength(1);
            entity.Property(e => e.StreckenNr).HasDefaultValue(0L);
            entity.Property(e => e.Vdmproz)
                .HasMaxLength(1)
                .HasColumnName("VDMProz");
            entity.Property(e => e.VertrNr).HasDefaultValue(0L);
            entity.Property(e => e.VertrProv).HasDefaultValue((short)0);
            entity.Property(e => e.Vez)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("VEZ");
            entity.Property(e => e.VpckNr).HasDefaultValue(0L);
            entity.Property(e => e.Vxz)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("VXZ");
            entity.Property(e => e.Zustellart).HasMaxLength(25);
        });

        modelBuilder.Entity<Erv>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ERV$ID");

            entity.ToTable("ERV");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Anzahlung).HasDefaultValue(false);
            entity.Property(e => e.BruttoAend).HasDefaultValue(false);
            entity.Property(e => e.Eingang).HasDefaultValue(false);
            entity.Property(e => e.EralteNr).HasColumnName("ERAlteNr");
            entity.Property(e => e.Eranrede)
                .HasMaxLength(30)
                .HasColumnName("ERAnrede");
            entity.Property(e => e.Erasld)
                .HasMaxLength(2)
                .HasColumnName("ERASLD");
            entity.Property(e => e.Erbank)
                .HasMaxLength(30)
                .HasColumnName("ERBank");
            entity.Property(e => e.Erbemerkung).HasColumnName("ERBemerkung");
            entity.Property(e => e.Erbld)
                .HasMaxLength(21)
                .HasColumnName("ERBLD");
            entity.Property(e => e.Erbldnr).HasColumnName("ERBLDNr");
            entity.Property(e => e.Erblz)
                .HasMaxLength(8)
                .HasColumnName("ERBLZ");
            entity.Property(e => e.Erbrutto1)
                .HasColumnType("money")
                .HasColumnName("ERBrutto1");
            entity.Property(e => e.Erbrutto2)
                .HasColumnType("money")
                .HasColumnName("ERBrutto2");
            entity.Property(e => e.Erdatum)
                .HasColumnType("datetime")
                .HasColumnName("ERDatum");
            entity.Property(e => e.Erfibu2)
                .HasDefaultValue(false)
                .HasColumnName("ERFibu2");
            entity.Property(e => e.ErfrachtRe).HasColumnName("ERFrachtRE");
            entity.Property(e => e.ErgesBrutto)
                .HasColumnType("money")
                .HasColumnName("ERGesBrutto");
            entity.Property(e => e.ErgesMenge).HasColumnName("ERGesMenge");
            entity.Property(e => e.ErgesMwst)
                .HasColumnType("money")
                .HasColumnName("ERGesMwst");
            entity.Property(e => e.ErgesNetto)
                .HasColumnType("money")
                .HasColumnName("ERGesNetto");
            entity.Property(e => e.Erkontonr)
                .HasMaxLength(12)
                .HasColumnName("ERKontonr");
            entity.Property(e => e.Erland)
                .HasMaxLength(30)
                .HasColumnName("ERLand");
            entity.Property(e => e.Erlfnr).HasColumnName("ERLFNr");
            entity.Property(e => e.Ermwst1E)
                .HasColumnType("money")
                .HasColumnName("ERMwst1E");
            entity.Property(e => e.Ermwst1P)
                .HasColumnType("money")
                .HasColumnName("ERMwst1P");
            entity.Property(e => e.Ermwst2E)
                .HasColumnType("money")
                .HasColumnName("ERMwst2E");
            entity.Property(e => e.Ermwst2P)
                .HasColumnType("money")
                .HasColumnName("ERMwst2P");
            entity.Property(e => e.Ername1)
                .HasMaxLength(30)
                .HasColumnName("ERName1");
            entity.Property(e => e.Ername2)
                .HasMaxLength(30)
                .HasColumnName("ERName2");
            entity.Property(e => e.Ername3)
                .HasMaxLength(30)
                .HasColumnName("ERName3");
            entity.Property(e => e.Ernetto1)
                .HasColumnType("money")
                .HasColumnName("ERNetto1");
            entity.Property(e => e.Ernetto2)
                .HasColumnType("money")
                .HasColumnName("ERNetto2");
            entity.Property(e => e.ErnettoTage).HasColumnName("ERNettoTage");
            entity.Property(e => e.Ernummer).HasColumnName("ERNummer");
            entity.Property(e => e.Erort)
                .HasMaxLength(30)
                .HasColumnName("EROrt");
            entity.Property(e => e.Erplz)
                .HasMaxLength(10)
                .HasColumnName("ERPLZ");
            entity.Property(e => e.Errebetrag)
                .HasColumnType("money")
                .HasColumnName("ERREBetrag");
            entity.Property(e => e.Errelf)
                .HasMaxLength(20)
                .HasColumnName("ERRELF");
            entity.Property(e => e.Ersbg)
                .HasMaxLength(10)
                .HasColumnName("ERSBG");
            entity.Property(e => e.Erskto1E)
                .HasColumnType("money")
                .HasColumnName("ERSkto1E");
            entity.Property(e => e.Erskto1P)
                .HasColumnType("money")
                .HasColumnName("ERSkto1P");
            entity.Property(e => e.Erskto1T).HasColumnName("ERSkto1T");
            entity.Property(e => e.Erskto2E)
                .HasColumnType("money")
                .HasColumnName("ERSkto2E");
            entity.Property(e => e.Erskto2P)
                .HasColumnType("money")
                .HasColumnName("ERSkto2P");
            entity.Property(e => e.Erskto2T).HasColumnName("ERSkto2T");
            entity.Property(e => e.ErstNr)
                .HasMaxLength(20)
                .HasColumnName("ERStNr");
            entity.Property(e => e.Erstatus).HasColumnName("ERStatus");
            entity.Property(e => e.ErstornoKennung)
                .HasMaxLength(1)
                .HasColumnName("ERStornoKennung");
            entity.Property(e => e.Erstrasse)
                .HasMaxLength(30)
                .HasColumnName("ERStrasse");
            entity.Property(e => e.ErstreckenNr).HasColumnName("ERStreckenNr");
            entity.Property(e => e.ErumrFakt).HasColumnName("ERUmrFakt");
            entity.Property(e => e.Erustidnr)
                .HasMaxLength(20)
                .HasColumnName("ERUSTIDNR");
            entity.Property(e => e.ErvertrNr).HasColumnName("ERVertrNr");
            entity.Property(e => e.ErwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("ERWhrgKurs");
            entity.Property(e => e.Erwru)
                .HasMaxLength(4)
                .HasColumnName("ERWRU");
            entity.Property(e => e.Erzustellart)
                .HasMaxLength(25)
                .HasColumnName("ERZustellart");
            entity.Property(e => e.Erzz1)
                .HasMaxLength(60)
                .HasColumnName("ERZZ1");
            entity.Property(e => e.Erzz2)
                .HasMaxLength(60)
                .HasColumnName("ERZZ2");
            entity.Property(e => e.Erzz3)
                .HasMaxLength(60)
                .HasColumnName("ERZZ3");
            entity.Property(e => e.Iban)
                .HasMaxLength(30)
                .HasColumnName("IBAN");
            entity.Property(e => e.NuetzEing).HasDefaultValue(false);
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.Swift)
                .HasMaxLength(30)
                .HasColumnName("SWIFT");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Estrecke>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EStrecke$ID");

            entity.ToTable("EStrecke");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.EigLagerEk).HasColumnName("EigLagerEK");
            entity.Property(e => e.EigLagerVk).HasColumnName("EigLagerVK");
            entity.Property(e => e.GesMenge)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Lkw).HasColumnName("LKW");
            entity.Property(e => e.StablStLand)
                .HasMaxLength(30)
                .HasColumnName("STAblStLand");
            entity.Property(e => e.StablStName)
                .HasMaxLength(30)
                .HasColumnName("STAblStName");
            entity.Property(e => e.StablStNr).HasColumnName("STAblStNr");
            entity.Property(e => e.StablStOrt)
                .HasMaxLength(30)
                .HasColumnName("STAblStOrt");
            entity.Property(e => e.StablStPlz)
                .HasMaxLength(10)
                .HasColumnName("STAblStPLZ");
            entity.Property(e => e.StablStStrasse)
                .HasMaxLength(30)
                .HasColumnName("STAblStStrasse");
            entity.Property(e => e.StablStTelefax)
                .HasMaxLength(20)
                .HasColumnName("STAblStTelefax");
            entity.Property(e => e.StablStTelefon)
                .HasMaxLength(20)
                .HasColumnName("STAblStTelefon");
            entity.Property(e => e.StabladeMenge).HasColumnName("STAbladeMenge");
            entity.Property(e => e.StabladeTag)
                .HasColumnType("datetime")
                .HasColumnName("STAbladeTag");
            entity.Property(e => e.StabrWeg).HasColumnName("STAbrWeg");
            entity.Property(e => e.Stanlagedatum)
                .HasColumnType("datetime")
                .HasColumnName("STAnlagedatum");
            entity.Property(e => e.StartBez2Ek)
                .HasMaxLength(30)
                .HasColumnName("STArtBez2EK");
            entity.Property(e => e.StartBez2Vk)
                .HasMaxLength(30)
                .HasColumnName("STArtBez2VK");
            entity.Property(e => e.StartBezEk)
                .HasMaxLength(30)
                .HasColumnName("STArtBezEK");
            entity.Property(e => e.StartBezVk)
                .HasMaxLength(30)
                .HasColumnName("STArtBezVK");
            entity.Property(e => e.StartNrEk).HasColumnName("STArtNrEK");
            entity.Property(e => e.StartNrFracht).HasColumnName("STArtNrFracht");
            entity.Property(e => e.StartNrVk).HasColumnName("STArtNrVK");
            entity.Property(e => e.StasldEk)
                .HasMaxLength(2)
                .HasColumnName("STAsldEK");
            entity.Property(e => e.StasldVk)
                .HasMaxLength(2)
                .HasColumnName("STAsldVK");
            entity.Property(e => e.StauftrNrEk).HasColumnName("STAuftrNrEK");
            entity.Property(e => e.StauftrNrVk).HasColumnName("STAuftrNrVK");
            entity.Property(e => e.Stauftragsdatum)
                .HasColumnType("datetime")
                .HasColumnName("STAuftragsdatum");
            entity.Property(e => e.StbelStLand)
                .HasMaxLength(30)
                .HasColumnName("STBelStLand");
            entity.Property(e => e.StbelStName)
                .HasMaxLength(30)
                .HasColumnName("STBelStName");
            entity.Property(e => e.StbelStNr).HasColumnName("STBelStNr");
            entity.Property(e => e.StbelStOrt)
                .HasMaxLength(30)
                .HasColumnName("STBelStOrt");
            entity.Property(e => e.StbelStPlz)
                .HasMaxLength(10)
                .HasColumnName("STBelStPLZ");
            entity.Property(e => e.StbelStStrasse)
                .HasMaxLength(30)
                .HasColumnName("STBelStStrasse");
            entity.Property(e => e.StbelStTelefax)
                .HasMaxLength(20)
                .HasColumnName("STBelStTelefax");
            entity.Property(e => e.StbelStTelefon)
                .HasMaxLength(20)
                .HasColumnName("STBelStTelefon");
            entity.Property(e => e.StbemAblSt).HasColumnName("STBemAblSt");
            entity.Property(e => e.StbemBelSt).HasColumnName("STBemBelSt");
            entity.Property(e => e.StbemEk).HasColumnName("STBemEK");
            entity.Property(e => e.StbemSped).HasColumnName("STBemSped");
            entity.Property(e => e.StbemVk).HasColumnName("STBemVK");
            entity.Property(e => e.Stbetreuer)
                .HasMaxLength(20)
                .HasColumnName("STBetreuer");
            entity.Property(e => e.StdatumBis)
                .HasColumnType("datetime")
                .HasColumnName("STDatumBis");
            entity.Property(e => e.StdispoKng).HasColumnName("STDispoKng");
            entity.Property(e => e.StfrSpedNr)
                .HasMaxLength(15)
                .HasColumnName("STFrSpedNr");
            entity.Property(e => e.StfrachtPreis)
                .HasColumnType("money")
                .HasColumnName("STFrachtPreis");
            entity.Property(e => e.StfrachtSchNr).HasColumnName("STFrachtSchNr");
            entity.Property(e => e.StfremdLsnrEk)
                .HasMaxLength(30)
                .HasColumnName("STFremdLSNrEK");
            entity.Property(e => e.StfremdLsnrVk)
                .HasMaxLength(30)
                .HasColumnName("STFremdLSNrVK");
            entity.Property(e => e.StfremdRenrEk)
                .HasMaxLength(30)
                .HasColumnName("STFremdRENrEK");
            entity.Property(e => e.StfremdRenrVk)
                .HasMaxLength(30)
                .HasColumnName("STFremdRENrVK");
            entity.Property(e => e.StktnrEk).HasColumnName("STKTNrEK");
            entity.Property(e => e.StktnrHdPek)
                .HasMaxLength(10)
                .HasColumnName("STKTNrHdPEK");
            entity.Property(e => e.StktnrHdPvk)
                .HasMaxLength(10)
                .HasColumnName("STKTNrHdPVK");
            entity.Property(e => e.StktnrVk).HasColumnName("STKTNrVK");
            entity.Property(e => e.StlandEk)
                .HasMaxLength(30)
                .HasColumnName("STLandEK");
            entity.Property(e => e.StlandVk)
                .HasMaxLength(30)
                .HasColumnName("STLandVK");
            entity.Property(e => e.Stlkwgew).HasColumnName("STLKWGew");
            entity.Property(e => e.Stlkwkammer1)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer1");
            entity.Property(e => e.Stlkwkammer10)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer10");
            entity.Property(e => e.Stlkwkammer2)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer2");
            entity.Property(e => e.Stlkwkammer3)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer3");
            entity.Property(e => e.Stlkwkammer4)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer4");
            entity.Property(e => e.Stlkwkammer5)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer5");
            entity.Property(e => e.Stlkwkammer6)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer6");
            entity.Property(e => e.Stlkwkammer7)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer7");
            entity.Property(e => e.Stlkwkammer8)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer8");
            entity.Property(e => e.Stlkwkammer9)
                .HasMaxLength(1)
                .HasColumnName("STLKWKammer9");
            entity.Property(e => e.Stlkwkennz)
                .HasMaxLength(15)
                .HasColumnName("STLKWKennz");
            entity.Property(e => e.Stlkwkennz2)
                .HasMaxLength(15)
                .HasColumnName("STLKWKennz2");
            entity.Property(e => e.StlskngEk)
                .HasMaxLength(1)
                .HasColumnName("STLSKngEK");
            entity.Property(e => e.StlskngVk)
                .HasMaxLength(1)
                .HasColumnName("STLSKngVK");
            entity.Property(e => e.Stmenge).HasColumnName("STMenge");
            entity.Property(e => e.Stname1Ek)
                .HasMaxLength(30)
                .HasColumnName("STName1EK");
            entity.Property(e => e.Stname1Vk)
                .HasMaxLength(30)
                .HasColumnName("STName1VK");
            entity.Property(e => e.Stname2Ek)
                .HasMaxLength(30)
                .HasColumnName("STName2EK");
            entity.Property(e => e.Stname2Vk)
                .HasMaxLength(30)
                .HasColumnName("STName2VK");
            entity.Property(e => e.Stnr).HasColumnName("STNr");
            entity.Property(e => e.StortEk)
                .HasMaxLength(30)
                .HasColumnName("STOrtEK");
            entity.Property(e => e.StortVk)
                .HasMaxLength(30)
                .HasColumnName("STOrtVK");
            entity.Property(e => e.StpersNrEk).HasColumnName("STPersNrEK");
            entity.Property(e => e.StpersNrVk).HasColumnName("STPersNrVK");
            entity.Property(e => e.Stplzek)
                .HasMaxLength(10)
                .HasColumnName("STPLZEK");
            entity.Property(e => e.Stplzvk)
                .HasMaxLength(10)
                .HasColumnName("STPLZVK");
            entity.Property(e => e.StpreisEk)
                .HasColumnType("money")
                .HasColumnName("STPreisEK");
            entity.Property(e => e.StpreisVk)
                .HasColumnType("money")
                .HasColumnName("STPreisVK");
            entity.Property(e => e.StrenrEk).HasColumnName("STRENrEK");
            entity.Property(e => e.StrenrVk).HasColumnName("STRENrVK");
            entity.Property(e => e.StspedLand)
                .HasMaxLength(30)
                .HasColumnName("STSpedLand");
            entity.Property(e => e.StspedName)
                .HasMaxLength(30)
                .HasColumnName("STSpedName");
            entity.Property(e => e.StspedName2)
                .HasMaxLength(30)
                .HasColumnName("STSpedName2");
            entity.Property(e => e.StspedName3)
                .HasMaxLength(30)
                .HasColumnName("STSpedName3");
            entity.Property(e => e.StspedNr).HasColumnName("STSpedNr");
            entity.Property(e => e.StspedOrt)
                .HasMaxLength(30)
                .HasColumnName("STSpedOrt");
            entity.Property(e => e.StspedPlz)
                .HasMaxLength(10)
                .HasColumnName("STSpedPLZ");
            entity.Property(e => e.StspedStrasse)
                .HasMaxLength(30)
                .HasColumnName("STSpedStrasse");
            entity.Property(e => e.StspedTelefax)
                .HasMaxLength(20)
                .HasColumnName("STSpedTelefax");
            entity.Property(e => e.StspedTelefon)
                .HasMaxLength(20)
                .HasColumnName("STSpedTelefon");
            entity.Property(e => e.StstrasseEk)
                .HasMaxLength(30)
                .HasColumnName("STStrasseEK");
            entity.Property(e => e.StstrasseVk)
                .HasMaxLength(30)
                .HasColumnName("STStrasseVK");
            entity.Property(e => e.SttelefaxEk)
                .HasMaxLength(20)
                .HasColumnName("STTelefaxEK");
            entity.Property(e => e.SttelefaxVk)
                .HasMaxLength(20)
                .HasColumnName("STTelefaxVK");
            entity.Property(e => e.SttelefonEk)
                .HasMaxLength(20)
                .HasColumnName("STTelefonEK");
            entity.Property(e => e.SttelefonVk)
                .HasMaxLength(20)
                .HasColumnName("STTelefonVK");
            entity.Property(e => e.StverladeTag)
                .HasColumnType("datetime")
                .HasColumnName("STVerladeTag");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.ZusKost).HasColumnType("money");
        });

        modelBuilder.Entity<Etabelle>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ETabelle$ID");

            entity.ToTable("ETabelle");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Lbwert1)
                .HasColumnType("money")
                .HasColumnName("LBWert1");
            entity.Property(e => e.Lbwert10)
                .HasColumnType("money")
                .HasColumnName("LBWert10");
            entity.Property(e => e.Lbwert100)
                .HasColumnType("money")
                .HasColumnName("LBWert100");
            entity.Property(e => e.Lbwert11)
                .HasColumnType("money")
                .HasColumnName("LBWert11");
            entity.Property(e => e.Lbwert12)
                .HasColumnType("money")
                .HasColumnName("LBWert12");
            entity.Property(e => e.Lbwert13)
                .HasColumnType("money")
                .HasColumnName("LBWert13");
            entity.Property(e => e.Lbwert14)
                .HasColumnType("money")
                .HasColumnName("LBWert14");
            entity.Property(e => e.Lbwert15)
                .HasColumnType("money")
                .HasColumnName("LBWert15");
            entity.Property(e => e.Lbwert16)
                .HasColumnType("money")
                .HasColumnName("LBWert16");
            entity.Property(e => e.Lbwert17)
                .HasColumnType("money")
                .HasColumnName("LBWert17");
            entity.Property(e => e.Lbwert18)
                .HasColumnType("money")
                .HasColumnName("LBWert18");
            entity.Property(e => e.Lbwert19)
                .HasColumnType("money")
                .HasColumnName("LBWert19");
            entity.Property(e => e.Lbwert2)
                .HasColumnType("money")
                .HasColumnName("LBWert2");
            entity.Property(e => e.Lbwert20)
                .HasColumnType("money")
                .HasColumnName("LBWert20");
            entity.Property(e => e.Lbwert21)
                .HasColumnType("money")
                .HasColumnName("LBWert21");
            entity.Property(e => e.Lbwert22)
                .HasColumnType("money")
                .HasColumnName("LBWert22");
            entity.Property(e => e.Lbwert23)
                .HasColumnType("money")
                .HasColumnName("LBWert23");
            entity.Property(e => e.Lbwert24)
                .HasColumnType("money")
                .HasColumnName("LBWert24");
            entity.Property(e => e.Lbwert25)
                .HasColumnType("money")
                .HasColumnName("LBWert25");
            entity.Property(e => e.Lbwert26)
                .HasColumnType("money")
                .HasColumnName("LBWert26");
            entity.Property(e => e.Lbwert27)
                .HasColumnType("money")
                .HasColumnName("LBWert27");
            entity.Property(e => e.Lbwert28)
                .HasColumnType("money")
                .HasColumnName("LBWert28");
            entity.Property(e => e.Lbwert29)
                .HasColumnType("money")
                .HasColumnName("LBWert29");
            entity.Property(e => e.Lbwert3)
                .HasColumnType("money")
                .HasColumnName("LBWert3");
            entity.Property(e => e.Lbwert30)
                .HasColumnType("money")
                .HasColumnName("LBWert30");
            entity.Property(e => e.Lbwert31)
                .HasColumnType("money")
                .HasColumnName("LBWert31");
            entity.Property(e => e.Lbwert32)
                .HasColumnType("money")
                .HasColumnName("LBWert32");
            entity.Property(e => e.Lbwert33)
                .HasColumnType("money")
                .HasColumnName("LBWert33");
            entity.Property(e => e.Lbwert34)
                .HasColumnType("money")
                .HasColumnName("LBWert34");
            entity.Property(e => e.Lbwert35)
                .HasColumnType("money")
                .HasColumnName("LBWert35");
            entity.Property(e => e.Lbwert36)
                .HasColumnType("money")
                .HasColumnName("LBWert36");
            entity.Property(e => e.Lbwert37)
                .HasColumnType("money")
                .HasColumnName("LBWert37");
            entity.Property(e => e.Lbwert38)
                .HasColumnType("money")
                .HasColumnName("LBWert38");
            entity.Property(e => e.Lbwert39)
                .HasColumnType("money")
                .HasColumnName("LBWert39");
            entity.Property(e => e.Lbwert4)
                .HasColumnType("money")
                .HasColumnName("LBWert4");
            entity.Property(e => e.Lbwert40)
                .HasColumnType("money")
                .HasColumnName("LBWert40");
            entity.Property(e => e.Lbwert41)
                .HasColumnType("money")
                .HasColumnName("LBWert41");
            entity.Property(e => e.Lbwert42)
                .HasColumnType("money")
                .HasColumnName("LBWert42");
            entity.Property(e => e.Lbwert43)
                .HasColumnType("money")
                .HasColumnName("LBWert43");
            entity.Property(e => e.Lbwert44)
                .HasColumnType("money")
                .HasColumnName("LBWert44");
            entity.Property(e => e.Lbwert45)
                .HasColumnType("money")
                .HasColumnName("LBWert45");
            entity.Property(e => e.Lbwert46)
                .HasColumnType("money")
                .HasColumnName("LBWert46");
            entity.Property(e => e.Lbwert47)
                .HasColumnType("money")
                .HasColumnName("LBWert47");
            entity.Property(e => e.Lbwert48)
                .HasColumnType("money")
                .HasColumnName("LBWert48");
            entity.Property(e => e.Lbwert49)
                .HasColumnType("money")
                .HasColumnName("LBWert49");
            entity.Property(e => e.Lbwert5)
                .HasColumnType("money")
                .HasColumnName("LBWert5");
            entity.Property(e => e.Lbwert50)
                .HasColumnType("money")
                .HasColumnName("LBWert50");
            entity.Property(e => e.Lbwert51)
                .HasColumnType("money")
                .HasColumnName("LBWert51");
            entity.Property(e => e.Lbwert52)
                .HasColumnType("money")
                .HasColumnName("LBWert52");
            entity.Property(e => e.Lbwert53)
                .HasColumnType("money")
                .HasColumnName("LBWert53");
            entity.Property(e => e.Lbwert54)
                .HasColumnType("money")
                .HasColumnName("LBWert54");
            entity.Property(e => e.Lbwert55)
                .HasColumnType("money")
                .HasColumnName("LBWert55");
            entity.Property(e => e.Lbwert56)
                .HasColumnType("money")
                .HasColumnName("LBWert56");
            entity.Property(e => e.Lbwert57)
                .HasColumnType("money")
                .HasColumnName("LBWert57");
            entity.Property(e => e.Lbwert58)
                .HasColumnType("money")
                .HasColumnName("LBWert58");
            entity.Property(e => e.Lbwert59)
                .HasColumnType("money")
                .HasColumnName("LBWert59");
            entity.Property(e => e.Lbwert6)
                .HasColumnType("money")
                .HasColumnName("LBWert6");
            entity.Property(e => e.Lbwert60)
                .HasColumnType("money")
                .HasColumnName("LBWert60");
            entity.Property(e => e.Lbwert61)
                .HasColumnType("money")
                .HasColumnName("LBWert61");
            entity.Property(e => e.Lbwert62)
                .HasColumnType("money")
                .HasColumnName("LBWert62");
            entity.Property(e => e.Lbwert63)
                .HasColumnType("money")
                .HasColumnName("LBWert63");
            entity.Property(e => e.Lbwert64)
                .HasColumnType("money")
                .HasColumnName("LBWert64");
            entity.Property(e => e.Lbwert65)
                .HasColumnType("money")
                .HasColumnName("LBWert65");
            entity.Property(e => e.Lbwert66)
                .HasColumnType("money")
                .HasColumnName("LBWert66");
            entity.Property(e => e.Lbwert67)
                .HasColumnType("money")
                .HasColumnName("LBWert67");
            entity.Property(e => e.Lbwert68)
                .HasColumnType("money")
                .HasColumnName("LBWert68");
            entity.Property(e => e.Lbwert69)
                .HasColumnType("money")
                .HasColumnName("LBWert69");
            entity.Property(e => e.Lbwert7)
                .HasColumnType("money")
                .HasColumnName("LBWert7");
            entity.Property(e => e.Lbwert70)
                .HasColumnType("money")
                .HasColumnName("LBWert70");
            entity.Property(e => e.Lbwert71)
                .HasColumnType("money")
                .HasColumnName("LBWert71");
            entity.Property(e => e.Lbwert72)
                .HasColumnType("money")
                .HasColumnName("LBWert72");
            entity.Property(e => e.Lbwert73)
                .HasColumnType("money")
                .HasColumnName("LBWert73");
            entity.Property(e => e.Lbwert74)
                .HasColumnType("money")
                .HasColumnName("LBWert74");
            entity.Property(e => e.Lbwert75)
                .HasColumnType("money")
                .HasColumnName("LBWert75");
            entity.Property(e => e.Lbwert76)
                .HasColumnType("money")
                .HasColumnName("LBWert76");
            entity.Property(e => e.Lbwert77)
                .HasColumnType("money")
                .HasColumnName("LBWert77");
            entity.Property(e => e.Lbwert78)
                .HasColumnType("money")
                .HasColumnName("LBWert78");
            entity.Property(e => e.Lbwert79)
                .HasColumnType("money")
                .HasColumnName("LBWert79");
            entity.Property(e => e.Lbwert8)
                .HasColumnType("money")
                .HasColumnName("LBWert8");
            entity.Property(e => e.Lbwert80)
                .HasColumnType("money")
                .HasColumnName("LBWert80");
            entity.Property(e => e.Lbwert81)
                .HasColumnType("money")
                .HasColumnName("LBWert81");
            entity.Property(e => e.Lbwert82)
                .HasColumnType("money")
                .HasColumnName("LBWert82");
            entity.Property(e => e.Lbwert83)
                .HasColumnType("money")
                .HasColumnName("LBWert83");
            entity.Property(e => e.Lbwert84)
                .HasColumnType("money")
                .HasColumnName("LBWert84");
            entity.Property(e => e.Lbwert85)
                .HasColumnType("money")
                .HasColumnName("LBWert85");
            entity.Property(e => e.Lbwert86)
                .HasColumnType("money")
                .HasColumnName("LBWert86");
            entity.Property(e => e.Lbwert87)
                .HasColumnType("money")
                .HasColumnName("LBWert87");
            entity.Property(e => e.Lbwert88)
                .HasColumnType("money")
                .HasColumnName("LBWert88");
            entity.Property(e => e.Lbwert89)
                .HasColumnType("money")
                .HasColumnName("LBWert89");
            entity.Property(e => e.Lbwert9)
                .HasColumnType("money")
                .HasColumnName("LBWert9");
            entity.Property(e => e.Lbwert90)
                .HasColumnType("money")
                .HasColumnName("LBWert90");
            entity.Property(e => e.Lbwert91)
                .HasColumnType("money")
                .HasColumnName("LBWert91");
            entity.Property(e => e.Lbwert92)
                .HasColumnType("money")
                .HasColumnName("LBWert92");
            entity.Property(e => e.Lbwert93)
                .HasColumnType("money")
                .HasColumnName("LBWert93");
            entity.Property(e => e.Lbwert94)
                .HasColumnType("money")
                .HasColumnName("LBWert94");
            entity.Property(e => e.Lbwert95)
                .HasColumnType("money")
                .HasColumnName("LBWert95");
            entity.Property(e => e.Lbwert96)
                .HasColumnType("money")
                .HasColumnName("LBWert96");
            entity.Property(e => e.Lbwert97)
                .HasColumnType("money")
                .HasColumnName("LBWert97");
            entity.Property(e => e.Lbwert98)
                .HasColumnType("money")
                .HasColumnName("LBWert98");
            entity.Property(e => e.Lbwert99)
                .HasColumnType("money")
                .HasColumnName("LBWert99");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Tbaw).HasColumnName("TBAW");
            entity.Property(e => e.Tbnr).HasColumnName("TBNR");
            entity.Property(e => e.Wert1).HasColumnType("money");
            entity.Property(e => e.Wert10).HasColumnType("money");
            entity.Property(e => e.Wert100).HasColumnType("money");
            entity.Property(e => e.Wert11).HasColumnType("money");
            entity.Property(e => e.Wert12).HasColumnType("money");
            entity.Property(e => e.Wert13).HasColumnType("money");
            entity.Property(e => e.Wert14).HasColumnType("money");
            entity.Property(e => e.Wert15).HasColumnType("money");
            entity.Property(e => e.Wert16).HasColumnType("money");
            entity.Property(e => e.Wert17).HasColumnType("money");
            entity.Property(e => e.Wert18).HasColumnType("money");
            entity.Property(e => e.Wert19).HasColumnType("money");
            entity.Property(e => e.Wert2).HasColumnType("money");
            entity.Property(e => e.Wert20).HasColumnType("money");
            entity.Property(e => e.Wert21).HasColumnType("money");
            entity.Property(e => e.Wert22).HasColumnType("money");
            entity.Property(e => e.Wert23).HasColumnType("money");
            entity.Property(e => e.Wert24).HasColumnType("money");
            entity.Property(e => e.Wert25).HasColumnType("money");
            entity.Property(e => e.Wert26).HasColumnType("money");
            entity.Property(e => e.Wert27).HasColumnType("money");
            entity.Property(e => e.Wert28).HasColumnType("money");
            entity.Property(e => e.Wert29).HasColumnType("money");
            entity.Property(e => e.Wert3).HasColumnType("money");
            entity.Property(e => e.Wert30).HasColumnType("money");
            entity.Property(e => e.Wert31).HasColumnType("money");
            entity.Property(e => e.Wert32).HasColumnType("money");
            entity.Property(e => e.Wert33).HasColumnType("money");
            entity.Property(e => e.Wert34).HasColumnType("money");
            entity.Property(e => e.Wert35).HasColumnType("money");
            entity.Property(e => e.Wert36).HasColumnType("money");
            entity.Property(e => e.Wert37).HasColumnType("money");
            entity.Property(e => e.Wert38).HasColumnType("money");
            entity.Property(e => e.Wert39).HasColumnType("money");
            entity.Property(e => e.Wert4).HasColumnType("money");
            entity.Property(e => e.Wert40).HasColumnType("money");
            entity.Property(e => e.Wert41).HasColumnType("money");
            entity.Property(e => e.Wert42).HasColumnType("money");
            entity.Property(e => e.Wert43).HasColumnType("money");
            entity.Property(e => e.Wert44).HasColumnType("money");
            entity.Property(e => e.Wert45).HasColumnType("money");
            entity.Property(e => e.Wert46).HasColumnType("money");
            entity.Property(e => e.Wert47).HasColumnType("money");
            entity.Property(e => e.Wert48).HasColumnType("money");
            entity.Property(e => e.Wert49).HasColumnType("money");
            entity.Property(e => e.Wert5).HasColumnType("money");
            entity.Property(e => e.Wert50).HasColumnType("money");
            entity.Property(e => e.Wert51).HasColumnType("money");
            entity.Property(e => e.Wert52).HasColumnType("money");
            entity.Property(e => e.Wert53).HasColumnType("money");
            entity.Property(e => e.Wert54).HasColumnType("money");
            entity.Property(e => e.Wert55).HasColumnType("money");
            entity.Property(e => e.Wert56).HasColumnType("money");
            entity.Property(e => e.Wert57).HasColumnType("money");
            entity.Property(e => e.Wert58).HasColumnType("money");
            entity.Property(e => e.Wert59).HasColumnType("money");
            entity.Property(e => e.Wert6).HasColumnType("money");
            entity.Property(e => e.Wert60).HasColumnType("money");
            entity.Property(e => e.Wert61).HasColumnType("money");
            entity.Property(e => e.Wert62).HasColumnType("money");
            entity.Property(e => e.Wert63).HasColumnType("money");
            entity.Property(e => e.Wert64).HasColumnType("money");
            entity.Property(e => e.Wert65).HasColumnType("money");
            entity.Property(e => e.Wert66).HasColumnType("money");
            entity.Property(e => e.Wert67).HasColumnType("money");
            entity.Property(e => e.Wert68).HasColumnType("money");
            entity.Property(e => e.Wert69).HasColumnType("money");
            entity.Property(e => e.Wert7).HasColumnType("money");
            entity.Property(e => e.Wert70).HasColumnType("money");
            entity.Property(e => e.Wert71).HasColumnType("money");
            entity.Property(e => e.Wert72).HasColumnType("money");
            entity.Property(e => e.Wert73).HasColumnType("money");
            entity.Property(e => e.Wert74).HasColumnType("money");
            entity.Property(e => e.Wert75).HasColumnType("money");
            entity.Property(e => e.Wert76).HasColumnType("money");
            entity.Property(e => e.Wert77).HasColumnType("money");
            entity.Property(e => e.Wert78).HasColumnType("money");
            entity.Property(e => e.Wert79).HasColumnType("money");
            entity.Property(e => e.Wert8).HasColumnType("money");
            entity.Property(e => e.Wert80).HasColumnType("money");
            entity.Property(e => e.Wert81).HasColumnType("money");
            entity.Property(e => e.Wert82).HasColumnType("money");
            entity.Property(e => e.Wert83).HasColumnType("money");
            entity.Property(e => e.Wert84).HasColumnType("money");
            entity.Property(e => e.Wert85).HasColumnType("money");
            entity.Property(e => e.Wert86).HasColumnType("money");
            entity.Property(e => e.Wert87).HasColumnType("money");
            entity.Property(e => e.Wert88).HasColumnType("money");
            entity.Property(e => e.Wert89).HasColumnType("money");
            entity.Property(e => e.Wert9).HasColumnType("money");
            entity.Property(e => e.Wert90).HasColumnType("money");
            entity.Property(e => e.Wert91).HasColumnType("money");
            entity.Property(e => e.Wert92).HasColumnType("money");
            entity.Property(e => e.Wert93).HasColumnType("money");
            entity.Property(e => e.Wert94).HasColumnType("money");
            entity.Property(e => e.Wert95).HasColumnType("money");
            entity.Property(e => e.Wert96).HasColumnType("money");
            entity.Property(e => e.Wert97).HasColumnType("money");
            entity.Property(e => e.Wert98).HasColumnType("money");
            entity.Property(e => e.Wert99).HasColumnType("money");
        });

        modelBuilder.Entity<Ezg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EZG$ID");

            entity.ToTable("EZG");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Ezganrede)
                .HasMaxLength(30)
                .HasColumnName("EZGAnrede");
            entity.Property(e => e.Ezgbeitrag)
                .HasColumnType("money")
                .HasColumnName("EZGBeitrag");
            entity.Property(e => e.Ezgbem).HasColumnName("EZGBem");
            entity.Property(e => e.Ezgcheck1).HasColumnName("EZGCheck1");
            entity.Property(e => e.Ezgcheck2).HasColumnName("EZGCheck2");
            entity.Property(e => e.Ezgcheck3).HasColumnName("EZGCheck3");
            entity.Property(e => e.Ezgcheck4).HasColumnName("EZGCheck4");
            entity.Property(e => e.Ezgemail)
                .HasMaxLength(50)
                .HasColumnName("EZGEMail");
            entity.Property(e => e.Ezgfax)
                .HasMaxLength(20)
                .HasColumnName("EZGFax");
            entity.Property(e => e.EzgfilName)
                .HasMaxLength(30)
                .HasColumnName("EZGFilName");
            entity.Property(e => e.Ezgfiliale)
                .HasMaxLength(2)
                .HasColumnName("EZGFiliale");
            entity.Property(e => e.Ezgkng1)
                .HasMaxLength(3)
                .HasColumnName("EZGKng1");
            entity.Property(e => e.Ezgkng10)
                .HasMaxLength(3)
                .HasColumnName("EZGKng10");
            entity.Property(e => e.Ezgkng11)
                .HasMaxLength(3)
                .HasColumnName("EZGKng11");
            entity.Property(e => e.Ezgkng2)
                .HasMaxLength(3)
                .HasColumnName("EZGKng2");
            entity.Property(e => e.Ezgkng3)
                .HasMaxLength(3)
                .HasColumnName("EZGKng3");
            entity.Property(e => e.Ezgkng4)
                .HasMaxLength(3)
                .HasColumnName("EZGKng4");
            entity.Property(e => e.Ezgkng5)
                .HasMaxLength(3)
                .HasColumnName("EZGKng5");
            entity.Property(e => e.Ezgkng6)
                .HasMaxLength(3)
                .HasColumnName("EZGKng6");
            entity.Property(e => e.Ezgkng7)
                .HasMaxLength(3)
                .HasColumnName("EZGKng7");
            entity.Property(e => e.Ezgkng8)
                .HasMaxLength(3)
                .HasColumnName("EZGKng8");
            entity.Property(e => e.Ezgkng9)
                .HasMaxLength(3)
                .HasColumnName("EZGKng9");
            entity.Property(e => e.Ezgkonto)
                .HasMaxLength(4)
                .HasColumnName("EZGKonto");
            entity.Property(e => e.Ezgland)
                .HasMaxLength(30)
                .HasColumnName("EZGLand");
            entity.Property(e => e.Ezgname1)
                .HasMaxLength(30)
                .HasColumnName("EZGName1");
            entity.Property(e => e.Ezgname2)
                .HasMaxLength(30)
                .HasColumnName("EZGName2");
            entity.Property(e => e.Ezgname3)
                .HasMaxLength(30)
                .HasColumnName("EZGName3");
            entity.Property(e => e.Ezgnr).HasColumnName("EZGNr");
            entity.Property(e => e.Ezgort)
                .HasMaxLength(30)
                .HasColumnName("EZGOrt");
            entity.Property(e => e.Ezgplz)
                .HasMaxLength(10)
                .HasColumnName("EZGPLZ");
            entity.Property(e => e.Ezgsbg)
                .HasMaxLength(10)
                .HasColumnName("EZGSBG");
            entity.Property(e => e.Ezgstrasse)
                .HasMaxLength(30)
                .HasColumnName("EZGStrasse");
            entity.Property(e => e.Ezgtelefon1)
                .HasMaxLength(20)
                .HasColumnName("EZGTelefon1");
            entity.Property(e => e.Ezgtelefon2)
                .HasMaxLength(20)
                .HasColumnName("EZGTelefon2");
            entity.Property(e => e.EzgwgBez)
                .HasMaxLength(10)
                .HasColumnName("EZGWgBez");
            entity.Property(e => e.Ezgwährung)
                .HasMaxLength(2)
                .HasColumnName("EZGWährung");
            entity.Property(e => e.Ezgzuschlag)
                .HasColumnType("money")
                .HasColumnName("EZGZuschlag");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<FbezKonto>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("FBezKonto$ID");

            entity.ToTable("FBezKonto");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Erledigt)
                .HasDefaultValue(false)
                .HasColumnName("erledigt");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Renr).HasColumnName("RENr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(50);
        });

        modelBuilder.Entity<Fbkonto>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("FBKonto$ID");

            entity.ToTable("FBKonto");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.FbaktMge)
                .HasColumnType("money")
                .HasColumnName("FBAktMge");
            entity.Property(e => e.FbartNr).HasColumnName("FBArtNr");
            entity.Property(e => e.Fbdatum)
                .HasColumnType("datetime")
                .HasColumnName("FBDatum");
            entity.Property(e => e.Fbep)
                .HasColumnType("money")
                .HasColumnName("FBEp");
            entity.Property(e => e.FbkdNr).HasColumnName("FBKdNr");
            entity.Property(e => e.Fblsnr).HasColumnName("FBLSNr");
            entity.Property(e => e.Fbopt)
                .HasMaxLength(50)
                .HasColumnName("FBOpt");
            entity.Property(e => e.FbposNr).HasColumnName("FBPosNr");
            entity.Property(e => e.Fbrenr).HasColumnName("FBRENr");
            entity.Property(e => e.FbstartMge)
                .HasColumnType("money")
                .HasColumnName("FBStartMge");
        });

        modelBuilder.Entity<Frachtzonen>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Frachtzonen$ID");

            entity.ToTable("Frachtzonen");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdnr).HasColumnName("KDNr");
            entity.Property(e => e.Lgnr).HasColumnName("LGNr");
        });

        modelBuilder.Entity<Gbvadr>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GBVADR$ID");

            entity.ToTable("GBVADR");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AnzBrutto).HasColumnType("money");
            entity.Property(e => e.AnzNetto).HasColumnType("money");
            entity.Property(e => e.AnzRestOffen).HasColumnType("money");
            entity.Property(e => e.AnzStartBrutto).HasColumnType("money");
            entity.Property(e => e.AnzStartNetto).HasColumnType("money");
            entity.Property(e => e.BioNr).HasMaxLength(50);
            entity.Property(e => e.Gbabfall)
                .HasColumnType("money")
                .HasColumnName("GBAbfall");
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzgBetr1)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr1");
            entity.Property(e => e.GbabzgBetr2)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr2");
            entity.Property(e => e.GbabzgBetr3)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr3");
            entity.Property(e => e.GbabzgBetr4)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr4");
            entity.Property(e => e.GbabzgBetr5)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr5");
            entity.Property(e => e.GbabzgText1)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText1");
            entity.Property(e => e.GbabzgText2)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText2");
            entity.Property(e => e.GbabzgText3)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText3");
            entity.Property(e => e.GbabzgText4)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText4");
            entity.Property(e => e.GbabzgText5)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText5");
            entity.Property(e => e.GbabzgVz1)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ1");
            entity.Property(e => e.GbabzgVz2)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ2");
            entity.Property(e => e.GbabzgVz3)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ3");
            entity.Property(e => e.GbabzgVz4)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ4");
            entity.Property(e => e.GbabzgVz5)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ5");
            entity.Property(e => e.Gbabzug)
                .HasColumnType("money")
                .HasColumnName("GBAbzug");
            entity.Property(e => e.Gbaltnr).HasColumnName("GBAltnr");
            entity.Property(e => e.GbangAbfall)
                .HasColumnType("money")
                .HasColumnName("GBAngAbfall");
            entity.Property(e => e.Gbanlfnr).HasColumnName("GBANLFNR");
            entity.Property(e => e.Gbanrede)
                .HasMaxLength(30)
                .HasColumnName("GBAnrede");
            entity.Property(e => e.Gbart).HasColumnName("GBArt");
            entity.Property(e => e.Gbasld).HasColumnName("GBASLD");
            entity.Property(e => e.Gbbank)
                .HasMaxLength(30)
                .HasColumnName("GBBank");
            entity.Property(e => e.Gbbld)
                .HasMaxLength(30)
                .HasColumnName("GBBLD");
            entity.Property(e => e.Gbbldkng).HasColumnName("GBBLDKng");
            entity.Property(e => e.Gbblz)
                .HasMaxLength(10)
                .HasColumnName("GBBLZ");
            entity.Property(e => e.Gbbrutto)
                .HasColumnType("money")
                .HasColumnName("GBBrutto");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.GbdruckKz)
                .HasMaxLength(1)
                .HasColumnName("GBDruckKz");
            entity.Property(e => e.GbentfKl)
                .HasMaxLength(2)
                .HasColumnName("GBEntfKL");
            entity.Property(e => e.GbentfKlzuEur)
                .HasColumnType("money")
                .HasColumnName("GBEntfKLZuEUR");
            entity.Property(e => e.Gbezg)
                .HasDefaultValue(false)
                .HasColumnName("GBEZG");
            entity.Property(e => e.Gbezgbetr)
                .HasColumnType("money")
                .HasColumnName("GBEZGBetr");
            entity.Property(e => e.Gbfax)
                .HasMaxLength(20)
                .HasColumnName("GBFax");
            entity.Property(e => e.Gbfibu2)
                .HasDefaultValue(false)
                .HasColumnName("GBFibu2");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.GbgesMenge)
                .HasColumnType("money")
                .HasColumnName("GBGesMenge");
            entity.Property(e => e.Gbhaendler)
                .HasDefaultValue(false)
                .HasColumnName("GBHaendler");
            entity.Property(e => e.Gbiban)
                .HasMaxLength(30)
                .HasColumnName("GBIBAN");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(20)
                .HasColumnName("GBKonto");
            entity.Property(e => e.Gbktnr).HasColumnName("GBKTNr");
            entity.Property(e => e.GbktrNrLf)
                .HasMaxLength(10)
                .HasColumnName("GBKtrNrLF");
            entity.Property(e => e.Gbland)
                .HasMaxLength(30)
                .HasColumnName("GBLand");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblfstatus)
                .HasMaxLength(15)
                .HasColumnName("GBLFStatus");
            entity.Property(e => e.GbmitText1)
                .HasMaxLength(60)
                .HasColumnName("GBMitText1");
            entity.Property(e => e.GbmitText2)
                .HasMaxLength(60)
                .HasColumnName("GBMitText2");
            entity.Property(e => e.Gbmwst)
                .HasColumnType("money")
                .HasColumnName("GBMwst");
            entity.Property(e => e.Gbnachzhlg)
                .HasMaxLength(1)
                .HasColumnName("GBNachzhlg");
            entity.Property(e => e.Gbname1)
                .HasMaxLength(30)
                .HasColumnName("GBName1");
            entity.Property(e => e.Gbname2)
                .HasMaxLength(30)
                .HasColumnName("GBName2");
            entity.Property(e => e.Gbname3)
                .HasMaxLength(30)
                .HasColumnName("GBName3");
            entity.Property(e => e.Gbnetto)
                .HasColumnType("money")
                .HasColumnName("GBNetto");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.Gbort)
                .HasMaxLength(30)
                .HasColumnName("GBOrt");
            entity.Property(e => e.Gbplz)
                .HasMaxLength(10)
                .HasColumnName("GBPLZ");
            entity.Property(e => e.GbreNrLf)
                .HasMaxLength(25)
                .HasColumnName("GBReNrLF");
            entity.Property(e => e.Gbsbg)
                .HasMaxLength(10)
                .HasColumnName("GBSBG");
            entity.Property(e => e.GbstNr)
                .HasMaxLength(20)
                .HasColumnName("GBStNr");
            entity.Property(e => e.GbstProz)
                .HasColumnType("money")
                .HasColumnName("GBStProz");
            entity.Property(e => e.GbstProz2).HasColumnName("GBStProz2");
            entity.Property(e => e.GbstSchl).HasColumnName("GBStSchl");
            entity.Property(e => e.Gbstorno)
                .HasMaxLength(1)
                .HasColumnName("GBStorno");
            entity.Property(e => e.Gbstrasse)
                .HasMaxLength(30)
                .HasColumnName("GBStrasse");
            entity.Property(e => e.Gbswift)
                .HasMaxLength(30)
                .HasColumnName("GBSWIFT");
            entity.Property(e => e.Gbtelefon)
                .HasMaxLength(20)
                .HasColumnName("GBTelefon");
            entity.Property(e => e.Gbustidnr)
                .HasMaxLength(18)
                .HasColumnName("GBUSTIDNR");
            entity.Property(e => e.Gbvaluta)
                .HasMaxLength(60)
                .HasColumnName("GBValuta");
            entity.Property(e => e.GbvalutaDatum)
                .HasColumnType("datetime")
                .HasColumnName("GBValutaDatum");
            entity.Property(e => e.Gbvwhrg)
                .HasMaxLength(4)
                .HasColumnName("GBVWhrg");
            entity.Property(e => e.GbvwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("GBVWhrgKurs");
            entity.Property(e => e.GbzahlBetrag)
                .HasColumnType("money")
                .HasColumnName("GBZahlBetrag");
            entity.Property(e => e.GbzuschlEur)
                .HasColumnType("money")
                .HasColumnName("GBZuschlEUR");
            entity.Property(e => e.GbzuschlSumme)
                .HasColumnType("money")
                .HasColumnName("GBZuschlSumme");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Gbvhpt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GBVHPT$ID");

            entity.ToTable("GBVHPT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BuchMenge).HasColumnType("money");
            entity.Property(e => e.ErfSchema).HasMaxLength(2);
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzGew)
                .HasColumnType("money")
                .HasColumnName("GBAbzGew");
            entity.Property(e => e.GbanlGew)
                .HasColumnType("money")
                .HasColumnName("GBAnlGew");
            entity.Property(e => e.GbartNr).HasColumnName("GBArtNr");
            entity.Property(e => e.Gbep)
                .HasColumnType("money")
                .HasColumnName("GBEP");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.Gbfracht)
                .HasColumnType("money")
                .HasColumnName("GBFracht");
            entity.Property(e => e.GbgesNetto)
                .HasColumnType("money")
                .HasColumnName("GBGesNetto");
            entity.Property(e => e.GbgetrKng)
                .HasMaxLength(3)
                .HasColumnName("GBGetrKng");
            entity.Property(e => e.GbgridPos).HasColumnName("GBGridPos");
            entity.Property(e => e.Gbhartikel).HasColumnName("GBHArtikel");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(10)
                .HasColumnName("GBKonto");
            entity.Property(e => e.GbkontrNr).HasColumnName("GBKontrNr");
            entity.Property(e => e.Gbkz)
                .HasMaxLength(10)
                .HasColumnName("GBKz");
            entity.Property(e => e.Gblbeh)
                .HasMaxLength(5)
                .HasColumnName("GBLBEH");
            entity.Property(e => e.Gblbwert)
                .HasColumnType("money")
                .HasColumnName("GBLBWert");
            entity.Property(e => e.Gblkw)
                .HasMaxLength(15)
                .HasColumnName("GBLKW");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.GbstProz).HasColumnName("GBStProz");
            entity.Property(e => e.GbstreckNr).HasColumnName("GBStreckNr");
            entity.Property(e => e.Gbtext)
                .HasMaxLength(255)
                .HasColumnName("GBText");
            entity.Property(e => e.Gbvznr).HasColumnName("GBVZNr");
            entity.Property(e => e.GbwgsNr).HasColumnName("GBWgsNr");
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.Tp)
                .HasDefaultValue(false)
                .HasColumnName("TP");
        });

        modelBuilder.Entity<Getreideparameter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Getreideparameter$ID");

            entity.ToTable("Getreideparameter");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abrgesp).HasMaxLength(1);
            entity.Property(e => e.Bezeichnung).HasMaxLength(18);
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Kennung).HasMaxLength(3);
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Nfaktor1)
                .HasColumnType("money")
                .HasColumnName("NFaktor1");
            entity.Property(e => e.Nfaktor10)
                .HasColumnType("money")
                .HasColumnName("NFaktor10");
            entity.Property(e => e.Nfaktor11)
                .HasColumnType("money")
                .HasColumnName("NFaktor11");
            entity.Property(e => e.Nfaktor12)
                .HasColumnType("money")
                .HasColumnName("NFaktor12");
            entity.Property(e => e.Nfaktor13)
                .HasColumnType("money")
                .HasColumnName("NFaktor13");
            entity.Property(e => e.Nfaktor14)
                .HasColumnType("money")
                .HasColumnName("NFaktor14");
            entity.Property(e => e.Nfaktor15)
                .HasColumnType("money")
                .HasColumnName("NFaktor15");
            entity.Property(e => e.Nfaktor16)
                .HasColumnType("money")
                .HasColumnName("NFaktor16");
            entity.Property(e => e.Nfaktor17)
                .HasColumnType("money")
                .HasColumnName("NFaktor17");
            entity.Property(e => e.Nfaktor18)
                .HasColumnType("money")
                .HasColumnName("NFaktor18");
            entity.Property(e => e.Nfaktor19)
                .HasColumnType("money")
                .HasColumnName("NFaktor19");
            entity.Property(e => e.Nfaktor2)
                .HasColumnType("money")
                .HasColumnName("NFaktor2");
            entity.Property(e => e.Nfaktor20)
                .HasColumnType("money")
                .HasColumnName("NFaktor20");
            entity.Property(e => e.Nfaktor21)
                .HasColumnType("money")
                .HasColumnName("NFaktor21");
            entity.Property(e => e.Nfaktor22)
                .HasColumnType("money")
                .HasColumnName("NFaktor22");
            entity.Property(e => e.Nfaktor23)
                .HasColumnType("money")
                .HasColumnName("NFaktor23");
            entity.Property(e => e.Nfaktor24)
                .HasColumnType("money")
                .HasColumnName("NFaktor24");
            entity.Property(e => e.Nfaktor25)
                .HasColumnType("money")
                .HasColumnName("NFaktor25");
            entity.Property(e => e.Nfaktor26)
                .HasColumnType("money")
                .HasColumnName("NFaktor26");
            entity.Property(e => e.Nfaktor27)
                .HasColumnType("money")
                .HasColumnName("NFaktor27");
            entity.Property(e => e.Nfaktor28)
                .HasColumnType("money")
                .HasColumnName("NFaktor28");
            entity.Property(e => e.Nfaktor29)
                .HasColumnType("money")
                .HasColumnName("NFaktor29");
            entity.Property(e => e.Nfaktor3)
                .HasColumnType("money")
                .HasColumnName("NFaktor3");
            entity.Property(e => e.Nfaktor30)
                .HasColumnType("money")
                .HasColumnName("NFaktor30");
            entity.Property(e => e.Nfaktor31)
                .HasColumnType("money")
                .HasColumnName("NFaktor31");
            entity.Property(e => e.Nfaktor32)
                .HasColumnType("money")
                .HasColumnName("NFaktor32");
            entity.Property(e => e.Nfaktor33)
                .HasColumnType("money")
                .HasColumnName("NFaktor33");
            entity.Property(e => e.Nfaktor34)
                .HasColumnType("money")
                .HasColumnName("NFaktor34");
            entity.Property(e => e.Nfaktor35)
                .HasColumnType("money")
                .HasColumnName("NFaktor35");
            entity.Property(e => e.Nfaktor36)
                .HasColumnType("money")
                .HasColumnName("NFaktor36");
            entity.Property(e => e.Nfaktor4)
                .HasColumnType("money")
                .HasColumnName("NFaktor4");
            entity.Property(e => e.Nfaktor5)
                .HasColumnType("money")
                .HasColumnName("NFaktor5");
            entity.Property(e => e.Nfaktor6)
                .HasColumnType("money")
                .HasColumnName("NFaktor6");
            entity.Property(e => e.Nfaktor7)
                .HasColumnType("money")
                .HasColumnName("NFaktor7");
            entity.Property(e => e.Nfaktor8)
                .HasColumnType("money")
                .HasColumnName("NFaktor8");
            entity.Property(e => e.Nfaktor9)
                .HasColumnType("money")
                .HasColumnName("NFaktor9");
            entity.Property(e => e.Nft)
                .HasMaxLength(2)
                .HasColumnName("NFT");
            entity.Property(e => e.Nlaborpos1)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos1");
            entity.Property(e => e.Nlaborpos10)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos10");
            entity.Property(e => e.Nlaborpos11)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos11");
            entity.Property(e => e.Nlaborpos12)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos12");
            entity.Property(e => e.Nlaborpos2)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos2");
            entity.Property(e => e.Nlaborpos3)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos3");
            entity.Property(e => e.Nlaborpos4)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos4");
            entity.Property(e => e.Nlaborpos5)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos5");
            entity.Property(e => e.Nlaborpos6)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos6");
            entity.Property(e => e.Nlaborpos7)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos7");
            entity.Property(e => e.Nlaborpos8)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos8");
            entity.Property(e => e.Nlaborpos9)
                .HasMaxLength(4)
                .HasColumnName("NLaborpos9");
            entity.Property(e => e.Npreis)
                .HasColumnType("money")
                .HasColumnName("NPreis");
            entity.Property(e => e.Ntk)
                .HasMaxLength(2)
                .HasColumnName("NTK");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
            entity.Property(e => e.Vfaktor1)
                .HasColumnType("money")
                .HasColumnName("VFaktor1");
            entity.Property(e => e.Vfaktor10)
                .HasColumnType("money")
                .HasColumnName("VFaktor10");
            entity.Property(e => e.Vfaktor11)
                .HasColumnType("money")
                .HasColumnName("VFaktor11");
            entity.Property(e => e.Vfaktor12)
                .HasColumnType("money")
                .HasColumnName("VFaktor12");
            entity.Property(e => e.Vfaktor13)
                .HasColumnType("money")
                .HasColumnName("VFaktor13");
            entity.Property(e => e.Vfaktor14)
                .HasColumnType("money")
                .HasColumnName("VFaktor14");
            entity.Property(e => e.Vfaktor15)
                .HasColumnType("money")
                .HasColumnName("VFaktor15");
            entity.Property(e => e.Vfaktor16)
                .HasColumnType("money")
                .HasColumnName("VFaktor16");
            entity.Property(e => e.Vfaktor17)
                .HasColumnType("money")
                .HasColumnName("VFaktor17");
            entity.Property(e => e.Vfaktor18)
                .HasColumnType("money")
                .HasColumnName("VFaktor18");
            entity.Property(e => e.Vfaktor19)
                .HasColumnType("money")
                .HasColumnName("VFaktor19");
            entity.Property(e => e.Vfaktor2)
                .HasColumnType("money")
                .HasColumnName("VFaktor2");
            entity.Property(e => e.Vfaktor20)
                .HasColumnType("money")
                .HasColumnName("VFaktor20");
            entity.Property(e => e.Vfaktor21)
                .HasColumnType("money")
                .HasColumnName("VFaktor21");
            entity.Property(e => e.Vfaktor22)
                .HasColumnType("money")
                .HasColumnName("VFaktor22");
            entity.Property(e => e.Vfaktor23)
                .HasColumnType("money")
                .HasColumnName("VFaktor23");
            entity.Property(e => e.Vfaktor24)
                .HasColumnType("money")
                .HasColumnName("VFaktor24");
            entity.Property(e => e.Vfaktor25)
                .HasColumnType("money")
                .HasColumnName("VFaktor25");
            entity.Property(e => e.Vfaktor26)
                .HasColumnType("money")
                .HasColumnName("VFaktor26");
            entity.Property(e => e.Vfaktor27)
                .HasColumnType("money")
                .HasColumnName("VFaktor27");
            entity.Property(e => e.Vfaktor28)
                .HasColumnType("money")
                .HasColumnName("VFaktor28");
            entity.Property(e => e.Vfaktor29)
                .HasColumnType("money")
                .HasColumnName("VFaktor29");
            entity.Property(e => e.Vfaktor3)
                .HasColumnType("money")
                .HasColumnName("VFaktor3");
            entity.Property(e => e.Vfaktor30)
                .HasColumnType("money")
                .HasColumnName("VFaktor30");
            entity.Property(e => e.Vfaktor31)
                .HasColumnType("money")
                .HasColumnName("VFaktor31");
            entity.Property(e => e.Vfaktor32)
                .HasColumnType("money")
                .HasColumnName("VFaktor32");
            entity.Property(e => e.Vfaktor33)
                .HasColumnType("money")
                .HasColumnName("VFaktor33");
            entity.Property(e => e.Vfaktor34)
                .HasColumnType("money")
                .HasColumnName("VFaktor34");
            entity.Property(e => e.Vfaktor35)
                .HasColumnType("money")
                .HasColumnName("VFaktor35");
            entity.Property(e => e.Vfaktor36)
                .HasColumnType("money")
                .HasColumnName("VFaktor36");
            entity.Property(e => e.Vfaktor4)
                .HasColumnType("money")
                .HasColumnName("VFaktor4");
            entity.Property(e => e.Vfaktor5)
                .HasColumnType("money")
                .HasColumnName("VFaktor5");
            entity.Property(e => e.Vfaktor6)
                .HasColumnType("money")
                .HasColumnName("VFaktor6");
            entity.Property(e => e.Vfaktor7)
                .HasColumnType("money")
                .HasColumnName("VFaktor7");
            entity.Property(e => e.Vfaktor8)
                .HasColumnType("money")
                .HasColumnName("VFaktor8");
            entity.Property(e => e.Vfaktor9)
                .HasColumnType("money")
                .HasColumnName("VFaktor9");
            entity.Property(e => e.Vft)
                .HasMaxLength(2)
                .HasColumnName("VFT");
            entity.Property(e => e.Vlaborpos1)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos1");
            entity.Property(e => e.Vlaborpos10)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos10");
            entity.Property(e => e.Vlaborpos11)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos11");
            entity.Property(e => e.Vlaborpos12)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos12");
            entity.Property(e => e.Vlaborpos2)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos2");
            entity.Property(e => e.Vlaborpos3)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos3");
            entity.Property(e => e.Vlaborpos4)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos4");
            entity.Property(e => e.Vlaborpos5)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos5");
            entity.Property(e => e.Vlaborpos6)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos6");
            entity.Property(e => e.Vlaborpos7)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos7");
            entity.Property(e => e.Vlaborpos8)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos8");
            entity.Property(e => e.Vlaborpos9)
                .HasMaxLength(4)
                .HasColumnName("VLaborpos9");
            entity.Property(e => e.Vpreis)
                .HasColumnType("money")
                .HasColumnName("VPreis");
            entity.Property(e => e.Vtk)
                .HasMaxLength(2)
                .HasColumnName("VTK");
            entity.Property(e => e.Wgs)
                .HasMaxLength(4)
                .HasColumnName("WGS");
            entity.Property(e => e.WgsBezeichnung1).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung10).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung11).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung12).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung2).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung3).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung4).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung5).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung6).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung7).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung8).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung9).HasMaxLength(18);
            entity.Property(e => e.WgsEinh1).HasMaxLength(3);
            entity.Property(e => e.WgsEinh10).HasMaxLength(3);
            entity.Property(e => e.WgsEinh11).HasMaxLength(3);
            entity.Property(e => e.WgsEinh12).HasMaxLength(3);
            entity.Property(e => e.WgsEinh2).HasMaxLength(3);
            entity.Property(e => e.WgsEinh3).HasMaxLength(3);
            entity.Property(e => e.WgsEinh4).HasMaxLength(3);
            entity.Property(e => e.WgsEinh5).HasMaxLength(3);
            entity.Property(e => e.WgsEinh6).HasMaxLength(3);
            entity.Property(e => e.WgsEinh7).HasMaxLength(3);
            entity.Property(e => e.WgsEinh8).HasMaxLength(3);
            entity.Property(e => e.WgsEinh9).HasMaxLength(3);
            entity.Property(e => e.Wgskennung1)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung1");
            entity.Property(e => e.Wgskennung10)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung10");
            entity.Property(e => e.Wgskennung11)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung11");
            entity.Property(e => e.Wgskennung12)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung12");
            entity.Property(e => e.Wgskennung2)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung2");
            entity.Property(e => e.Wgskennung3)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung3");
            entity.Property(e => e.Wgskennung4)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung4");
            entity.Property(e => e.Wgskennung5)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung5");
            entity.Property(e => e.Wgskennung6)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung6");
            entity.Property(e => e.Wgskennung7)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung7");
            entity.Property(e => e.Wgskennung8)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung8");
            entity.Property(e => e.Wgskennung9)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung9");
        });

        modelBuilder.Entity<Gparam>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GParam$ID");

            entity.ToTable("GParam");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abrgesp).HasDefaultValue(false);
            entity.Property(e => e.Bezeichnung).HasMaxLength(18);
            entity.Property(e => e.Kennung).HasMaxLength(3);
            entity.Property(e => e.Wgs)
                .HasMaxLength(4)
                .HasColumnName("WGS");
            entity.Property(e => e.WgsBezeichnung1).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung10).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung11).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung12).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung2).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung3).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung4).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung5).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung6).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung7).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung8).HasMaxLength(18);
            entity.Property(e => e.WgsBezeichnung9).HasMaxLength(18);
            entity.Property(e => e.WgsEinh1).HasMaxLength(3);
            entity.Property(e => e.WgsEinh10).HasMaxLength(3);
            entity.Property(e => e.WgsEinh11).HasMaxLength(3);
            entity.Property(e => e.WgsEinh12).HasMaxLength(3);
            entity.Property(e => e.WgsEinh2).HasMaxLength(3);
            entity.Property(e => e.WgsEinh3).HasMaxLength(3);
            entity.Property(e => e.WgsEinh4).HasMaxLength(3);
            entity.Property(e => e.WgsEinh5).HasMaxLength(3);
            entity.Property(e => e.WgsEinh6).HasMaxLength(3);
            entity.Property(e => e.WgsEinh7).HasMaxLength(3);
            entity.Property(e => e.WgsEinh8).HasMaxLength(3);
            entity.Property(e => e.WgsEinh9).HasMaxLength(3);
            entity.Property(e => e.Wgskennung1)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung1");
            entity.Property(e => e.Wgskennung10)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung10");
            entity.Property(e => e.Wgskennung11)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung11");
            entity.Property(e => e.Wgskennung12)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung12");
            entity.Property(e => e.Wgskennung2)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung2");
            entity.Property(e => e.Wgskennung3)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung3");
            entity.Property(e => e.Wgskennung4)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung4");
            entity.Property(e => e.Wgskennung5)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung5");
            entity.Property(e => e.Wgskennung6)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung6");
            entity.Property(e => e.Wgskennung7)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung7");
            entity.Property(e => e.Wgskennung8)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung8");
            entity.Property(e => e.Wgskennung9)
                .HasMaxLength(4)
                .HasColumnName("WGSKennung9");
        });

        modelBuilder.Entity<GparamNo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GParamNO$ID");

            entity.ToTable("GParamNO");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<GparamNz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GParamNZ$ID");

            entity.ToTable("GParamNZ");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<GparamVz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GParamVZ$ID");

            entity.ToTable("GParamVZ");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<GparamWg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GParamWGS$ID");

            entity.ToTable("GParamWGS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Faktor1).HasColumnType("money");
            entity.Property(e => e.Faktor10).HasColumnType("money");
            entity.Property(e => e.Faktor11).HasColumnType("money");
            entity.Property(e => e.Faktor12).HasColumnType("money");
            entity.Property(e => e.Faktor13).HasColumnType("money");
            entity.Property(e => e.Faktor14).HasColumnType("money");
            entity.Property(e => e.Faktor15).HasColumnType("money");
            entity.Property(e => e.Faktor16).HasColumnType("money");
            entity.Property(e => e.Faktor17).HasColumnType("money");
            entity.Property(e => e.Faktor18).HasColumnType("money");
            entity.Property(e => e.Faktor19).HasColumnType("money");
            entity.Property(e => e.Faktor2).HasColumnType("money");
            entity.Property(e => e.Faktor20).HasColumnType("money");
            entity.Property(e => e.Faktor21).HasColumnType("money");
            entity.Property(e => e.Faktor22).HasColumnType("money");
            entity.Property(e => e.Faktor23).HasColumnType("money");
            entity.Property(e => e.Faktor24).HasColumnType("money");
            entity.Property(e => e.Faktor25).HasColumnType("money");
            entity.Property(e => e.Faktor26).HasColumnType("money");
            entity.Property(e => e.Faktor27).HasColumnType("money");
            entity.Property(e => e.Faktor28).HasColumnType("money");
            entity.Property(e => e.Faktor29).HasColumnType("money");
            entity.Property(e => e.Faktor3).HasColumnType("money");
            entity.Property(e => e.Faktor30).HasColumnType("money");
            entity.Property(e => e.Faktor31).HasColumnType("money");
            entity.Property(e => e.Faktor32).HasColumnType("money");
            entity.Property(e => e.Faktor33).HasColumnType("money");
            entity.Property(e => e.Faktor34).HasColumnType("money");
            entity.Property(e => e.Faktor35).HasColumnType("money");
            entity.Property(e => e.Faktor36).HasColumnType("money");
            entity.Property(e => e.Faktor4).HasColumnType("money");
            entity.Property(e => e.Faktor5).HasColumnType("money");
            entity.Property(e => e.Faktor6).HasColumnType("money");
            entity.Property(e => e.Faktor7).HasColumnType("money");
            entity.Property(e => e.Faktor8).HasColumnType("money");
            entity.Property(e => e.Faktor9).HasColumnType("money");
            entity.Property(e => e.Ft)
                .HasMaxLength(2)
                .HasColumnName("FT");
            entity.Property(e => e.Laborpos1).HasMaxLength(4);
            entity.Property(e => e.Laborpos10).HasMaxLength(4);
            entity.Property(e => e.Laborpos11).HasMaxLength(4);
            entity.Property(e => e.Laborpos12).HasMaxLength(4);
            entity.Property(e => e.Laborpos2).HasMaxLength(4);
            entity.Property(e => e.Laborpos3).HasMaxLength(4);
            entity.Property(e => e.Laborpos4).HasMaxLength(4);
            entity.Property(e => e.Laborpos5).HasMaxLength(4);
            entity.Property(e => e.Laborpos6).HasMaxLength(4);
            entity.Property(e => e.Laborpos7).HasMaxLength(4);
            entity.Property(e => e.Laborpos8).HasMaxLength(4);
            entity.Property(e => e.Laborpos9).HasMaxLength(4);
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Tk)
                .HasMaxLength(2)
                .HasColumnName("TK");
        });

        modelBuilder.Entity<Gpfad>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GPfad$ID");

            entity.ToTable("GPfad");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Gtabelle>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GTabelle$ID");

            entity.ToTable("GTabelle");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Gw1bis40)
                .HasMaxLength(240)
                .HasColumnName("GW1bis40");
            entity.Property(e => e.Gw41bis80)
                .HasMaxLength(240)
                .HasColumnName("GW41bis80");
            entity.Property(e => e.Gw81bis100)
                .HasMaxLength(120)
                .HasColumnName("GW81bis100");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Tbaw).HasColumnName("TBAW");
            entity.Property(e => e.Tbnr)
                .HasMaxLength(3)
                .HasColumnName("TBNR");
            entity.Property(e => e.Von1bis40).HasMaxLength(240);
            entity.Property(e => e.Von41bis80).HasMaxLength(240);
            entity.Property(e => e.Von81bis100).HasMaxLength(120);
            entity.Property(e => e.Zu1bis40)
                .HasMaxLength(240)
                .HasColumnName("ZU1bis40");
            entity.Property(e => e.Zu41bis80)
                .HasMaxLength(240)
                .HasColumnName("ZU41bis80");
            entity.Property(e => e.Zu81bis100)
                .HasMaxLength(120)
                .HasColumnName("ZU81bis100");
        });

        modelBuilder.Entity<IntraStat>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("IntraStat$ID");

            entity.ToTable("IntraStat");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.KontoEgek)
                .HasDefaultValue((short)0)
                .HasColumnName("KontoEGEK");
            entity.Property(e => e.KontoEgvk)
                .HasDefaultValue((short)0)
                .HasColumnName("KontoEGVK");
        });

        modelBuilder.Entity<Kanalyse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KAnalyse$ID");

            entity.ToTable("KAnalyse");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Aartnr).HasColumnName("AARTNR");
            entity.Property(e => e.Adesc1)
                .HasMaxLength(5)
                .HasColumnName("ADesc1");
            entity.Property(e => e.Adesc10)
                .HasMaxLength(5)
                .HasColumnName("ADesc10");
            entity.Property(e => e.Adesc11)
                .HasMaxLength(5)
                .HasColumnName("ADesc11");
            entity.Property(e => e.Adesc12)
                .HasMaxLength(5)
                .HasColumnName("ADesc12");
            entity.Property(e => e.Adesc13)
                .HasMaxLength(5)
                .HasColumnName("ADesc13");
            entity.Property(e => e.Adesc14)
                .HasMaxLength(5)
                .HasColumnName("ADesc14");
            entity.Property(e => e.Adesc15)
                .HasMaxLength(5)
                .HasColumnName("ADesc15");
            entity.Property(e => e.Adesc16)
                .HasMaxLength(5)
                .HasColumnName("ADesc16");
            entity.Property(e => e.Adesc17)
                .HasMaxLength(5)
                .HasColumnName("ADesc17");
            entity.Property(e => e.Adesc18)
                .HasMaxLength(5)
                .HasColumnName("ADesc18");
            entity.Property(e => e.Adesc19)
                .HasMaxLength(5)
                .HasColumnName("ADesc19");
            entity.Property(e => e.Adesc2)
                .HasMaxLength(5)
                .HasColumnName("ADesc2");
            entity.Property(e => e.Adesc20)
                .HasMaxLength(5)
                .HasColumnName("ADesc20");
            entity.Property(e => e.Adesc21)
                .HasMaxLength(5)
                .HasColumnName("ADesc21");
            entity.Property(e => e.Adesc22)
                .HasMaxLength(5)
                .HasColumnName("ADesc22");
            entity.Property(e => e.Adesc23)
                .HasMaxLength(5)
                .HasColumnName("ADesc23");
            entity.Property(e => e.Adesc24)
                .HasMaxLength(5)
                .HasColumnName("ADesc24");
            entity.Property(e => e.Adesc3)
                .HasMaxLength(5)
                .HasColumnName("ADesc3");
            entity.Property(e => e.Adesc4)
                .HasMaxLength(5)
                .HasColumnName("ADesc4");
            entity.Property(e => e.Adesc5)
                .HasMaxLength(5)
                .HasColumnName("ADesc5");
            entity.Property(e => e.Adesc6)
                .HasMaxLength(5)
                .HasColumnName("ADesc6");
            entity.Property(e => e.Adesc7)
                .HasMaxLength(5)
                .HasColumnName("ADesc7");
            entity.Property(e => e.Adesc8)
                .HasMaxLength(5)
                .HasColumnName("ADesc8");
            entity.Property(e => e.Adesc9)
                .HasMaxLength(5)
                .HasColumnName("ADesc9");
            entity.Property(e => e.Akdnr).HasColumnName("AKDNR");
            entity.Property(e => e.Atext1)
                .HasMaxLength(30)
                .HasColumnName("AText1");
            entity.Property(e => e.Atext10)
                .HasMaxLength(30)
                .HasColumnName("AText10");
            entity.Property(e => e.Atext11)
                .HasMaxLength(30)
                .HasColumnName("AText11");
            entity.Property(e => e.Atext12)
                .HasMaxLength(30)
                .HasColumnName("AText12");
            entity.Property(e => e.Atext13)
                .HasMaxLength(30)
                .HasColumnName("AText13");
            entity.Property(e => e.Atext14)
                .HasMaxLength(30)
                .HasColumnName("AText14");
            entity.Property(e => e.Atext15)
                .HasMaxLength(30)
                .HasColumnName("AText15");
            entity.Property(e => e.Atext16)
                .HasMaxLength(30)
                .HasColumnName("AText16");
            entity.Property(e => e.Atext17)
                .HasMaxLength(30)
                .HasColumnName("AText17");
            entity.Property(e => e.Atext18)
                .HasMaxLength(30)
                .HasColumnName("AText18");
            entity.Property(e => e.Atext19)
                .HasMaxLength(30)
                .HasColumnName("AText19");
            entity.Property(e => e.Atext2)
                .HasMaxLength(30)
                .HasColumnName("AText2");
            entity.Property(e => e.Atext20)
                .HasMaxLength(30)
                .HasColumnName("AText20");
            entity.Property(e => e.Atext21)
                .HasMaxLength(30)
                .HasColumnName("AText21");
            entity.Property(e => e.Atext22)
                .HasMaxLength(30)
                .HasColumnName("AText22");
            entity.Property(e => e.Atext23)
                .HasMaxLength(30)
                .HasColumnName("AText23");
            entity.Property(e => e.Atext24)
                .HasMaxLength(30)
                .HasColumnName("AText24");
            entity.Property(e => e.Atext3)
                .HasMaxLength(30)
                .HasColumnName("AText3");
            entity.Property(e => e.Atext4)
                .HasMaxLength(30)
                .HasColumnName("AText4");
            entity.Property(e => e.Atext5)
                .HasMaxLength(30)
                .HasColumnName("AText5");
            entity.Property(e => e.Atext6)
                .HasMaxLength(30)
                .HasColumnName("AText6");
            entity.Property(e => e.Atext7)
                .HasMaxLength(30)
                .HasColumnName("AText7");
            entity.Property(e => e.Atext8)
                .HasMaxLength(30)
                .HasColumnName("AText8");
            entity.Property(e => e.Atext9)
                .HasMaxLength(30)
                .HasColumnName("AText9");
            entity.Property(e => e.Bbis1).HasColumnName("BBIS1");
            entity.Property(e => e.Bbis10).HasColumnName("BBIS10");
            entity.Property(e => e.Bbis11).HasColumnName("BBIS11");
            entity.Property(e => e.Bbis12).HasColumnName("BBIS12");
            entity.Property(e => e.Bbis13).HasColumnName("BBIS13");
            entity.Property(e => e.Bbis14).HasColumnName("BBIS14");
            entity.Property(e => e.Bbis15).HasColumnName("BBIS15");
            entity.Property(e => e.Bbis16).HasColumnName("BBIS16");
            entity.Property(e => e.Bbis17).HasColumnName("BBIS17");
            entity.Property(e => e.Bbis18).HasColumnName("BBIS18");
            entity.Property(e => e.Bbis19).HasColumnName("BBIS19");
            entity.Property(e => e.Bbis2).HasColumnName("BBIS2");
            entity.Property(e => e.Bbis20).HasColumnName("BBIS20");
            entity.Property(e => e.Bbis21).HasColumnName("BBIS21");
            entity.Property(e => e.Bbis22).HasColumnName("BBIS22");
            entity.Property(e => e.Bbis23).HasColumnName("BBIS23");
            entity.Property(e => e.Bbis24).HasColumnName("BBIS24");
            entity.Property(e => e.Bbis3).HasColumnName("BBIS3");
            entity.Property(e => e.Bbis4).HasColumnName("BBIS4");
            entity.Property(e => e.Bbis5).HasColumnName("BBIS5");
            entity.Property(e => e.Bbis6).HasColumnName("BBIS6");
            entity.Property(e => e.Bbis7).HasColumnName("BBIS7");
            entity.Property(e => e.Bbis8).HasColumnName("BBIS8");
            entity.Property(e => e.Bbis9).HasColumnName("BBIS9");
            entity.Property(e => e.Bvon1).HasColumnName("BVON1");
            entity.Property(e => e.Bvon10).HasColumnName("BVON10");
            entity.Property(e => e.Bvon11).HasColumnName("BVON11");
            entity.Property(e => e.Bvon12).HasColumnName("BVON12");
            entity.Property(e => e.Bvon13).HasColumnName("BVON13");
            entity.Property(e => e.Bvon14).HasColumnName("BVON14");
            entity.Property(e => e.Bvon15).HasColumnName("BVON15");
            entity.Property(e => e.Bvon16).HasColumnName("BVON16");
            entity.Property(e => e.Bvon17).HasColumnName("BVON17");
            entity.Property(e => e.Bvon18).HasColumnName("BVON18");
            entity.Property(e => e.Bvon19).HasColumnName("BVON19");
            entity.Property(e => e.Bvon2).HasColumnName("BVON2");
            entity.Property(e => e.Bvon20).HasColumnName("BVON20");
            entity.Property(e => e.Bvon21).HasColumnName("BVON21");
            entity.Property(e => e.Bvon22).HasColumnName("BVON22");
            entity.Property(e => e.Bvon23).HasColumnName("BVON23");
            entity.Property(e => e.Bvon24).HasColumnName("BVON24");
            entity.Property(e => e.Bvon3).HasColumnName("BVON3");
            entity.Property(e => e.Bvon4).HasColumnName("BVON4");
            entity.Property(e => e.Bvon5).HasColumnName("BVON5");
            entity.Property(e => e.Bvon6).HasColumnName("BVON6");
            entity.Property(e => e.Bvon7).HasColumnName("BVON7");
            entity.Property(e => e.Bvon8).HasColumnName("BVON8");
            entity.Property(e => e.Bvon9).HasColumnName("BVON9");
            entity.Property(e => e.Nk1).HasColumnName("NK1");
            entity.Property(e => e.Nk10).HasColumnName("NK10");
            entity.Property(e => e.Nk11).HasColumnName("NK11");
            entity.Property(e => e.Nk12).HasColumnName("NK12");
            entity.Property(e => e.Nk13).HasColumnName("NK13");
            entity.Property(e => e.Nk14).HasColumnName("NK14");
            entity.Property(e => e.Nk15).HasColumnName("NK15");
            entity.Property(e => e.Nk16).HasColumnName("NK16");
            entity.Property(e => e.Nk17).HasColumnName("NK17");
            entity.Property(e => e.Nk18).HasColumnName("NK18");
            entity.Property(e => e.Nk19).HasColumnName("NK19");
            entity.Property(e => e.Nk2).HasColumnName("NK2");
            entity.Property(e => e.Nk20).HasColumnName("NK20");
            entity.Property(e => e.Nk21).HasColumnName("NK21");
            entity.Property(e => e.Nk22).HasColumnName("NK22");
            entity.Property(e => e.Nk23).HasColumnName("NK23");
            entity.Property(e => e.Nk24).HasColumnName("NK24");
            entity.Property(e => e.Nk3).HasColumnName("NK3");
            entity.Property(e => e.Nk4).HasColumnName("NK4");
            entity.Property(e => e.Nk5).HasColumnName("NK5");
            entity.Property(e => e.Nk6).HasColumnName("NK6");
            entity.Property(e => e.Nk7).HasColumnName("NK7");
            entity.Property(e => e.Nk8).HasColumnName("NK8");
            entity.Property(e => e.Nk9).HasColumnName("NK9");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<KasAb>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KasAbs$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kabbetrag)
                .HasColumnType("money")
                .HasColumnName("KABBetrag");
            entity.Property(e => e.Kabdatum)
                .HasColumnType("datetime")
                .HasColumnName("KABDatum");
            entity.Property(e => e.Kabopt1)
                .HasColumnType("money")
                .HasColumnName("KABOpt1");
            entity.Property(e => e.Kabopt2).HasColumnName("KABOpt2");
            entity.Property(e => e.KabsysDatum)
                .HasColumnType("datetime")
                .HasColumnName("KABSysDatum");
            entity.Property(e => e.Kabuser)
                .HasMaxLength(50)
                .HasColumnName("KABUser");
            entity.Property(e => e.KasVortr).HasColumnType("money");
            entity.Property(e => e.KasseName).HasMaxLength(30);
            entity.Property(e => e.Korrektur).HasColumnType("money");
        });

        modelBuilder.Entity<KasHpt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KasHpt$ID");

            entity.ToTable("KasHpt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Gegeben)
                .HasColumnType("money")
                .HasColumnName("gegeben");
            entity.Property(e => e.Gsnr).HasColumnName("GSNr");
            entity.Property(e => e.Gsrest)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("GSRest");
            entity.Property(e => e.HartNr).HasColumnName("HArtNr");
            entity.Property(e => e.KabschlNr).HasColumnName("KAbschlNr");
            entity.Property(e => e.KaltMenge).HasColumnName("KAltMenge");
            entity.Property(e => e.Kanzahl).HasColumnName("KAnzahl");
            entity.Property(e => e.KartGrp).HasColumnName("KArtGrp");
            entity.Property(e => e.KartNr).HasColumnName("KArtNr");
            entity.Property(e => e.KartNr2).HasColumnName("KArtNr2");
            entity.Property(e => e.KasseName).HasMaxLength(30);
            entity.Property(e => e.Kbez)
                .HasMaxLength(30)
                .HasColumnName("KBez");
            entity.Property(e => e.Kdatum)
                .HasColumnType("datetime")
                .HasColumnName("KDatum");
            entity.Property(e => e.Kep)
                .HasColumnType("money")
                .HasColumnName("KEP");
            entity.Property(e => e.Kfaktor).HasColumnName("KFaktor");
            entity.Property(e => e.Kgp)
                .HasColumnType("money")
                .HasColumnName("KGP");
            entity.Property(e => e.Kgrund)
                .HasMaxLength(80)
                .HasColumnName("KGrund");
            entity.Property(e => e.Kkn)
                .HasMaxLength(2)
                .HasColumnName("KKn");
            entity.Property(e => e.Kkonto)
                .HasMaxLength(10)
                .HasColumnName("KKonto");
            entity.Property(e => e.KmonAbschl)
                .HasDefaultValue(false)
                .HasColumnName("KMonAbschl");
            entity.Property(e => e.Kmwst)
                .HasColumnType("money")
                .HasColumnName("KMwst");
            entity.Property(e => e.KmwstSchl).HasColumnName("KMwstSchl");
            entity.Property(e => e.Kopt1)
                .HasMaxLength(10)
                .HasColumnName("KOpt1");
            entity.Property(e => e.Kopt2)
                .HasColumnType("money")
                .HasColumnName("KOpt2");
            entity.Property(e => e.Kopt3).HasColumnName("KOpt3");
            entity.Property(e => e.Kopt4)
                .HasMaxLength(1)
                .HasColumnName("KOpt4");
            entity.Property(e => e.KposNr).HasColumnName("KPosNr");
            entity.Property(e => e.Kstatus).HasColumnName("KStatus");
            entity.Property(e => e.Kuhrzeit).HasColumnType("datetime");
            entity.Property(e => e.KuserNr).HasColumnName("KUserNr");
            entity.Property(e => e.Kvnr).HasColumnName("KVNr");
            entity.Property(e => e.KvnrAlt).HasColumnName("KVNrAlt");
            entity.Property(e => e.Kvnv).HasColumnName("KVNV");
            entity.Property(e => e.KvpNr)
                .HasMaxLength(12)
                .HasColumnName("KVpNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Zurueck)
                .HasColumnType("money")
                .HasColumnName("zurueck");
        });

        modelBuilder.Entity<KasHptAb>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KasHptAbs$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Gegeben)
                .HasColumnType("money")
                .HasColumnName("gegeben");
            entity.Property(e => e.Gsnr).HasColumnName("GSNr");
            entity.Property(e => e.Gsrest)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("GSRest");
            entity.Property(e => e.HartNr).HasColumnName("HArtNr");
            entity.Property(e => e.KabschlNr).HasColumnName("KAbschlNr");
            entity.Property(e => e.KaltMenge).HasColumnName("KAltMenge");
            entity.Property(e => e.Kanzahl).HasColumnName("KAnzahl");
            entity.Property(e => e.KartGrp).HasColumnName("KArtGrp");
            entity.Property(e => e.KartNr).HasColumnName("KArtNr");
            entity.Property(e => e.KartNr2).HasColumnName("KArtNr2");
            entity.Property(e => e.Kbez)
                .HasMaxLength(30)
                .HasColumnName("KBez");
            entity.Property(e => e.Kdatum)
                .HasColumnType("datetime")
                .HasColumnName("KDatum");
            entity.Property(e => e.Kep)
                .HasColumnType("money")
                .HasColumnName("KEP");
            entity.Property(e => e.Kfaktor).HasColumnName("KFaktor");
            entity.Property(e => e.Kgp)
                .HasColumnType("money")
                .HasColumnName("KGP");
            entity.Property(e => e.Kgrund)
                .HasMaxLength(80)
                .HasColumnName("KGrund");
            entity.Property(e => e.Kkn)
                .HasMaxLength(2)
                .HasColumnName("KKn");
            entity.Property(e => e.Kkonto)
                .HasMaxLength(10)
                .HasColumnName("KKonto");
            entity.Property(e => e.KmonAbschl)
                .HasDefaultValue(false)
                .HasColumnName("KMonAbschl");
            entity.Property(e => e.Kmwst)
                .HasColumnType("money")
                .HasColumnName("KMwst");
            entity.Property(e => e.KmwstSchl).HasColumnName("KMwstSchl");
            entity.Property(e => e.Kopt1)
                .HasMaxLength(10)
                .HasColumnName("KOpt1");
            entity.Property(e => e.Kopt2)
                .HasColumnType("money")
                .HasColumnName("KOpt2");
            entity.Property(e => e.Kopt3).HasColumnName("KOpt3");
            entity.Property(e => e.Kopt4)
                .HasMaxLength(1)
                .HasColumnName("KOpt4");
            entity.Property(e => e.KposNr).HasColumnName("KPosNr");
            entity.Property(e => e.Kstatus).HasColumnName("KStatus");
            entity.Property(e => e.Kuhrzeit).HasColumnType("datetime");
            entity.Property(e => e.KuserNr).HasColumnName("KUserNr");
            entity.Property(e => e.Kvnr).HasColumnName("KVNr");
            entity.Property(e => e.KvnrAlt).HasColumnName("KVNrAlt");
            entity.Property(e => e.Kvnv).HasColumnName("KVNV");
            entity.Property(e => e.KvpNr)
                .HasMaxLength(12)
                .HasColumnName("KVpNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Zurueck)
                .HasColumnType("money")
                .HasColumnName("zurueck");
        });

        modelBuilder.Entity<KasHptTse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KasHpt_TSE$ID");

            entity.ToTable("KasHpt_TSE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BonBegin).IsUnicode(false);
            entity.Property(e => e.BonEnd).IsUnicode(false);
            entity.Property(e => e.Gegeben)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("gegeben");
            entity.Property(e => e.Gsnr)
                .HasDefaultValueSql("('0')")
                .HasColumnName("GSNr");
            entity.Property(e => e.Gsrest)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("GSRest");
            entity.Property(e => e.Kbediener)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("KBediener");
            entity.Property(e => e.Kbrutto1)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KBrutto1");
            entity.Property(e => e.Kbrutto2)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KBrutto2");
            entity.Property(e => e.Kbrutto3)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KBrutto3");
            entity.Property(e => e.KmwSt1)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KMwSt1");
            entity.Property(e => e.KmwSt2)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KMwSt2");
            entity.Property(e => e.KmwSt3)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KMwSt3");
            entity.Property(e => e.Knetto1)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KNetto1");
            entity.Property(e => e.Knetto2)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KNetto2");
            entity.Property(e => e.Knetto3)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("KNetto3");
            entity.Property(e => e.KstNr1)
                .HasDefaultValueSql("('0')")
                .HasColumnName("KStNr1");
            entity.Property(e => e.KstNr2)
                .HasDefaultValueSql("('0')")
                .HasColumnName("KStNr2");
            entity.Property(e => e.KstNr3)
                .HasDefaultValueSql("('0')")
                .HasColumnName("KStNr3");
            entity.Property(e => e.Kvnr)
                .HasDefaultValueSql("('00')")
                .HasColumnName("KVnr");
            entity.Property(e => e.ProcessData).IsUnicode(false);
            entity.Property(e => e.ProcessTyp).IsUnicode(false);
            entity.Property(e => e.PublicKey).IsUnicode(false);
            entity.Property(e => e.SerNr).IsUnicode(false);
            entity.Property(e => e.Signature).IsUnicode(false);
            entity.Property(e => e.SignatureAlgorithm).IsUnicode(false);
            entity.Property(e => e.SignatureCounter).IsUnicode(false);
            entity.Property(e => e.TimeFormat).IsUnicode(false);
            entity.Property(e => e.Zurück)
                .HasDefaultValueSql("('0')")
                .HasColumnType("money")
                .HasColumnName("zurück");
        });

        modelBuilder.Entity<KassHandArtikel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KassHandArtikel$ID");

            entity.ToTable("KassHandArtikel");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LfdNr)
                .HasDefaultValue(0L)
                .HasColumnName("lfdNr");
            entity.Property(e => e.Name).HasMaxLength(30);
            entity.Property(e => e.Nummer).HasDefaultValue(0L);
        });

        modelBuilder.Entity<Kbetreuer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KBetreuer$ID");

            entity.ToTable("KBetreuer");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Babkuerzung)
                .HasMaxLength(10)
                .HasColumnName("BAbkuerzung");
            entity.Property(e => e.Bname)
                .HasMaxLength(50)
                .HasColumnName("BName");
        });

        modelBuilder.Entity<KdbranchenKennung>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KDBranchenKennung$ID");

            entity.ToTable("KDBranchenKennung");

            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasColumnName("ID");
            entity.Property(e => e.KdbranchenKng)
                .HasMaxLength(30)
                .HasColumnName("KDBranchenKng");
        });

        modelBuilder.Entity<Kdlprei>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KDLPreis$ID");

            entity.ToTable("KDLPreis");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Preis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
        });

        modelBuilder.Entity<KdstatEuroRe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KDStatEuroRE$ID");

            entity.ToTable("KDStatEuroRE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdstejahr)
                .HasDefaultValue((short)0)
                .HasColumnName("KDSTEJahr");
            entity.Property(e => e.KdstekdNr).HasColumnName("KDSTEKdNr");
            entity.Property(e => e.Kdstemonat01)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat01");
            entity.Property(e => e.Kdstemonat02)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat02");
            entity.Property(e => e.Kdstemonat03)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat03");
            entity.Property(e => e.Kdstemonat04)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat04");
            entity.Property(e => e.Kdstemonat05)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat05");
            entity.Property(e => e.Kdstemonat06)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat06");
            entity.Property(e => e.Kdstemonat07)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat07");
            entity.Property(e => e.Kdstemonat08)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat08");
            entity.Property(e => e.Kdstemonat09)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat09");
            entity.Property(e => e.Kdstemonat10)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat10");
            entity.Property(e => e.Kdstemonat11)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat11");
            entity.Property(e => e.Kdstemonat12)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat12");
        });

        modelBuilder.Entity<KdstatEuroWe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KDStatEuroWE$ID");

            entity.ToTable("KDStatEuroWE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdstejahr)
                .HasDefaultValue((short)0)
                .HasColumnName("KDSTEJahr");
            entity.Property(e => e.KdstekdNr).HasColumnName("KDSTEKdNr");
            entity.Property(e => e.Kdstemonat01)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat01");
            entity.Property(e => e.Kdstemonat02)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat02");
            entity.Property(e => e.Kdstemonat03)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat03");
            entity.Property(e => e.Kdstemonat04)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat04");
            entity.Property(e => e.Kdstemonat05)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat05");
            entity.Property(e => e.Kdstemonat06)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat06");
            entity.Property(e => e.Kdstemonat07)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat07");
            entity.Property(e => e.Kdstemonat08)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat08");
            entity.Property(e => e.Kdstemonat09)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat09");
            entity.Property(e => e.Kdstemonat10)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat10");
            entity.Property(e => e.Kdstemonat11)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat11");
            entity.Property(e => e.Kdstemonat12)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTEMonat12");
        });

        modelBuilder.Entity<KontrZu>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KontrZus$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Ktnr).HasColumnName("KTNr");
            entity.Property(e => e.Ktwrbasis).HasColumnName("KTWRBasis");
            entity.Property(e => e.Ktwrmax).HasColumnName("KTWRMax");
            entity.Property(e => e.Ktwrmin).HasColumnName("KTWRMin");
            entity.Property(e => e.Ktwrname)
                .HasMaxLength(30)
                .HasColumnName("KTWRName");
            entity.Property(e => e.Ktwrps).HasColumnName("KTWRPS");
            entity.Property(e => e.Ktwrstossgr).HasColumnName("KTWRStossgr");
            entity.Property(e => e.Ktwrxx).HasColumnName("KTWRXX");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
        });

        modelBuilder.Entity<Kontrakt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Kontrakt$ID");

            entity.ToTable("Kontrakt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.EigLager).HasDefaultValue(false);
            entity.Property(e => e.Email)
                .HasMaxLength(255)
                .HasDefaultValueSql("((0))")
                .HasColumnName("EMail");
            entity.Property(e => e.GrPreis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.GrWert)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.KtabDatum)
                .HasColumnType("datetime")
                .HasColumnName("KTAbDatum");
            entity.Property(e => e.KtabgerMenge).HasColumnName("KTabgerMenge");
            entity.Property(e => e.Ktabnahme)
                .HasDefaultValue(false)
                .HasColumnName("KTAbnahme");
            entity.Property(e => e.KtartBez1)
                .HasMaxLength(30)
                .HasColumnName("KTArtBez1");
            entity.Property(e => e.KtartBez2)
                .HasMaxLength(30)
                .HasColumnName("KTArtBez2");
            entity.Property(e => e.KtartikelNr).HasColumnName("KTArtikelNr");
            entity.Property(e => e.Ktasld).HasColumnName("KTAsld");
            entity.Property(e => e.Ktbasispreis)
                .HasColumnType("money")
                .HasColumnName("KTBasispreis");
            entity.Property(e => e.KtbeladestelleNr).HasColumnName("KTBeladestelleNr");
            entity.Property(e => e.Ktbetreuer)
                .HasMaxLength(20)
                .HasColumnName("KTBetreuer");
            entity.Property(e => e.Ktbezeichnung)
                .HasMaxLength(30)
                .HasColumnName("KTBezeichnung");
            entity.Property(e => e.KtbisDatum)
                .HasColumnType("datetime")
                .HasColumnName("KTBisDatum");
            entity.Property(e => e.KtbstName1)
                .HasMaxLength(30)
                .HasColumnName("KTBStName1");
            entity.Property(e => e.KtbstName2)
                .HasMaxLength(30)
                .HasColumnName("KTBStName2");
            entity.Property(e => e.KtbstOrt)
                .HasMaxLength(30)
                .HasColumnName("KTBStOrt");
            entity.Property(e => e.KtbstPlz)
                .HasMaxLength(10)
                .HasColumnName("KTBStPLZ");
            entity.Property(e => e.KtbstSbg)
                .HasMaxLength(10)
                .HasColumnName("KTBStSBG");
            entity.Property(e => e.KtbstStrasse)
                .HasMaxLength(30)
                .HasColumnName("KTBStStrasse");
            entity.Property(e => e.KtbstTelefax)
                .HasMaxLength(20)
                .HasColumnName("KTBStTelefax");
            entity.Property(e => e.KtbstTelefon)
                .HasMaxLength(20)
                .HasColumnName("KTBStTelefon");
            entity.Property(e => e.Ktdatum)
                .HasColumnType("datetime")
                .HasColumnName("KTDatum");
            entity.Property(e => e.Ktek)
                .HasDefaultValue(false)
                .HasColumnName("KTEK");
            entity.Property(e => e.Ktende)
                .HasColumnType("datetime")
                .HasColumnName("KTEnde");
            entity.Property(e => e.Kterledigt)
                .HasDefaultValue(false)
                .HasColumnName("KTErledigt");
            entity.Property(e => e.Kternte).HasColumnName("KTErnte");
            entity.Property(e => e.Ktfracht)
                .HasColumnType("money")
                .HasColumnName("KTFracht");
            entity.Property(e => e.KtfreiEing)
                .HasDefaultValue(false)
                .HasColumnName("KTfreiEing");
            entity.Property(e => e.KtfreiText)
                .HasMaxLength(20)
                .HasColumnName("KTFreiText");
            entity.Property(e => e.KthändlerNr).HasColumnName("KTHändlerNr");
            entity.Property(e => e.Ktkennung).HasColumnName("KTKennung");
            entity.Property(e => e.Ktklassifizierung).HasColumnName("KTKlassifizierung");
            entity.Property(e => e.KtkursTabelle)
                .HasDefaultValue(false)
                .HasColumnName("KTKursTabelle");
            entity.Property(e => e.Ktkwahl)
                .HasDefaultValue(false)
                .HasColumnName("KTKWahl");
            entity.Property(e => e.KtliefAdr1)
                .HasMaxLength(30)
                .HasColumnName("KTLiefAdr1");
            entity.Property(e => e.KtliefAdr2)
                .HasMaxLength(30)
                .HasColumnName("KTLiefAdr2");
            entity.Property(e => e.KtliefAdr3)
                .HasMaxLength(30)
                .HasColumnName("KTLiefAdr3");
            entity.Property(e => e.KtliefAnrede)
                .HasMaxLength(30)
                .HasColumnName("KTLiefAnrede");
            entity.Property(e => e.KtliefLand)
                .HasMaxLength(3)
                .HasColumnName("KTLiefLand");
            entity.Property(e => e.KtliefNr).HasColumnName("KTLiefNr");
            entity.Property(e => e.KtliefOrt)
                .HasMaxLength(40)
                .HasColumnName("KTLiefOrt");
            entity.Property(e => e.KtliefPlz)
                .HasMaxLength(10)
                .HasColumnName("KTLiefPLZ");
            entity.Property(e => e.KtliefSbg)
                .HasMaxLength(10)
                .HasColumnName("KTLiefSBG");
            entity.Property(e => e.KtliefStraße)
                .HasMaxLength(30)
                .HasColumnName("KTLiefStraße");
            entity.Property(e => e.KtliefTelefax)
                .HasMaxLength(20)
                .HasColumnName("KTLiefTelefax");
            entity.Property(e => e.KtliefTelefon)
                .HasMaxLength(20)
                .HasColumnName("KTLiefTelefon");
            entity.Property(e => e.KtloeschKng)
                .HasDefaultValue(false)
                .HasColumnName("KTLoeschKng");
            entity.Property(e => e.KtmaklerN1)
                .HasMaxLength(30)
                .HasColumnName("KTMaklerN1");
            entity.Property(e => e.KtmaklerN2)
                .HasMaxLength(30)
                .HasColumnName("KTMaklerN2");
            entity.Property(e => e.KtmaklerNr).HasColumnName("KTMaklerNr");
            entity.Property(e => e.KtmaklerOrt)
                .HasMaxLength(30)
                .HasColumnName("KTMaklerOrt");
            entity.Property(e => e.KtmaklerPlz)
                .HasMaxLength(10)
                .HasColumnName("KTMaklerPLZ");
            entity.Property(e => e.KtmaklerTel)
                .HasMaxLength(20)
                .HasColumnName("KTMaklerTel");
            entity.Property(e => e.KtmatchCode)
                .HasMaxLength(10)
                .HasColumnName("KTMatchCode");
            entity.Property(e => e.Ktmobil)
                .HasMaxLength(255)
                .HasColumnName("KTMobil");
            entity.Property(e => e.KtmonRate)
                .HasDefaultValue(false)
                .HasColumnName("KTmonRate");
            entity.Property(e => e.Ktnr).HasColumnName("KTNr");
            entity.Property(e => e.KtnrHändler)
                .HasMaxLength(20)
                .HasColumnName("KTNrHändler");
            entity.Property(e => e.Ktparität)
                .HasMaxLength(10)
                .HasColumnName("KTParität");
            entity.Property(e => e.KtpreislistNr).HasColumnName("KTPreislistNr");
            entity.Property(e => e.KtprovHöhe).HasColumnName("KTProvHöhe");
            entity.Property(e => e.Ktreportaufschlag)
                .HasColumnType("money")
                .HasColumnName("KTReportaufschlag");
            entity.Property(e => e.KtrestMenge).HasColumnName("KTRestMenge");
            entity.Property(e => e.Ktstart)
                .HasColumnType("datetime")
                .HasColumnName("KTStart");
            entity.Property(e => e.KtstartMenge).HasColumnName("KTStartMenge");
            entity.Property(e => e.KtustIdnr)
                .HasMaxLength(20)
                .HasColumnName("KTUstIDNr");
            entity.Property(e => e.Ktvalutatage)
                .HasMaxLength(60)
                .HasColumnName("KTValutatage");
            entity.Property(e => e.Ktvbank)
                .HasDefaultValue(false)
                .HasColumnName("KTVBank");
            entity.Property(e => e.Ktvcode)
                .HasMaxLength(255)
                .HasColumnName("KTVCode");
            entity.Property(e => e.Ktvdatum)
                .HasColumnType("datetime")
                .HasColumnName("KTVDatum");
            entity.Property(e => e.KtvertrNr)
                .HasDefaultValue(0L)
                .HasColumnName("KTVertrNr");
            entity.Property(e => e.Ktvorauszahlg).HasColumnName("KTVorauszahlg");
            entity.Property(e => e.Ktwhrg)
                .HasMaxLength(4)
                .HasColumnName("KTWhrg");
            entity.Property(e => e.KtwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("KTWhrgKurs");
            entity.Property(e => e.KtzuText1)
                .HasMaxLength(30)
                .HasColumnName("KTZuText1");
            entity.Property(e => e.KtzuText2)
                .HasMaxLength(30)
                .HasColumnName("KTZuText2");
            entity.Property(e => e.KtzuText3)
                .HasMaxLength(30)
                .HasColumnName("KTZuText3");
            entity.Property(e => e.KtzuText4)
                .HasMaxLength(30)
                .HasColumnName("KTZuText4");
            entity.Property(e => e.KtzuText5)
                .HasMaxLength(30)
                .HasColumnName("KTZuText5");
            entity.Property(e => e.KtzuText6)
                .HasMaxLength(30)
                .HasColumnName("KTZuText6");
            entity.Property(e => e.Lagergeld).HasDefaultValue(false);
            entity.Property(e => e.Lbpos)
                .HasDefaultValue(0)
                .HasColumnName("LBPos");
            entity.Property(e => e.MatifPreis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.PktrKng)
                .HasDefaultValue(false)
                .HasColumnName("PKtrKng");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
            entity.Property(e => e.PrPreis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Kpartner>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KPartner$ID");

            entity.ToTable("KPartner");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Email)
                .HasMaxLength(255)
                .HasColumnName("EMail");
            entity.Property(e => e.Geburtstag).HasColumnType("datetime");
            entity.Property(e => e.Internet).HasMaxLength(255);
            entity.Property(e => e.Kdnr).HasColumnName("KDNr");
            entity.Property(e => e.Mobil).HasMaxLength(20);
            entity.Property(e => e.Name1).HasMaxLength(30);
            entity.Property(e => e.Name2).HasMaxLength(30);
            entity.Property(e => e.Name3).HasMaxLength(30);
            entity.Property(e => e.Ort).HasMaxLength(30);
            entity.Property(e => e.Plz)
                .HasMaxLength(10)
                .HasColumnName("PLZ");
            entity.Property(e => e.Plzpostfach)
                .HasMaxLength(10)
                .HasColumnName("PLZPostfach");
            entity.Property(e => e.Postfach).HasMaxLength(30);
            entity.Property(e => e.Strasse).HasMaxLength(30);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
            entity.Property(e => e.Telefax).HasMaxLength(20);
            entity.Property(e => e.Telefon1).HasMaxLength(20);
            entity.Property(e => e.Telefon2).HasMaxLength(20);
        });

        modelBuilder.Entity<Kprotokoll>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KProtokoll$ID");

            entity.ToTable("KProtokoll");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kpgpos).HasColumnName("KPGPos");
            entity.Property(e => e.Kpldatum)
                .HasColumnType("datetime")
                .HasColumnName("KPLDatum");
            entity.Property(e => e.Kpldiffmenge).HasColumnName("KPLDiffmenge");
            entity.Property(e => e.Kplentnahmetext)
                .HasMaxLength(30)
                .HasColumnName("KPLEntnahmetext");
            entity.Property(e => e.Kplkennung).HasColumnName("KPLKennung");
            entity.Property(e => e.Kplrenr).HasColumnName("KPLRENr");
            entity.Property(e => e.Kplrestmenge).HasColumnName("KPLRestmenge");
            entity.Property(e => e.KpposNr).HasColumnName("KPPosNr");
            entity.Property(e => e.Kppreis)
                .HasColumnType("money")
                .HasColumnName("KPPreis");
            entity.Property(e => e.KpscheinNr).HasColumnName("KPScheinNr");
            entity.Property(e => e.KpstMenge)
                .HasColumnType("money")
                .HasColumnName("KPStMenge");
            entity.Property(e => e.Kpvznr).HasColumnName("KPVZNr");
            entity.Property(e => e.Ktnr).HasColumnName("KTNr");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
        });

        modelBuilder.Entity<Ktexte>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KTexte$ID");

            entity.ToTable("KTexte");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdnr).HasColumnName("KDNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Txtart)
                .HasMaxLength(10)
                .HasColumnName("TXTArt");
            entity.Property(e => e.Txtpfad)
                .HasMaxLength(255)
                .HasColumnName("TXTPfad");
        });

        modelBuilder.Entity<KtrEk>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KtrEK$ID");

            entity.ToTable("KtrEK");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.KtrEkdatum)
                .HasColumnType("datetime")
                .HasColumnName("KtrEKDatum");
            entity.Property(e => e.KtrEkmenge).HasColumnName("KtrEKMenge");
            entity.Property(e => e.KtrEknr).HasColumnName("KtrEKNr");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
        });

        modelBuilder.Entity<KtrMail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KtrMail$ID");

            entity.ToTable("KtrMail");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Etext1)
                .HasMaxLength(50)
                .HasColumnName("EText1");
            entity.Property(e => e.Etext10)
                .HasMaxLength(50)
                .HasColumnName("EText10");
            entity.Property(e => e.Etext2)
                .HasMaxLength(50)
                .HasColumnName("EText2");
            entity.Property(e => e.Etext3)
                .HasMaxLength(50)
                .HasColumnName("EText3");
            entity.Property(e => e.Etext4)
                .HasMaxLength(50)
                .HasColumnName("EText4");
            entity.Property(e => e.Etext5)
                .HasMaxLength(50)
                .HasColumnName("EText5");
            entity.Property(e => e.Etext6)
                .HasMaxLength(50)
                .HasColumnName("EText6");
            entity.Property(e => e.Etext7)
                .HasMaxLength(50)
                .HasColumnName("EText7");
            entity.Property(e => e.Etext8)
                .HasMaxLength(50)
                .HasColumnName("EText8");
            entity.Property(e => e.Etext9)
                .HasMaxLength(50)
                .HasColumnName("EText9");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vtext1)
                .HasMaxLength(50)
                .HasColumnName("VText1");
            entity.Property(e => e.Vtext10)
                .HasMaxLength(50)
                .HasColumnName("VText10");
            entity.Property(e => e.Vtext2)
                .HasMaxLength(50)
                .HasColumnName("VText2");
            entity.Property(e => e.Vtext3)
                .HasMaxLength(50)
                .HasColumnName("VText3");
            entity.Property(e => e.Vtext4)
                .HasMaxLength(50)
                .HasColumnName("VText4");
            entity.Property(e => e.Vtext5)
                .HasMaxLength(50)
                .HasColumnName("VText5");
            entity.Property(e => e.Vtext6)
                .HasMaxLength(50)
                .HasColumnName("VText6");
            entity.Property(e => e.Vtext7)
                .HasMaxLength(50)
                .HasColumnName("VText7");
            entity.Property(e => e.Vtext8)
                .HasMaxLength(50)
                .HasColumnName("VText8");
            entity.Property(e => e.Vtext9)
                .HasMaxLength(50)
                .HasColumnName("VText9");
        });

        modelBuilder.Entity<KtrVk>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KtrVK$ID");

            entity.ToTable("KtrVK");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.KtrVkdatum)
                .HasColumnType("datetime")
                .HasColumnName("KtrVKDatum");
            entity.Property(e => e.KtrVkmenge).HasColumnName("KtrVKMenge");
            entity.Property(e => e.KtrVknr).HasColumnName("KtrVKNr");
        });

        modelBuilder.Entity<KundInfo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KundInfo$ID");

            entity.ToTable("KundInfo");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bearbeiter).HasMaxLength(30);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Erledigt)
                .HasDefaultValue(false)
                .HasColumnName("erledigt");
            entity.Property(e => e.FuerUser)
                .HasMaxLength(255)
                .HasColumnName("fuerUser");
            entity.Property(e => e.Kalender).HasDefaultValue(false);
            entity.Property(e => e.Name).HasMaxLength(30);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.UserSelect).HasDefaultValue(false);
            entity.Property(e => e.Wvdatum)
                .HasColumnType("datetime")
                .HasColumnName("WVDatum");
        });

        modelBuilder.Entity<KundRabatte>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KundRabatte$ID");

            entity.ToTable("KundRabatte");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Basis).HasDefaultValue(false);
            entity.Property(e => e.Basisbetrag)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Kleiner).HasDefaultValue(false);
            entity.Property(e => e.Rabatt).HasDefaultValue(false);
            entity.Property(e => e.Rabattbetrag)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });

        modelBuilder.Entity<KundStatJahrRe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KundStatJahrRE$ID");

            entity.ToTable("KundStatJahrRE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdstjahr)
                .HasDefaultValue((short)0)
                .HasColumnName("KDSTJahr");
            entity.Property(e => e.KdstkdNr).HasColumnName("KDSTKdNr");
            entity.Property(e => e.Kdstmonat01)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat01");
            entity.Property(e => e.Kdstmonat02)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat02");
            entity.Property(e => e.Kdstmonat03)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat03");
            entity.Property(e => e.Kdstmonat04)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat04");
            entity.Property(e => e.Kdstmonat05)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat05");
            entity.Property(e => e.Kdstmonat06)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat06");
            entity.Property(e => e.Kdstmonat07)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat07");
            entity.Property(e => e.Kdstmonat08)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat08");
            entity.Property(e => e.Kdstmonat09)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat09");
            entity.Property(e => e.Kdstmonat10)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat10");
            entity.Property(e => e.Kdstmonat11)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat11");
            entity.Property(e => e.Kdstmonat12)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat12");
        });

        modelBuilder.Entity<KundStatJahrWe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("KundStatJahrWE$ID");

            entity.ToTable("KundStatJahrWE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kdstjahr)
                .HasDefaultValue((short)0)
                .HasColumnName("KDSTJahr");
            entity.Property(e => e.KdstkdNr).HasColumnName("KDSTKdNr");
            entity.Property(e => e.Kdstmonat01)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat01");
            entity.Property(e => e.Kdstmonat02)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat02");
            entity.Property(e => e.Kdstmonat03)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat03");
            entity.Property(e => e.Kdstmonat04)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat04");
            entity.Property(e => e.Kdstmonat05)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat05");
            entity.Property(e => e.Kdstmonat06)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat06");
            entity.Property(e => e.Kdstmonat07)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat07");
            entity.Property(e => e.Kdstmonat08)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat08");
            entity.Property(e => e.Kdstmonat09)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat09");
            entity.Property(e => e.Kdstmonat10)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat10");
            entity.Property(e => e.Kdstmonat11)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat11");
            entity.Property(e => e.Kdstmonat12)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("KDSTMonat12");
        });

        modelBuilder.Entity<Kunden>(entity =>
        {
            entity.HasKey(e => new { e.Kdnummer, e.Id }).HasName("Kunden$KuNr");

            entity.ToTable("Kunden");

            entity.Property(e => e.Kdnummer).HasColumnName("KDNummer");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.AlsdruckForm)
                .HasMaxLength(255)
                .HasColumnName("ALSDruckForm");
            entity.Property(e => e.Anzahlungsbetrag)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.ArdruckForm)
                .HasMaxLength(255)
                .HasColumnName("ARDruckForm");
            entity.Property(e => e.DruckForm1).HasMaxLength(255);
            entity.Property(e => e.DruckForm2).HasMaxLength(255);
            entity.Property(e => e.DruckRform)
                .HasMaxLength(255)
                .HasColumnName("DruckRForm");
            entity.Property(e => e.Druckanzahl).HasDefaultValue(0);
            entity.Property(e => e.Edifact).HasDefaultValue(false);
            entity.Property(e => e.EdifactDatum).HasColumnType("datetime");
            entity.Property(e => e.EigLager).HasDefaultValue(false);
            entity.Property(e => e.EmailVersand)
                .HasDefaultValue(false)
                .HasColumnName("EMailVersand");
            entity.Property(e => e.FactSend).HasDefaultValue(false);
            entity.Property(e => e.Gedifact).HasDefaultValue(false);
            entity.Property(e => e.Hebebuehne).HasDefaultValue(false);
            entity.Property(e => e.Iln)
                .HasDefaultValue(false)
                .HasColumnName("ILN");
            entity.Property(e => e.Katnr).HasColumnName("KATNr");
            entity.Property(e => e.KdLfNr).HasMaxLength(20);
            entity.Property(e => e.KdabrStelleName)
                .HasMaxLength(30)
                .HasColumnName("KDAbrStelleName");
            entity.Property(e => e.KdabrStelleNr)
                .HasDefaultValue(0L)
                .HasColumnName("KDAbrStelleNr");
            entity.Property(e => e.KdabrStelleOrt)
                .HasMaxLength(50)
                .HasColumnName("KDAbrStelleOrt");
            entity.Property(e => e.KdabrStelleStatus)
                .HasMaxLength(1)
                .HasColumnName("KDAbrStelleStatus");
            entity.Property(e => e.Kdanalyse)
                .HasDefaultValue(false)
                .HasColumnName("KDAnalyse");
            entity.Property(e => e.Kdanrede)
                .HasMaxLength(30)
                .HasColumnName("KDAnrede");
            entity.Property(e => e.KdansprPartner)
                .HasMaxLength(30)
                .HasColumnName("KDAnsprPartner");
            entity.Property(e => e.Kdasld)
                .HasDefaultValue((short)0)
                .HasColumnName("KDASLD");
            entity.Property(e => e.Kdbank)
                .HasMaxLength(60)
                .HasColumnName("KDBank");
            entity.Property(e => e.KdbankEinzug)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBankEinzug");
            entity.Property(e => e.KdbankZuOrdnung).HasColumnName("KDBankZuOrdnung");
            entity.Property(e => e.KdbldKennung)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBldKennung");
            entity.Property(e => e.Kdblz)
                .HasMaxLength(10)
                .HasColumnName("KDBLZ");
            entity.Property(e => e.KdbranchenKng1)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng1");
            entity.Property(e => e.KdbranchenKng2)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng2");
            entity.Property(e => e.KdbranchenKng3)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng3");
            entity.Property(e => e.KdbranchenKng4)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng4");
            entity.Property(e => e.KdbranchenKng5)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng5");
            entity.Property(e => e.KdbranchenKng6)
                .HasDefaultValue((short)0)
                .HasColumnName("KDBranchenKng6");
            entity.Property(e => e.Kdbundesland)
                .HasMaxLength(21)
                .HasColumnName("KDBundesland");
            entity.Property(e => e.KddatLetztRechn)
                .HasColumnType("datetime")
                .HasColumnName("KDDatLetztRechn");
            entity.Property(e => e.Kddekade)
                .HasDefaultValue(false)
                .HasColumnName("KDDekade");
            entity.Property(e => e.Kdemail)
                .HasMaxLength(255)
                .HasColumnName("KDemail");
            entity.Property(e => e.Kdentsorger)
                .HasDefaultValue(false)
                .HasColumnName("KDEntsorger");
            entity.Property(e => e.KdfacFlowSend)
                .HasDefaultValue(false)
                .HasColumnName("KDFacFlowSend");
            entity.Property(e => e.KdfilialNr).HasColumnName("KDFilialNr");
            entity.Property(e => e.Kdgaw)
                .HasDefaultValue((short)0)
                .HasColumnName("KDGAW");
            entity.Property(e => e.Kdgeburtstag)
                .HasColumnType("datetime")
                .HasColumnName("KDGeburtstag");
            entity.Property(e => e.KdgesRabatt)
                .HasColumnType("money")
                .HasColumnName("KDGesRabatt");
            entity.Property(e => e.Kdgruppe)
                .HasDefaultValue((short)0)
                .HasColumnName("KDGruppe");
            entity.Property(e => e.KdhabenZins)
                .HasColumnType("money")
                .HasColumnName("KDHabenZins");
            entity.Property(e => e.Kdhmtl)
                .HasDefaultValue(false)
                .HasColumnName("KDHMtl");
            entity.Property(e => e.Kdhänger)
                .HasDefaultValue(false)
                .HasColumnName("KDHänger");
            entity.Property(e => e.Kdiban)
                .HasMaxLength(255)
                .HasColumnName("KDIBAN");
            entity.Property(e => e.Kdinternet)
                .HasMaxLength(50)
                .HasColumnName("KDInternet");
            entity.Property(e => e.Kdintra1)
                .HasMaxLength(40)
                .HasColumnName("KDIntra1");
            entity.Property(e => e.Kdintra2)
                .HasDefaultValue((short)0)
                .HasColumnName("KDIntra2");
            entity.Property(e => e.KdkontoNr)
                .HasMaxLength(20)
                .HasColumnName("KDKontoNr");
            entity.Property(e => e.Kdkreditlimit)
                .HasColumnType("money")
                .HasColumnName("KDKreditlimit");
            entity.Property(e => e.KdktrStelleNr).HasColumnName("KDKtrStelleNr");
            entity.Property(e => e.Kdland)
                .HasMaxLength(30)
                .HasColumnName("KDLand");
            entity.Property(e => e.Kdle)
                .HasMaxLength(1)
                .HasColumnName("KDLE");
            entity.Property(e => e.Kdleh)
                .HasMaxLength(30)
                .HasColumnName("KDLEH");
            entity.Property(e => e.KdletztAend)
                .HasColumnType("datetime")
                .HasColumnName("KDLetztAend");
            entity.Property(e => e.Kdlfr1)
                .HasMaxLength(70)
                .HasColumnName("KDLfr1");
            entity.Property(e => e.Kdlfr2)
                .HasMaxLength(70)
                .HasColumnName("KDLfr2");
            entity.Property(e => e.Kdlfr3)
                .HasMaxLength(70)
                .HasColumnName("KDLfr3");
            entity.Property(e => e.Kdlfr4)
                .HasMaxLength(70)
                .HasColumnName("KDLfr4");
            entity.Property(e => e.Kdlfstopp)
                .HasDefaultValue(false)
                .HasColumnName("KDLFStopp");
            entity.Property(e => e.KdlieferSatz)
                .HasColumnType("money")
                .HasColumnName("KDLieferSatz");
            entity.Property(e => e.Kdlieferrythmus).HasColumnName("KDLieferrythmus");
            entity.Property(e => e.KdmitTour)
                .HasDefaultValue(false)
                .HasColumnName("KDmitTour");
            entity.Property(e => e.Kdmitteilungstext)
                .HasMaxLength(255)
                .HasColumnName("KDMitteilungstext");
            entity.Property(e => e.Kdmobil)
                .HasMaxLength(20)
                .HasColumnName("KDMobil");
            entity.Property(e => e.Kdmtl)
                .HasDefaultValue(false)
                .HasColumnName("KDMtl");
            entity.Property(e => e.Kdname1)
                .HasMaxLength(30)
                .HasColumnName("KDName1");
            entity.Property(e => e.Kdname2)
                .HasMaxLength(30)
                .HasColumnName("KDName2");
            entity.Property(e => e.Kdname3)
                .HasMaxLength(30)
                .HasColumnName("KDName3");
            entity.Property(e => e.Kdnotiz).HasColumnName("KDNotiz");
            entity.Property(e => e.Kdort)
                .HasMaxLength(30)
                .HasColumnName("KDOrt");
            entity.Property(e => e.Kdplz)
                .HasMaxLength(10)
                .HasColumnName("KDPLZ");
            entity.Property(e => e.Kdplzpostfach)
                .HasMaxLength(10)
                .HasColumnName("KDPLZPostfach");
            entity.Property(e => e.Kdpostfach)
                .HasMaxLength(30)
                .HasColumnName("KDPostfach");
            entity.Property(e => e.KdpreislNr1)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr1");
            entity.Property(e => e.KdpreislNr2)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr2");
            entity.Property(e => e.KdpreislNr3)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr3");
            entity.Property(e => e.KdpreislNr4)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr4");
            entity.Property(e => e.KdpreislNr5)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr5");
            entity.Property(e => e.KdpreislNr6)
                .HasDefaultValue(0L)
                .HasColumnName("KDPreislNr6");
            entity.Property(e => e.KdrabText)
                .HasMaxLength(20)
                .HasColumnName("KDRabText");
            entity.Property(e => e.Kdrabatt)
                .HasDefaultValue((short)0)
                .HasColumnName("KDRabatt");
            entity.Property(e => e.KdreAusdrAnzahl).HasColumnName("KDReAusdrAnzahl");
            entity.Property(e => e.Kdsbg)
                .HasMaxLength(10)
                .HasColumnName("KDSBG");
            entity.Property(e => e.Kdskt1Proz)
                .HasColumnType("money")
                .HasColumnName("KDSkt1Proz");
            entity.Property(e => e.Kdskt1Tage).HasColumnName("KDSkt1Tage");
            entity.Property(e => e.Kdskt2Proz)
                .HasColumnType("money")
                .HasColumnName("KDSkt2Proz");
            entity.Property(e => e.Kdskt2Tage).HasColumnName("KDSkt2Tage");
            entity.Property(e => e.KdsollZins)
                .HasColumnType("money")
                .HasColumnName("KDSollZins");
            entity.Property(e => e.Kdsperr)
                .HasDefaultValue(false)
                .HasColumnName("KDSperr");
            entity.Property(e => e.Kdstaffelnr).HasColumnName("KDStaffelnr");
            entity.Property(e => e.Kdstrasse)
                .HasMaxLength(30)
                .HasColumnName("KDStrasse");
            entity.Property(e => e.Kdswift)
                .HasMaxLength(255)
                .HasColumnName("KDSwift");
            entity.Property(e => e.KdtageNetto).HasColumnName("KDTageNetto");
            entity.Property(e => e.Kdtelefax)
                .HasMaxLength(20)
                .HasColumnName("KDTelefax");
            entity.Property(e => e.Kdtelefon1)
                .HasMaxLength(20)
                .HasColumnName("KDTelefon1");
            entity.Property(e => e.Kdtelefon2)
                .HasMaxLength(20)
                .HasColumnName("KDTelefon2");
            entity.Property(e => e.KdtourenNr).HasColumnName("KDTourenNr");
            entity.Property(e => e.KdustIdnr)
                .HasMaxLength(20)
                .HasColumnName("KDUStIDNr");
            entity.Property(e => e.KdvertrNr)
                .HasDefaultValue(0L)
                .HasColumnName("KDVertrNr");
            entity.Property(e => e.Kdwtl)
                .HasDefaultValue(false)
                .HasColumnName("KDWtl");
            entity.Property(e => e.Kdzahlungsziel)
                .HasMaxLength(60)
                .HasColumnName("KDZahlungsziel");
            entity.Property(e => e.Kilometer).HasDefaultValue(0.0);
            entity.Property(e => e.KreditBem).HasMaxLength(255);
            entity.Property(e => e.LieferAvis).HasDefaultValue(false);
            entity.Property(e => e.LieferAvisDatum).HasColumnType("datetime");
            entity.Property(e => e.MandatNr).HasDefaultValue((short)0);
            entity.Property(e => e.Mvanalyse)
                .HasDefaultValue(false)
                .HasColumnName("MVAnalyse");
            entity.Property(e => e.MvanalyseFormular)
                .HasMaxLength(255)
                .HasColumnName("MVAnalyseFormular");
            entity.Property(e => e.MvdruckAnzahl).HasColumnName("MVDruckAnzahl");
            entity.Property(e => e.Mvetikett)
                .HasDefaultValue(false)
                .HasColumnName("MVEtikett");
            entity.Property(e => e.Mvformular)
                .HasMaxLength(255)
                .HasColumnName("MVFormular");
            entity.Property(e => e.Mvmail)
                .HasMaxLength(255)
                .HasColumnName("MVMail");
            entity.Property(e => e.MvmailVersand)
                .HasDefaultValue(false)
                .HasColumnName("MVMailVersand");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.Nvojahr)
                .HasDefaultValue((short)0)
                .HasColumnName("NVOJahr");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.Nvoscheinegeschrieben)
                .HasDefaultValue(false)
                .HasColumnName("NVOScheinegeschrieben");
            entity.Property(e => e.OrdersBest).HasDefaultValue(false);
            entity.Property(e => e.Ramail)
                .HasMaxLength(255)
                .HasColumnName("RAMail");
            entity.Property(e => e.SachKuNachweis).HasDefaultValue(false);
            entity.Property(e => e.SammelEmail)
                .HasDefaultValue(false)
                .HasColumnName("SammelEMail");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
            entity.Property(e => e.TimeWindowFrom1).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom2).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom3).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom4).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom5).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom6).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowFrom7).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo1).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo2).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo3).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo4).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo5).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo6).HasColumnType("datetime");
            entity.Property(e => e.TimeWindowTo7).HasColumnType("datetime");
            entity.Property(e => e.Vorkasse).HasDefaultValue(false);
            entity.Property(e => e.Vvonr)
                .HasMaxLength(30)
                .HasColumnName("VVONr");
            entity.Property(e => e.WgsmailVersand)
                .HasDefaultValue(false)
                .HasColumnName("WGSMailVersand");
            entity.Property(e => e.Zfeld1)
                .HasMaxLength(30)
                .HasColumnName("ZFeld1");
            entity.Property(e => e.Zfeld2)
                .HasMaxLength(30)
                .HasColumnName("ZFeld2");
            entity.Property(e => e.Zfeld3)
                .HasMaxLength(30)
                .HasColumnName("ZFeld3");
        });

        modelBuilder.Entity<Lagerbestand>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Lagerbestand$ID");

            entity.ToTable("Lagerbestand");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Artikelbezeichnung).HasMaxLength(30);
            entity.Property(e => e.Lgnr).HasColumnName("LGNr");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.Zonenpreis).HasColumnType("money");
        });

        modelBuilder.Entity<Lagerstelle>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Lagerstelle$ID");

            entity.ToTable("Lagerstelle");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Lgbemerkung).HasColumnName("LGBemerkung");
            entity.Property(e => e.Lgemail)
                .HasMaxLength(30)
                .HasColumnName("LGEmail");
            entity.Property(e => e.Lgfax)
                .HasMaxLength(30)
                .HasColumnName("LGFax");
            entity.Property(e => e.Lgkng)
                .HasMaxLength(5)
                .HasColumnName("LGKng");
            entity.Property(e => e.Lgmobil)
                .HasMaxLength(30)
                .HasColumnName("LGMobil");
            entity.Property(e => e.Lgname1)
                .HasMaxLength(30)
                .HasColumnName("LGName1");
            entity.Property(e => e.Lgname2)
                .HasMaxLength(30)
                .HasColumnName("LGName2");
            entity.Property(e => e.Lgname3)
                .HasMaxLength(30)
                .HasColumnName("LGName3");
            entity.Property(e => e.Lgnr).HasColumnName("LGNr");
            entity.Property(e => e.Lgort)
                .HasMaxLength(30)
                .HasColumnName("LGOrt");
            entity.Property(e => e.Lgplz)
                .HasMaxLength(10)
                .HasColumnName("LGPLZ");
            entity.Property(e => e.Lgsbg)
                .HasMaxLength(10)
                .HasColumnName("LGSBG");
            entity.Property(e => e.Lgstrasse)
                .HasMaxLength(30)
                .HasColumnName("LGStrasse");
            entity.Property(e => e.Lgtelefon1)
                .HasMaxLength(30)
                .HasColumnName("LGTelefon1");
            entity.Property(e => e.Lgtelefon2)
                .HasMaxLength(30)
                .HasColumnName("LGTelefon2");
            entity.Property(e => e.Lgvertreter).HasColumnName("LGVertreter");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Leergut>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Leergut$ID");

            entity.ToTable("Leergut");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LgartBez)
                .HasMaxLength(30)
                .HasColumnName("LGArtBez");
            entity.Property(e => e.LgartNr)
                .HasDefaultValue(0L)
                .HasColumnName("LGArtNr");
            entity.Property(e => e.LgartNrWare)
                .HasDefaultValue(0L)
                .HasColumnName("LGArtNrWare");
            entity.Property(e => e.Lgdatum)
                .HasColumnType("datetime")
                .HasColumnName("LGDatum");
            entity.Property(e => e.Lggesamt)
                .HasColumnType("money")
                .HasColumnName("LGGesamt");
            entity.Property(e => e.LgkdNr)
                .HasDefaultValue(0L)
                .HasColumnName("LGKdNr");
            entity.Property(e => e.Lglsnr)
                .HasDefaultValue(0L)
                .HasColumnName("LGLSNr");
            entity.Property(e => e.Lgmenge)
                .HasColumnType("money")
                .HasColumnName("LGMenge");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });

        modelBuilder.Entity<Lfartikel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("LFArtikel$ID");

            entity.ToTable("LFArtikel");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BestNr).HasMaxLength(50);
            entity.Property(e => e.Lfkuerz)
                .HasMaxLength(50)
                .HasColumnName("LFKuerz");
            entity.Property(e => e.Lfnr).HasColumnName("LFNr");
            entity.Property(e => e.MinBmenge)
                .HasColumnType("money")
                .HasColumnName("MinBMenge");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vorlaufzeit).HasMaxLength(30);
        });

        modelBuilder.Entity<LfgetrPrei>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("LFGetrPreis$ID");

            entity.ToTable("LFGetrPreis");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Lfnr).HasColumnName("LFNr");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.PreisFn)
                .HasColumnType("money")
                .HasColumnName("PreisFN");
            entity.Property(e => e.PreisNz)
                .HasColumnType("money")
                .HasColumnName("PreisNZ");
            entity.Property(e => e.PreisVz)
                .HasColumnType("money")
                .HasColumnName("PreisVZ");
        });

        modelBuilder.Entity<LfpreisOffert>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("LFPreisOffert$ID");

            entity.ToTable("LFPreisOffert");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Betreuer).HasMaxLength(15);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Datum1).HasColumnType("datetime");
            entity.Property(e => e.Datum2).HasColumnType("datetime");
            entity.Property(e => e.Datum3).HasColumnType("datetime");
            entity.Property(e => e.Erledigt)
                .HasDefaultValue(false)
                .HasColumnName("erledigt");
            entity.Property(e => e.Ernte).HasDefaultValue((short)0);
            entity.Property(e => e.Feuchte).HasColumnType("money");
            entity.Property(e => e.GetrArt).HasMaxLength(20);
            entity.Property(e => e.GetrKng).HasMaxLength(4);
            entity.Property(e => e.Gwert1)
                .HasMaxLength(10)
                .HasColumnName("GWert1");
            entity.Property(e => e.Gwert10)
                .HasMaxLength(10)
                .HasColumnName("GWert10");
            entity.Property(e => e.Gwert11)
                .HasMaxLength(10)
                .HasColumnName("GWert11");
            entity.Property(e => e.Gwert12)
                .HasMaxLength(10)
                .HasColumnName("GWert12");
            entity.Property(e => e.Gwert2)
                .HasMaxLength(10)
                .HasColumnName("GWert2");
            entity.Property(e => e.Gwert3)
                .HasMaxLength(10)
                .HasColumnName("GWert3");
            entity.Property(e => e.Gwert4)
                .HasMaxLength(10)
                .HasColumnName("GWert4");
            entity.Property(e => e.Gwert5)
                .HasMaxLength(10)
                .HasColumnName("GWert5");
            entity.Property(e => e.Gwert6)
                .HasMaxLength(10)
                .HasColumnName("GWert6");
            entity.Property(e => e.Gwert7)
                .HasMaxLength(10)
                .HasColumnName("GWert7");
            entity.Property(e => e.Gwert8)
                .HasMaxLength(10)
                .HasColumnName("GWert8");
            entity.Property(e => e.Gwert9)
                .HasMaxLength(10)
                .HasColumnName("GWert9");
            entity.Property(e => e.Lfnr).HasColumnName("LFNr");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Preis1).HasColumnType("money");
            entity.Property(e => e.Preis2).HasColumnType("money");
            entity.Property(e => e.Preis3).HasColumnType("money");
            entity.Property(e => e.Sorte).HasMaxLength(30);
        });

        modelBuilder.Entity<Lieferanten>(entity =>
        {
            entity.HasKey(e => new { e.Lfnummer, e.Id }).HasName("Lieferanten$LiefNr");

            entity.ToTable("Lieferanten");

            entity.Property(e => e.Lfnummer).HasColumnName("LFNummer");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.AnspPartn).HasMaxLength(30);
            entity.Property(e => e.DruckForm1).HasMaxLength(255);
            entity.Property(e => e.DruckGbvform)
                .HasMaxLength(255)
                .HasColumnName("DruckGBVForm");
            entity.Property(e => e.EigLager).HasDefaultValue(false);
            entity.Property(e => e.EmailVersand)
                .HasDefaultValue(false)
                .HasColumnName("EMailVersand");
            entity.Property(e => e.Gbvgewicht)
                .HasDefaultValue((short)0)
                .HasColumnName("GBVGewicht");
            entity.Property(e => e.Kreis).HasMaxLength(5);
            entity.Property(e => e.KtabrStelle).HasColumnName("KTAbrStelle");
            entity.Property(e => e.LbioNr)
                .HasMaxLength(50)
                .HasColumnName("LBioNr");
            entity.Property(e => e.Leh)
                .HasMaxLength(255)
                .HasColumnName("LEH");
            entity.Property(e => e.Lfanrede)
                .HasMaxLength(30)
                .HasColumnName("LFAnrede");
            entity.Property(e => e.Lfasld).HasColumnName("LFAsld");
            entity.Property(e => e.Lfbank)
                .HasMaxLength(30)
                .HasColumnName("LFBank");
            entity.Property(e => e.LfbankPlz)
                .HasMaxLength(10)
                .HasColumnName("LFBankPLZ");
            entity.Property(e => e.Lfbemerkung).HasColumnName("LFBemerkung");
            entity.Property(e => e.LfbldKng)
                .HasMaxLength(2)
                .HasColumnName("LFBldKng");
            entity.Property(e => e.Lfblz)
                .HasMaxLength(8)
                .HasColumnName("LFBLZ");
            entity.Property(e => e.Lfbr1)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr1");
            entity.Property(e => e.Lfbr2)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr2");
            entity.Property(e => e.Lfbr3)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr3");
            entity.Property(e => e.Lfbr4)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr4");
            entity.Property(e => e.Lfbr5)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr5");
            entity.Property(e => e.Lfbr6)
                .HasDefaultValue((short)0)
                .HasColumnName("LFBr6");
            entity.Property(e => e.Lfbundesland)
                .HasMaxLength(30)
                .HasColumnName("LFBundesland");
            entity.Property(e => e.LfeigKdnr)
                .HasMaxLength(25)
                .HasColumnName("LFEigKDNr");
            entity.Property(e => e.Lfemail)
                .HasMaxLength(255)
                .HasColumnName("LFEMail");
            entity.Property(e => e.LfentfKlasse).HasColumnName("LFEntfKlasse");
            entity.Property(e => e.Lferzeugergem)
                .HasDefaultValue(false)
                .HasColumnName("LFErzeugergem");
            entity.Property(e => e.Lfezg1).HasColumnName("LFEZG1");
            entity.Property(e => e.Lfezg2).HasColumnName("LFEZG2");
            entity.Property(e => e.Lfezg3).HasColumnName("LFEZG3");
            entity.Property(e => e.Lfezg4).HasColumnName("LFEZG4");
            entity.Property(e => e.Lfezg5).HasColumnName("LFEZG5");
            entity.Property(e => e.Lffiliale)
                .HasMaxLength(1)
                .HasColumnName("LFFiliale");
            entity.Property(e => e.Lfgaw)
                .HasDefaultValue((short)0)
                .HasColumnName("LFGAW");
            entity.Property(e => e.Lfhdlkng)
                .HasDefaultValue(false)
                .HasColumnName("LFHdlkng");
            entity.Property(e => e.Lfiban)
                .HasMaxLength(30)
                .HasColumnName("LFIBAN");
            entity.Property(e => e.Lfinternet)
                .HasMaxLength(40)
                .HasColumnName("LFInternet");
            entity.Property(e => e.LfintraKng)
                .HasMaxLength(4)
                .HasColumnName("LFIntraKng");
            entity.Property(e => e.LfintraLand)
                .HasMaxLength(30)
                .HasColumnName("LFIntraLand");
            entity.Property(e => e.Lfkonto)
                .HasMaxLength(20)
                .HasColumnName("LFKonto");
            entity.Property(e => e.Lfland)
                .HasMaxLength(30)
                .HasColumnName("LFLand");
            entity.Property(e => e.Lfmobil)
                .HasMaxLength(20)
                .HasColumnName("LFMobil");
            entity.Property(e => e.Lfname1)
                .HasMaxLength(30)
                .HasColumnName("LFName1");
            entity.Property(e => e.Lfname2)
                .HasMaxLength(30)
                .HasColumnName("LFName2");
            entity.Property(e => e.Lfname3)
                .HasMaxLength(30)
                .HasColumnName("LFName3");
            entity.Property(e => e.LfnettoTage).HasColumnName("LFNettoTage");
            entity.Property(e => e.Lfort)
                .HasMaxLength(30)
                .HasColumnName("LFOrt");
            entity.Property(e => e.Lfplz)
                .HasMaxLength(10)
                .HasColumnName("LFPLZ");
            entity.Property(e => e.Lfplzpostfach)
                .HasMaxLength(10)
                .HasColumnName("LFPLZPostfach");
            entity.Property(e => e.Lfpostfach)
                .HasMaxLength(20)
                .HasColumnName("LFPostfach");
            entity.Property(e => e.LfpräfNr)
                .HasMaxLength(10)
                .HasColumnName("LFPräfNr");
            entity.Property(e => e.Lfsbg)
                .HasMaxLength(10)
                .HasColumnName("LFSBG");
            entity.Property(e => e.Lfskto1)
                .HasColumnType("money")
                .HasColumnName("LFSkto1");
            entity.Property(e => e.Lfskto2)
                .HasColumnType("money")
                .HasColumnName("LFSkto2");
            entity.Property(e => e.Lfsperr)
                .HasDefaultValue(false)
                .HasColumnName("LFSperr");
            entity.Property(e => e.LfstNr)
                .HasMaxLength(20)
                .HasColumnName("LFStNr");
            entity.Property(e => e.LfstProzent)
                .HasColumnType("money")
                .HasColumnName("LFStProzent");
            entity.Property(e => e.LfstSchlüssel).HasColumnName("LFStSchlüssel");
            entity.Property(e => e.Lfstatus)
                .HasMaxLength(15)
                .HasColumnName("LFStatus");
            entity.Property(e => e.Lfstrasse)
                .HasMaxLength(30)
                .HasColumnName("LFStrasse");
            entity.Property(e => e.Lfswift)
                .HasMaxLength(30)
                .HasColumnName("LFSwift");
            entity.Property(e => e.Lftage1).HasColumnName("LFTage1");
            entity.Property(e => e.Lftage2).HasColumnName("LFTage2");
            entity.Property(e => e.Lftelefax)
                .HasMaxLength(20)
                .HasColumnName("LFTelefax");
            entity.Property(e => e.Lftelefon1)
                .HasMaxLength(20)
                .HasColumnName("LFTelefon1");
            entity.Property(e => e.Lftelefon2)
                .HasMaxLength(20)
                .HasColumnName("LFTelefon2");
            entity.Property(e => e.LfustIdNr)
                .HasMaxLength(18)
                .HasColumnName("LFUstIdNr");
            entity.Property(e => e.LfvertrNr)
                .HasDefaultValue(0L)
                .HasColumnName("LFVertrNr");
            entity.Property(e => e.LfwhgSchl)
                .HasMaxLength(4)
                .HasColumnName("LFWhgSchl");
            entity.Property(e => e.Lfwhrg)
                .HasMaxLength(4)
                .HasColumnName("LFWhrg");
            entity.Property(e => e.LfwhrgKurs)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("LFWhrgKurs");
            entity.Property(e => e.Lfzuschlag)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("LFZuschlag");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.Nvoco2)
                .HasDefaultValue(0.0)
                .HasColumnName("NVOCO2");
            entity.Property(e => e.Nvojahr)
                .HasDefaultValue((short)0)
                .HasColumnName("NVOJahr");
            entity.Property(e => e.Nvokm)
                .HasDefaultValue(0.0)
                .HasColumnName("NVOKM");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.Nvoscheinegeschrieben)
                .HasDefaultValue(false)
                .HasColumnName("NVOScheinegeschrieben");
            entity.Property(e => e.QszertNr)
                .HasMaxLength(30)
                .HasColumnName("QSZertNr");
            entity.Property(e => e.Qszertifizierung)
                .HasColumnType("datetime")
                .HasColumnName("QSZertifizierung");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
            entity.Property(e => e.Zertifizierung).HasColumnType("datetime");
        });

        modelBuilder.Entity<MengenstaffelKu>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MengenstaffelKU$ID");

            entity.ToTable("MengenstaffelKU");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bstaffel1)
                .HasColumnType("money")
                .HasColumnName("BStaffel1");
            entity.Property(e => e.Bstaffel2)
                .HasColumnType("money")
                .HasColumnName("BStaffel2");
            entity.Property(e => e.Bstaffel3)
                .HasColumnType("money")
                .HasColumnName("BStaffel3");
            entity.Property(e => e.Bstaffel4)
                .HasColumnType("money")
                .HasColumnName("BStaffel4");
            entity.Property(e => e.Bstaffel5)
                .HasColumnType("money")
                .HasColumnName("BStaffel5");
            entity.Property(e => e.Bstaffel6)
                .HasColumnType("money")
                .HasColumnName("BStaffel6");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<MibuProz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MibuProz$ID");

            entity.ToTable("MibuProz");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.MspartNr).HasColumnName("MSPArtNr");
            entity.Property(e => e.Mspbez1)
                .HasMaxLength(30)
                .HasColumnName("MSPBez1");
            entity.Property(e => e.Mspbez2)
                .HasMaxLength(30)
                .HasColumnName("MSPBez2");
            entity.Property(e => e.MspchgNr)
                .HasMaxLength(15)
                .HasColumnName("MSPChgNr");
            entity.Property(e => e.Mspeh)
                .HasMaxLength(10)
                .HasColumnName("MSPEh");
            entity.Property(e => e.Mspnr).HasColumnName("MSPNr");
            entity.Property(e => e.Msppos).HasColumnName("MSPPos");
            entity.Property(e => e.Mspsatz)
                .HasColumnType("money")
                .HasColumnName("MSPSatz");
        });

        modelBuilder.Entity<MibuStamm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MibuStamm$ID");

            entity.ToTable("MibuStamm");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.MsartNr).HasColumnName("MSArtNr");
            entity.Property(e => e.Msbem)
                .HasMaxLength(255)
                .HasColumnName("MSBem");
            entity.Property(e => e.Msbez1)
                .HasMaxLength(30)
                .HasColumnName("MSBez1");
            entity.Property(e => e.Msbez2)
                .HasMaxLength(30)
                .HasColumnName("MSBez2");
            entity.Property(e => e.Msbez3)
                .HasMaxLength(30)
                .HasColumnName("MSBez3");
            entity.Property(e => e.Msbez4)
                .HasMaxLength(30)
                .HasColumnName("MSBez4");
            entity.Property(e => e.Msdatum)
                .HasColumnType("datetime")
                .HasColumnName("MSDatum");
            entity.Property(e => e.Msmatch)
                .HasMaxLength(10)
                .HasColumnName("MSMatch");
            entity.Property(e => e.Msnr).HasColumnName("MSNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<MibuVp>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MibuVP$ID");

            entity.ToTable("MibuVP");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.MsvartNr).HasColumnName("MSVArtNr");
            entity.Property(e => e.Msvbez1)
                .HasMaxLength(30)
                .HasColumnName("MSVBez1");
            entity.Property(e => e.Msvbez2)
                .HasMaxLength(30)
                .HasColumnName("MSVBez2");
            entity.Property(e => e.MsvchrgNr)
                .HasMaxLength(15)
                .HasColumnName("MSVChrgNr");
            entity.Property(e => e.Msvmenge)
                .HasColumnType("money")
                .HasColumnName("MSVMenge");
            entity.Property(e => e.Msvnr).HasColumnName("MSVNr");
            entity.Property(e => e.Msvpos).HasColumnName("MSVPos");
            entity.Property(e => e.Msvvpe)
                .HasMaxLength(10)
                .HasColumnName("MSVVPE");
        });

        modelBuilder.Entity<MstPo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MStPos$ID");

            entity.ToTable("MStPos");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlteMenge).HasColumnType("money");
            entity.Property(e => e.BezGr).HasMaxLength(8);
            entity.Property(e => e.Bezeichnung).HasMaxLength(30);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.Ekpreis)
                .HasColumnType("money")
                .HasColumnName("EKPreis");
            entity.Property(e => e.ErfSchema).HasMaxLength(3);
            entity.Property(e => e.Faktor).HasMaxLength(4);
            entity.Property(e => e.GesMenge).HasColumnType("money");
            entity.Property(e => e.GesStck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.KolliInh).HasDefaultValue(0L);
            entity.Property(e => e.KontraktNrVk).HasColumnName("KontraktNrVK");
            entity.Property(e => e.Mhd)
                .HasColumnType("datetime")
                .HasColumnName("MHD");
            entity.Property(e => e.MwSt).HasColumnType("money");
            entity.Property(e => e.MwSt2).HasColumnType("money");
            entity.Property(e => e.PakGewicht).HasColumnType("money");
            entity.Property(e => e.PalGew).HasColumnType("money");
            entity.Property(e => e.Pfracht)
                .HasColumnType("money")
                .HasColumnName("PFracht");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vkpreis)
                .HasColumnType("money")
                .HasColumnName("VKPreis");
        });

        modelBuilder.Entity<Mstrecke>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MStrecke$ID");

            entity.ToTable("MStrecke");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AblStLand).HasMaxLength(30);
            entity.Property(e => e.AblStName).HasMaxLength(30);
            entity.Property(e => e.AblStOrt).HasMaxLength(30);
            entity.Property(e => e.AblStPlz)
                .HasMaxLength(10)
                .HasColumnName("AblStPLZ");
            entity.Property(e => e.AblStStrasse).HasMaxLength(30);
            entity.Property(e => e.AblStTelefax).HasMaxLength(20);
            entity.Property(e => e.AblStTelefon).HasMaxLength(20);
            entity.Property(e => e.Anlagedatum).HasColumnType("datetime");
            entity.Property(e => e.AsldEk)
                .HasMaxLength(2)
                .HasColumnName("AsldEK");
            entity.Property(e => e.AsldVk)
                .HasMaxLength(2)
                .HasColumnName("AsldVK");
            entity.Property(e => e.AuftrDatumBis).HasColumnType("datetime");
            entity.Property(e => e.AuftrDatumVon).HasColumnType("datetime");
            entity.Property(e => e.AuftrNrEk).HasColumnName("AuftrNrEK");
            entity.Property(e => e.AuftrNrVk).HasColumnName("AuftrNrVK");
            entity.Property(e => e.BelStLand).HasMaxLength(30);
            entity.Property(e => e.BelStName).HasMaxLength(30);
            entity.Property(e => e.BelStOrt).HasMaxLength(30);
            entity.Property(e => e.BelStPlz)
                .HasMaxLength(5)
                .HasColumnName("BelStPLZ");
            entity.Property(e => e.BelStStrasse).HasMaxLength(30);
            entity.Property(e => e.BelStTelefax).HasMaxLength(20);
            entity.Property(e => e.BelStTelefon).HasMaxLength(20);
            entity.Property(e => e.BemEk).HasColumnName("BemEK");
            entity.Property(e => e.BemVk).HasColumnName("BemVK");
            entity.Property(e => e.Betreuer).HasMaxLength(25);
            entity.Property(e => e.DispoKng).HasDefaultValue(false);
            entity.Property(e => e.EigLagerEk)
                .HasDefaultValue(false)
                .HasColumnName("EigLagerEK");
            entity.Property(e => e.EigLagerVk)
                .HasDefaultValue(false)
                .HasColumnName("EigLagerVK");
            entity.Property(e => e.EigWare).HasDefaultValue(false);
            entity.Property(e => e.FrachtPreis).HasColumnType("money");
            entity.Property(e => e.FremdLsnrEk)
                .HasMaxLength(30)
                .HasColumnName("FremdLSNrEK");
            entity.Property(e => e.FremdLsnrVk)
                .HasMaxLength(30)
                .HasColumnName("FremdLSNrVK");
            entity.Property(e => e.FremdRenrEk)
                .HasMaxLength(30)
                .HasColumnName("FremdRENrEK");
            entity.Property(e => e.FremdRenrVk)
                .HasMaxLength(30)
                .HasColumnName("FremdRENrVK");
            entity.Property(e => e.LandEk)
                .HasMaxLength(30)
                .HasColumnName("LandEK");
            entity.Property(e => e.LandVk)
                .HasMaxLength(30)
                .HasColumnName("LandVK");
            entity.Property(e => e.Lkw)
                .HasDefaultValue(false)
                .HasColumnName("LKW");
            entity.Property(e => e.Name1Ek)
                .HasMaxLength(30)
                .HasColumnName("Name1EK");
            entity.Property(e => e.Name1Vk)
                .HasMaxLength(30)
                .HasColumnName("Name1VK");
            entity.Property(e => e.Name2Ek)
                .HasMaxLength(30)
                .HasColumnName("Name2EK");
            entity.Property(e => e.Name2Vk)
                .HasMaxLength(30)
                .HasColumnName("Name2VK");
            entity.Property(e => e.OrtEk)
                .HasMaxLength(30)
                .HasColumnName("OrtEK");
            entity.Property(e => e.OrtVk)
                .HasMaxLength(30)
                .HasColumnName("OrtVK");
            entity.Property(e => e.PersNrEk).HasColumnName("PersNrEK");
            entity.Property(e => e.PersNrVk).HasColumnName("PersNrVK");
            entity.Property(e => e.Plzek)
                .HasMaxLength(10)
                .HasColumnName("PLZEK");
            entity.Property(e => e.Plzvk)
                .HasMaxLength(10)
                .HasColumnName("PLZVK");
            entity.Property(e => e.RenrEk).HasColumnName("RENrEK");
            entity.Property(e => e.RenrVk).HasColumnName("RENrVK");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SpedLand).HasMaxLength(30);
            entity.Property(e => e.SpedName).HasMaxLength(30);
            entity.Property(e => e.SpedName2).HasMaxLength(30);
            entity.Property(e => e.SpedName3).HasMaxLength(30);
            entity.Property(e => e.SpedOrt).HasMaxLength(30);
            entity.Property(e => e.SpedPlz)
                .HasMaxLength(10)
                .HasColumnName("SpedPLZ");
            entity.Property(e => e.SpedStrasse).HasMaxLength(30);
            entity.Property(e => e.SpedTelefax).HasMaxLength(20);
            entity.Property(e => e.SpedTelefon).HasMaxLength(20);
            entity.Property(e => e.StfrSpedNr)
                .HasMaxLength(15)
                .HasColumnName("STFrSpedNr");
            entity.Property(e => e.StrasseEk)
                .HasMaxLength(30)
                .HasColumnName("StrasseEK");
            entity.Property(e => e.StrasseVk)
                .HasMaxLength(30)
                .HasColumnName("StrasseVK");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.TelefaxEk)
                .HasMaxLength(20)
                .HasColumnName("TelefaxEK");
            entity.Property(e => e.TelefaxVk)
                .HasMaxLength(20)
                .HasColumnName("TelefaxVK");
            entity.Property(e => e.TelefonEk)
                .HasMaxLength(20)
                .HasColumnName("TelefonEK");
            entity.Property(e => e.TelefonVk)
                .HasMaxLength(20)
                .HasColumnName("TelefonVK");
            entity.Property(e => e.Uhrzeit).HasMaxLength(50);
        });

        modelBuilder.Entity<Nvoartikel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NVOArtikel$ID");

            entity.ToTable("NVOArtikel");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtBez1).HasMaxLength(30);
            entity.Property(e => e.ArtBez2).HasMaxLength(30);
            entity.Property(e => e.ArtSbg)
                .HasMaxLength(10)
                .HasColumnName("ArtSBG");
            entity.Property(e => e.Bilanzdatum).HasColumnType("datetime");
            entity.Property(e => e.GetrArt).HasMaxLength(20);
            entity.Property(e => e.NichtNvomenge)
                .HasColumnType("money")
                .HasColumnName("nichtNVOMenge");
            entity.Property(e => e.Nvomenge)
                .HasColumnType("money")
                .HasColumnName("NVOMenge");
        });

        modelBuilder.Entity<Nvobilanz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NVOBilanz$ID");

            entity.ToTable("NVOBilanz");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abgang).HasColumnType("money");
            entity.Property(e => e.Abgeschlossen).HasDefaultValue(false);
            entity.Property(e => e.Abschlussdatum).HasColumnType("datetime");
            entity.Property(e => e.Anfangsbestand).HasColumnType("money");
            entity.Property(e => e.ArtBezText1).HasMaxLength(30);
            entity.Property(e => e.ArtBezText2).HasMaxLength(30);
            entity.Property(e => e.Bestand).HasColumnType("money");
            entity.Property(e => e.NachhaltigeWare).HasDefaultValue(false);
            entity.Property(e => e.Schwund).HasColumnType("money");
            entity.Property(e => e.ZeitraumBis).HasColumnType("datetime");
            entity.Property(e => e.ZeitraumVon).HasColumnType("datetime");
            entity.Property(e => e.Zugang).HasColumnType("money");
        });

        modelBuilder.Entity<NvodepotBilanz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NVODepotBilanz$ID");

            entity.ToTable("NVODepotBilanz");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Abgang).HasColumnType("money");
            entity.Property(e => e.Abgeschlossen).HasDefaultValue(false);
            entity.Property(e => e.Abschlussdatum).HasColumnType("datetime");
            entity.Property(e => e.Anfangsbestand).HasColumnType("money");
            entity.Property(e => e.ArtBezText1).HasMaxLength(30);
            entity.Property(e => e.ArtBezText2).HasMaxLength(30);
            entity.Property(e => e.Bestand).HasColumnType("money");
            entity.Property(e => e.DepotInhaber).HasMaxLength(30);
            entity.Property(e => e.NachhaltigeWare).HasDefaultValue(false);
            entity.Property(e => e.Schwund).HasColumnType("money");
            entity.Property(e => e.ZeitraumBis).HasColumnType("datetime");
            entity.Property(e => e.ZeitraumVon).HasColumnType("datetime");
            entity.Property(e => e.Zugang).HasColumnType("money");
        });

        modelBuilder.Entity<Nvokonto>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NVOKonto$ID");

            entity.ToTable("NVOKonto");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.User).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(50);
        });

        modelBuilder.Entity<OptLexware>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("OptLexware$ID");

            entity.ToTable("OptLexware");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Fibu2Aab).HasColumnName("Fibu2AAB");
            entity.Property(e => e.Fibu2Ar).HasColumnName("Fibu2AR");
            entity.Property(e => e.Fibu2Er).HasColumnName("Fibu2ER");
            entity.Property(e => e.Fibu2Gb).HasColumnName("Fibu2GB");
            entity.Property(e => e.Fibu2Pfad).HasMaxLength(255);
            entity.Property(e => e.Fibu2Sta).HasColumnName("Fibu2STA");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Optionen>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Optionen$ID");

            entity.ToTable("Optionen");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlsanzAusdr2).HasColumnName("ALSAnzAusdr2");
            entity.Property(e => e.AlsartNrDruck).HasColumnName("ALSArtNrDruck");
            entity.Property(e => e.AlsauftrSperren).HasColumnName("ALSAuftrSperren");
            entity.Property(e => e.Alsauftrag).HasColumnName("ALSAuftrag");
            entity.Property(e => e.AlsautoDruck).HasColumnName("ALSAutoDruck");
            entity.Property(e => e.AlsautoDruckA).HasColumnName("ALSAutoDruckA");
            entity.Property(e => e.AlsdruckerNr2).HasColumnName("ALSDruckerNr2");
            entity.Property(e => e.AlsdrucktextA)
                .HasMaxLength(8)
                .HasColumnName("ALSDrucktextA");
            entity.Property(e => e.AlsdrucktextLs)
                .HasMaxLength(8)
                .HasColumnName("ALSDrucktextLS");
            entity.Property(e => e.AlsdrucktextRt)
                .HasMaxLength(8)
                .HasColumnName("ALSDrucktextRT");
            entity.Property(e => e.Alseingabe).HasColumnName("ALSEingabe");
            entity.Property(e => e.Alslagerakt).HasColumnName("ALSLagerakt");
            entity.Property(e => e.AlsletzterPreis).HasColumnName("ALSLetzterPreis");
            entity.Property(e => e.Alsliefersch).HasColumnName("ALSLiefersch");
            entity.Property(e => e.Alsnr).HasColumnName("ALSNr");
            entity.Property(e => e.AlsnrIndex).HasColumnName("ALSNrIndex");
            entity.Property(e => e.Alsopanzeige).HasColumnName("ALSOPAnzeige");
            entity.Property(e => e.Alsposition).HasColumnName("ALSPosition");
            entity.Property(e => e.AlsprAbglWere).HasColumnName("ALSPrAbglWERE");
            entity.Property(e => e.AlsprEkvk).HasColumnName("ALSPrEKVK");
            entity.Property(e => e.AlspreisEingabe).HasColumnName("ALSPreisEingabe");
            entity.Property(e => e.Alsserienerfassung).HasColumnName("ALSSerienerfassung");
            entity.Property(e => e.AlssortF).HasColumnName("ALSSortF");
            entity.Property(e => e.AlssortN).HasColumnName("ALSSortN");
            entity.Property(e => e.Alszustellartabfr).HasColumnName("ALSZustellartabfr");
            entity.Property(e => e.AnlanzAusdr1).HasColumnName("ANLAnzAusdr1");
            entity.Property(e => e.AnlanzAusdr2).HasColumnName("ANLAnzAusdr2");
            entity.Property(e => e.AnldruckerNr1).HasColumnName("ANLDruckerNr1");
            entity.Property(e => e.AnldruckerNr2).HasColumnName("ANLDruckerNr2");
            entity.Property(e => e.ArdatumAb).HasColumnName("ARDatumAb");
            entity.Property(e => e.Ardrname1)
                .HasMaxLength(255)
                .HasColumnName("ARDRName1");
            entity.Property(e => e.ArgrpSkt).HasColumnName("ARGrpSkt");
            entity.Property(e => e.Arzusa).HasColumnName("ARZusa");
            entity.Property(e => e.ArzwiZei).HasColumnName("ARZwiZei");
            entity.Property(e => e.AufanzAusdr1).HasColumnName("AUFAnzAusdr1");
            entity.Property(e => e.AufanzAusdr2).HasColumnName("AUFAnzAusdr2");
            entity.Property(e => e.AufdruckerNr1).HasColumnName("AUFDruckerNr1");
            entity.Property(e => e.AufdruckerNr2).HasColumnName("AUFDruckerNr2");
            entity.Property(e => e.Dp1man).HasColumnName("DP1Man");
            entity.Property(e => e.Dp1nr).HasColumnName("DP1Nr");
            entity.Property(e => e.Dp2man).HasColumnName("DP2Man");
            entity.Property(e => e.Dp2nr).HasColumnName("DP2Nr");
            entity.Property(e => e.ElsdruckerNr).HasColumnName("ELSDruckerNr");
            entity.Property(e => e.Elsparam1).HasColumnName("ELSParam1");
            entity.Property(e => e.Elsparam2).HasColumnName("ELSParam2");
            entity.Property(e => e.Elsparam3).HasColumnName("ELSParam3");
            entity.Property(e => e.Ercheck1).HasColumnName("ERCheck1");
            entity.Property(e => e.Ercheck2).HasColumnName("ERCheck2");
            entity.Property(e => e.Ercheck3).HasColumnName("ERCheck3");
            entity.Property(e => e.Ercheck4).HasColumnName("ERCheck4");
            entity.Property(e => e.Ercheck5).HasColumnName("ERCheck5");
            entity.Property(e => e.Ercheck6).HasColumnName("ERCheck6");
            entity.Property(e => e.Ercheck7).HasColumnName("ERCheck7");
            entity.Property(e => e.ErdruckParameter).HasColumnName("ERDruckParameter");
            entity.Property(e => e.ErdruckerNr).HasColumnName("ERDruckerNr");
            entity.Property(e => e.Erdrucktext)
                .HasMaxLength(8)
                .HasColumnName("ERDrucktext");
            entity.Property(e => e.ErindexNr).HasColumnName("ERIndexNr");
            entity.Property(e => e.ErindexParameter).HasColumnName("ERIndexParameter");
            entity.Property(e => e.Gbanz1).HasColumnName("GBAnz1");
            entity.Property(e => e.Gbanz2).HasColumnName("GBAnz2");
            entity.Property(e => e.GbberFaktor).HasColumnName("GBBerFaktor");
            entity.Property(e => e.GbbruttoAbr).HasColumnName("GBBruttoAbr");
            entity.Property(e => e.GbbruttoSilo).HasColumnName("GBBruttoSilo");
            entity.Property(e => e.Gbdarst).HasColumnName("GBDarst");
            entity.Property(e => e.GbdatumAb).HasColumnName("GBDatumAb");
            entity.Property(e => e.GbdruckAbfr).HasColumnName("GBDruckAbfr");
            entity.Property(e => e.Gbindex).HasColumnName("GBIndex");
            entity.Property(e => e.Gbkdnrbis).HasColumnName("GBKDNRBis");
            entity.Property(e => e.Gbkdnrvon).HasColumnName("GBKDNRVon");
            entity.Property(e => e.GbktrAlle).HasColumnName("GBKtrAlle");
            entity.Property(e => e.Gblfnrbis).HasColumnName("GBLFNRBis");
            entity.Property(e => e.Gblfnrvon).HasColumnName("GBLFNRVon");
            entity.Property(e => e.Gblswe).HasColumnName("GBLSWE");
            entity.Property(e => e.Gbmt1)
                .HasMaxLength(60)
                .HasColumnName("GBMT1");
            entity.Property(e => e.Gbmt2)
                .HasMaxLength(60)
                .HasColumnName("GBMT2");
            entity.Property(e => e.GbnrDrehen).HasColumnName("GBNrDrehen");
            entity.Property(e => e.Gboelpos1).HasColumnName("GBOelpos1");
            entity.Property(e => e.Gboelpos2).HasColumnName("GBOelpos2");
            entity.Property(e => e.Gboelpos3).HasColumnName("GBOelpos3");
            entity.Property(e => e.Gbpartie).HasColumnName("GBPartie");
            entity.Property(e => e.Gbrapspos1).HasColumnName("GBRapspos1");
            entity.Property(e => e.Gbrapspos2).HasColumnName("GBRapspos2");
            entity.Property(e => e.Gbrapspos3).HasColumnName("GBRapspos3");
            entity.Property(e => e.Gbsbpos1).HasColumnName("GBSBPos1");
            entity.Property(e => e.Gbsbpos2).HasColumnName("GBSBPos2");
            entity.Property(e => e.Gbsbpos3).HasColumnName("GBSBPos3");
            entity.Property(e => e.Gbspreis).HasColumnName("GBSPreis");
            entity.Property(e => e.GbvalutaBeachten).HasColumnName("GBValutaBeachten");
            entity.Property(e => e.GbvdruckerNr).HasColumnName("GBVDruckerNr");
            entity.Property(e => e.GbvdruckerNr2).HasColumnName("GBVDruckerNr2");
            entity.Property(e => e.Gbvormonat).HasColumnName("GBVormonat");
            entity.Property(e => e.GbwgsSort).HasColumnName("GBWgsSort");
            entity.Property(e => e.GbwgsSortFolge).HasColumnName("GBWgsSortFolge");
            entity.Property(e => e.Gbzgk).HasColumnName("GBZGK");
            entity.Property(e => e.Gbzusatz).HasColumnName("GBZusatz");
            entity.Property(e => e.Gbzzdatum).HasColumnName("GBZZDatum");
            entity.Property(e => e.KartBis).HasColumnName("KArtBis");
            entity.Property(e => e.KartVon).HasColumnName("KArtVon");
            entity.Property(e => e.KasAtext1)
                .HasMaxLength(30)
                .HasColumnName("KasAText1");
            entity.Property(e => e.KasAtext10)
                .HasMaxLength(30)
                .HasColumnName("KasAText10");
            entity.Property(e => e.KasAtext2)
                .HasMaxLength(30)
                .HasColumnName("KasAText2");
            entity.Property(e => e.KasAtext3)
                .HasMaxLength(30)
                .HasColumnName("KasAText3");
            entity.Property(e => e.KasAtext4)
                .HasMaxLength(30)
                .HasColumnName("KasAText4");
            entity.Property(e => e.KasAtext5)
                .HasMaxLength(30)
                .HasColumnName("KasAText5");
            entity.Property(e => e.KasAtext6)
                .HasMaxLength(30)
                .HasColumnName("KasAText6");
            entity.Property(e => e.KasAtext7)
                .HasMaxLength(30)
                .HasColumnName("KasAText7");
            entity.Property(e => e.KasAtext8)
                .HasMaxLength(30)
                .HasColumnName("KasAText8");
            entity.Property(e => e.KasAtext9)
                .HasMaxLength(30)
                .HasColumnName("KasAText9");
            entity.Property(e => e.KasGtext1)
                .HasMaxLength(30)
                .HasColumnName("KasGText1");
            entity.Property(e => e.KasGtext10)
                .HasMaxLength(30)
                .HasColumnName("KasGText10");
            entity.Property(e => e.KasGtext2)
                .HasMaxLength(30)
                .HasColumnName("KasGText2");
            entity.Property(e => e.KasGtext3)
                .HasMaxLength(30)
                .HasColumnName("KasGText3");
            entity.Property(e => e.KasGtext4)
                .HasMaxLength(30)
                .HasColumnName("KasGText4");
            entity.Property(e => e.KasGtext5)
                .HasMaxLength(30)
                .HasColumnName("KasGText5");
            entity.Property(e => e.KasGtext6)
                .HasMaxLength(30)
                .HasColumnName("KasGText6");
            entity.Property(e => e.KasGtext7)
                .HasMaxLength(30)
                .HasColumnName("KasGText7");
            entity.Property(e => e.KasGtext8)
                .HasMaxLength(30)
                .HasColumnName("KasGText8");
            entity.Property(e => e.KasGtext9)
                .HasMaxLength(30)
                .HasColumnName("KasGText9");
            entity.Property(e => e.KasText1).HasMaxLength(255);
            entity.Property(e => e.KasText10).HasMaxLength(255);
            entity.Property(e => e.KasText11).HasMaxLength(255);
            entity.Property(e => e.KasText12).HasMaxLength(255);
            entity.Property(e => e.KasText13).HasMaxLength(255);
            entity.Property(e => e.KasText14).HasMaxLength(255);
            entity.Property(e => e.KasText15).HasMaxLength(255);
            entity.Property(e => e.KasText16).HasMaxLength(255);
            entity.Property(e => e.KasText17).HasMaxLength(255);
            entity.Property(e => e.KasText18).HasMaxLength(255);
            entity.Property(e => e.KasText19).HasMaxLength(255);
            entity.Property(e => e.KasText2).HasMaxLength(255);
            entity.Property(e => e.KasText3).HasMaxLength(255);
            entity.Property(e => e.KasText4).HasMaxLength(255);
            entity.Property(e => e.KasText5).HasMaxLength(255);
            entity.Property(e => e.KasText6).HasMaxLength(255);
            entity.Property(e => e.KasText7).HasMaxLength(255);
            entity.Property(e => e.KasText8).HasMaxLength(255);
            entity.Property(e => e.KasText9).HasMaxLength(255);
            entity.Property(e => e.Kdgdi).HasColumnName("KDGDI");
            entity.Property(e => e.KdlfartikelPrsFdg).HasColumnName("KDLFArtikelPrsFdg");
            entity.Property(e => e.KdruckerNr).HasColumnName("KDruckerNr");
            entity.Property(e => e.Kedit).HasColumnName("KEdit");
            entity.Property(e => e.KindexNr).HasColumnName("KIndexNr");
            entity.Property(e => e.KrabArt).HasColumnName("KRabArt");
            entity.Property(e => e.KtausgangNr).HasColumnName("KTAusgangNr");
            entity.Property(e => e.KtautoDruck).HasColumnName("KTAutoDruck");
            entity.Property(e => e.Ktbetreuer).HasColumnName("KTBetreuer");
            entity.Property(e => e.KtdatumDr).HasColumnName("KTDatumDr");
            entity.Property(e => e.KtdruckAnz1).HasColumnName("KTDruckAnz1");
            entity.Property(e => e.KtdruckAnz2).HasColumnName("KTDruckAnz2");
            entity.Property(e => e.KtdruckerNr).HasColumnName("KTDruckerNr");
            entity.Property(e => e.KtdruckerNr2).HasColumnName("KTDruckerNr2");
            entity.Property(e => e.KteingangNr).HasColumnName("KTEingangNr");
            entity.Property(e => e.Ktf10modus).HasColumnName("KTF10Modus");
            entity.Property(e => e.Ktf1check).HasColumnName("KTF1Check");
            entity.Property(e => e.Ktfracht).HasColumnName("KTFracht");
            entity.Property(e => e.KthändlerNr).HasColumnName("KTHändlerNr");
            entity.Property(e => e.Ktklassifikation).HasColumnName("KTKlassifikation");
            entity.Property(e => e.KtoWashOut1).HasMaxLength(4);
            entity.Property(e => e.KtoWashOut2).HasMaxLength(4);
            entity.Property(e => e.KtoWashOut3).HasMaxLength(4);
            entity.Property(e => e.Ktplusminus).HasColumnName("KTplusminus");
            entity.Property(e => e.Ktrs3ek).HasColumnName("KTRS3EK");
            entity.Property(e => e.Ktrs3vk).HasColumnName("KTRS3VK");
            entity.Property(e => e.Lfgdi).HasColumnName("LFGDI");
            entity.Property(e => e.Lfust1Proz)
                .HasColumnType("money")
                .HasColumnName("LFUst1Proz");
            entity.Property(e => e.Lfust2Proz)
                .HasColumnType("money")
                .HasColumnName("LFUst2Proz");
            entity.Property(e => e.Lfust3Proz)
                .HasColumnType("money")
                .HasColumnName("LFUst3Proz");
            entity.Property(e => e.Lpakt).HasColumnName("LPAkt");
            entity.Property(e => e.RaausdrAnzahl2).HasColumnName("RAAusdrAnzahl2");
            entity.Property(e => e.RaausdrAnzahl3).HasColumnName("RAAusdrAnzahl3");
            entity.Property(e => e.RaausdruckGu)
                .HasMaxLength(8)
                .HasColumnName("RAAusdruckGU");
            entity.Property(e => e.RaausdruckRe)
                .HasMaxLength(8)
                .HasColumnName("RAAusdruckRE");
            entity.Property(e => e.RaautoDruck).HasColumnName("RAAutoDruck");
            entity.Property(e => e.RabelegDruck).HasColumnName("RABelegDruck");
            entity.Property(e => e.Rabemanzeigen).HasColumnName("RABemanzeigen");
            entity.Property(e => e.Rabeztext).HasColumnName("RABEZText");
            entity.Property(e => e.Radatumsabfr).HasColumnName("RADatumsabfr");
            entity.Property(e => e.RadruckerNr2).HasColumnName("RADruckerNr2");
            entity.Property(e => e.RadruckerNr3).HasColumnName("RADruckerNr3");
            entity.Property(e => e.RaeinKontrakt).HasColumnName("RAEinKontrakt");
            entity.Property(e => e.Raekvk).HasColumnName("RAEKVK");
            entity.Property(e => e.RakarenzTage).HasColumnName("RAKarenzTage");
            entity.Property(e => e.Rakontraktabgleich).HasColumnName("RAKontraktabgleich");
            entity.Property(e => e.Ralsabr).HasColumnName("RALSAbr");
            entity.Property(e => e.Ralsdruck).HasColumnName("RALSDruck");
            entity.Property(e => e.Ralswebs).HasColumnName("RALSWEBS");
            entity.Property(e => e.RanettoTage).HasColumnName("RANettoTage");
            entity.Property(e => e.Raskonto1Proz)
                .HasColumnType("money")
                .HasColumnName("RASkonto1Proz");
            entity.Property(e => e.Raskonto1Tage).HasColumnName("RASkonto1Tage");
            entity.Property(e => e.Raskonto2Proz)
                .HasColumnType("money")
                .HasColumnName("RASkonto2Proz");
            entity.Property(e => e.Raskonto2Tage).HasColumnName("RASkonto2Tage");
            entity.Property(e => e.Rasopt).HasColumnName("RASOpt");
            entity.Property(e => e.Rastreckenabfr).HasColumnName("RAStreckenabfr");
            entity.Property(e => e.Ravormonat).HasColumnName("RAVormonat");
            entity.Property(e => e.Ravorschau).HasColumnName("RAVorschau");
            entity.Property(e => e.Ravpckg).HasColumnName("RAVpckg");
            entity.Property(e => e.Rawee).HasColumnName("RAWEE");
            entity.Property(e => e.Razustellart).HasColumnName("RAZustellart");
            entity.Property(e => e.Razzstandard)
                .HasMaxLength(60)
                .HasColumnName("RAZZStandard");
            entity.Property(e => e.RazzvonRd).HasColumnName("RAZZvonRD");
            entity.Property(e => e.ScheckDrn)
                .HasMaxLength(255)
                .HasColumnName("ScheckDRN");
            entity.Property(e => e.StdruckerNr).HasColumnName("STDruckerNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Ubindex).HasColumnName("UBIndex");
            entity.Property(e => e.Wganlieferer).HasColumnName("WGAnlieferer");
            entity.Property(e => e.Wgdruckpar).HasColumnName("WGDruckpar");
            entity.Property(e => e.Wgextrakt).HasColumnName("WGExtrakt");
            entity.Property(e => e.WggewAkzept)
                .HasColumnType("money")
                .HasColumnName("WGGewAkzept");
            entity.Property(e => e.Wgindexpar).HasColumnName("WGIndexpar");
            entity.Property(e => e.Wgktabgl).HasColumnName("WGKTAbgl");
            entity.Property(e => e.Wglagerakt).HasColumnName("WGLagerakt");
            entity.Property(e => e.Wgpos2).HasColumnName("WGPos2");
            entity.Property(e => e.Wgrsm).HasColumnName("WGRSM");
            entity.Property(e => e.Wgrsmanz).HasColumnName("WGRSMAnz");
            entity.Property(e => e.Wgrsmdrucker).HasColumnName("WGRSMDrucker");
            entity.Property(e => e.WgserPort)
                .HasMaxLength(1)
                .HasColumnName("WGSerPort");
            entity.Property(e => e.Wgsiloakt).HasColumnName("WGSiloakt");
            entity.Property(e => e.WgsindexNr).HasColumnName("WGSIndexNr");
            entity.Property(e => e.Wgskp).HasColumnName("WGSKP");
            entity.Property(e => e.Wgstapel).HasColumnName("WGStapel");
            entity.Property(e => e.Wgwaagemanuell).HasColumnName("WGWaagemanuell");
            entity.Property(e => e.Wgwaagenart)
                .HasMaxLength(10)
                .HasColumnName("WGWaagenart");
        });

        modelBuilder.Entity<Optionen2>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Optionen2$ID");

            entity.ToTable("Optionen2");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AbgLsprf).HasColumnName("AbgLSPrf");
            entity.Property(e => e.AlbemRe).HasColumnName("ALBemRE");
            entity.Property(e => e.Albemerkung).HasColumnName("ALBemerkung");
            entity.Property(e => e.AlsbemTxtSchreiben).HasColumnName("ALSBemTxtSchreiben");
            entity.Property(e => e.Alslkwabfrage).HasColumnName("ALSLKWAbfrage");
            entity.Property(e => e.Alsnrsort).HasColumnName("ALSNRSort");
            entity.Property(e => e.AlssuchStart).HasColumnName("ALSSuchStart");
            entity.Property(e => e.AlstextNr).HasColumnName("ALSTextNr");
            entity.Property(e => e.AlszustLoeschen).HasColumnName("ALSZustLoeschen");
            entity.Property(e => e.ArbedAbfr).HasColumnName("ARBedAbfr");
            entity.Property(e => e.Arbemerkung).HasColumnName("ARBemerkung");
            entity.Property(e => e.ArfreigPruef).HasColumnName("ARFreigPruef");
            entity.Property(e => e.Arlfonly).HasColumnName("ARLFOnly");
            entity.Property(e => e.ArspezRabShow).HasColumnName("ARSpezRabShow");
            entity.Property(e => e.Arzweizeilig).HasColumnName("ARZweizeilig");
            entity.Property(e => e.Blsindex).HasColumnName("BLSIndex");
            entity.Property(e => e.Bltagesdatum).HasColumnName("BLTagesdatum");
            entity.Property(e => e.CfCartNr).HasColumnName("CfCArtNr");
            entity.Property(e => e.Eksuche).HasColumnName("EKSuche");
            entity.Property(e => e.Elbemerkung).HasColumnName("ELBemerkung");
            entity.Property(e => e.Eloanbindung).HasColumnName("ELOAnbindung");
            entity.Property(e => e.Eltagesdatum).HasColumnName("ELTagesdatum");
            entity.Property(e => e.Elzustellart).HasColumnName("ELZustellart");
            entity.Property(e => e.EmailAbsender)
                .HasMaxLength(50)
                .HasColumnName("EMailAbsender");
            entity.Property(e => e.EmailAnhang)
                .HasMaxLength(255)
                .HasColumnName("EMailAnhang");
            entity.Property(e => e.EmailAuth).HasColumnName("EMailAuth");
            entity.Property(e => e.EmailLogin)
                .HasMaxLength(50)
                .HasColumnName("EMailLogin");
            entity.Property(e => e.EmailPassw)
                .HasMaxLength(30)
                .HasColumnName("EMailPassw");
            entity.Property(e => e.EmailPort).HasColumnName("EMailPort");
            entity.Property(e => e.EmailSmtp)
                .HasMaxLength(50)
                .HasColumnName("EMailSMTP");
            entity.Property(e => e.Erlfonly).HasColumnName("ERLFOnly");
            entity.Property(e => e.Ertagesdatum).HasColumnName("ERTagesdatum");
            entity.Property(e => e.Fibu2Aab).HasColumnName("Fibu2AAB");
            entity.Property(e => e.Fibu2Ar).HasColumnName("Fibu2AR");
            entity.Property(e => e.Fibu2Er).HasColumnName("Fibu2ER");
            entity.Property(e => e.Fibu2Gb).HasColumnName("Fibu2GB");
            entity.Property(e => e.Fibu2Pfad).HasMaxLength(255);
            entity.Property(e => e.Fibu2Sta).HasColumnName("Fibu2STA");
            entity.Property(e => e.Fibu2Vdatum).HasColumnName("Fibu2VDatum");
            entity.Property(e => e.FibuKstmenge).HasColumnName("FibuKSTMenge");
            entity.Property(e => e.HarryEan).HasColumnName("HarryEAN");
            entity.Property(e => e.HoegemannEmpfArchiv).HasMaxLength(100);
            entity.Property(e => e.HoegemannEmpfPfad).HasMaxLength(100);
            entity.Property(e => e.HoegemannSendArchiv).HasMaxLength(100);
            entity.Property(e => e.HoegemannSendPfad).HasMaxLength(100);
            entity.Property(e => e.KdpreisRe).HasColumnName("KDPreisRE");
            entity.Property(e => e.KrabAbfr).HasColumnName("KRabAbfr");
            entity.Property(e => e.KrabAlgArt).HasColumnName("KRabAlgArt");
            entity.Property(e => e.KtbemTxt).HasColumnName("KTBemTxt");
            entity.Property(e => e.KtbruttoGew).HasColumnName("KTBruttoGew");
            entity.Property(e => e.KtvonAnl).HasColumnName("KTvonAnl");
            entity.Property(e => e.Kundenfrachtsatz).HasColumnType("money");
            entity.Property(e => e.LfanzKonto1).HasColumnName("LFAnzKonto1");
            entity.Property(e => e.LfanzKonto2).HasColumnName("LFAnzKonto2");
            entity.Property(e => e.LfanzKontoDrittLand).HasColumnName("LFAnzKontoDrittLand");
            entity.Property(e => e.LfanzKontoEg).HasColumnName("LFAnzKontoEG");
            entity.Property(e => e.LogFile).HasMaxLength(255);
            entity.Property(e => e.LsfremdNr).HasColumnName("LSFremdNr");
            entity.Property(e => e.MapFile).HasMaxLength(255);
            entity.Property(e => e.MappPassw).HasMaxLength(50);
            entity.Property(e => e.MappUser).HasMaxLength(50);
            entity.Property(e => e.MitMhd).HasColumnName("MitMHD");
            entity.Property(e => e.Mstrecke).HasColumnName("MStrecke");
            entity.Property(e => e.NeuGbv).HasColumnName("NeuGBV");
            entity.Property(e => e.Nvobilanz).HasColumnName("NVOBilanz");
            entity.Property(e => e.Nvowert)
                .HasColumnType("money")
                .HasColumnName("NVOWert");
            entity.Property(e => e.NvowertVorLieferant)
                .HasColumnType("money")
                .HasColumnName("NVOWertVorLieferant");
            entity.Property(e => e.OrdersPfad).HasMaxLength(100);
            entity.Property(e => e.OrdersPfadBak)
                .HasMaxLength(100)
                .HasColumnName("OrdersPfadBAK");
            entity.Property(e => e.Reweswitch).HasColumnName("REWESwitch");
            entity.Property(e => e.StatusLsdrucker1).HasColumnName("StatusLSDrucker1");
            entity.Property(e => e.StatusLsdrucker2).HasColumnName("StatusLSDrucker2");
            entity.Property(e => e.StatusLsdrucker3).HasColumnName("StatusLSDrucker3");
            entity.Property(e => e.StatusLsdrucker4).HasColumnName("StatusLSDrucker4");
            entity.Property(e => e.StatusLsdrucker5).HasColumnName("StatusLSDrucker5");
            entity.Property(e => e.StatusLsdrucker6).HasColumnName("StatusLSDrucker6");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VdatumOk).HasColumnName("VDatumOK");
            entity.Property(e => e.Version).HasMaxLength(20);
            entity.Property(e => e.Vksuche).HasColumnName("VKSuche");
            entity.Property(e => e.WgsbemAnz).HasColumnName("WGSBemAnz");
            entity.Property(e => e.Wgssilo).HasColumnName("WGSSilo");
            entity.Property(e => e.ZusTxtBez1).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez2).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez3).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez4).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez5).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez6).HasMaxLength(30);
            entity.Property(e => e.Zzwere).HasColumnName("ZZWERE");
        });

        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Orders$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AuftrNr).HasDefaultValue(0L);
            entity.Property(e => e.BestNr).HasDefaultValue(0L);
            entity.Property(e => e.OrdersBest).HasDefaultValue(false);
            entity.Property(e => e.Preis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });

        modelBuilder.Entity<Preislisten>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Preislisten$ID");

            entity.ToTable("Preislisten");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.PrlartBez)
                .HasMaxLength(30)
                .HasColumnName("PRLArtBez");
            entity.Property(e => e.PrlartBez2)
                .HasMaxLength(30)
                .HasColumnName("PRLArtBez2");
            entity.Property(e => e.PrlartNr).HasColumnName("PRLArtNr");
            entity.Property(e => e.PrlartPreis)
                .HasColumnType("money")
                .HasColumnName("PRLArtPreis");
            entity.Property(e => e.PrlartProz)
                .HasColumnType("money")
                .HasColumnName("PRLArtProz");
            entity.Property(e => e.Prlbzg)
                .HasMaxLength(10)
                .HasColumnName("PRLBzg");
            entity.Property(e => e.Prldatum1)
                .HasColumnType("datetime")
                .HasColumnName("PRLDatum1");
            entity.Property(e => e.Prldatum2)
                .HasColumnType("datetime")
                .HasColumnName("PRLDatum2");
            entity.Property(e => e.Prlekpreis)
                .HasColumnType("money")
                .HasColumnName("PRLEKPreis");
            entity.Property(e => e.Prlfracht)
                .HasColumnType("money")
                .HasColumnName("PRLFracht");
            entity.Property(e => e.Prlkng).HasColumnName("PRLKng");
            entity.Property(e => e.Prlnr).HasColumnName("PRLNr");
            entity.Property(e => e.Prlpos).HasColumnName("PRLPos");
            entity.Property(e => e.Prlpreis1)
                .HasColumnType("money")
                .HasColumnName("PRLPreis1");
            entity.Property(e => e.Prlpreis2)
                .HasColumnType("money")
                .HasColumnName("PRLPreis2");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Prlkopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PRLKopf$ID");

            entity.ToTable("PRLKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Kategorie).HasDefaultValue(0);
            entity.Property(e => e.Lfdatum).HasColumnName("LFDatum");
            entity.Property(e => e.Prlart).HasColumnName("PRLArt");
            entity.Property(e => e.Prldatum)
                .HasColumnType("datetime")
                .HasColumnName("PRLDatum");
            entity.Property(e => e.Prlexcl)
                .HasDefaultValue(false)
                .HasColumnName("PRLExcl");
            entity.Property(e => e.Prlnummer).HasColumnName("PRLNummer");
            entity.Property(e => e.Prlsbg)
                .HasMaxLength(10)
                .HasColumnName("PRLSBG");
            entity.Property(e => e.Prltext1)
                .HasMaxLength(30)
                .HasColumnName("PRLText1");
            entity.Property(e => e.Prltext2)
                .HasMaxLength(30)
                .HasColumnName("PRLText2");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<ProdSpez>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ProdSpez$ID");

            entity.ToTable("ProdSpez");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Allergen).HasMaxLength(255);
            entity.Property(e => e.ArtBeschr).HasMaxLength(255);
            entity.Property(e => e.Bezeichn).HasMaxLength(50);
            entity.Property(e => e.Farbe).HasMaxLength(100);
            entity.Property(e => e.Gdatum)
                .HasColumnType("datetime")
                .HasColumnName("GDatum");
            entity.Property(e => e.Geruch).HasMaxLength(100);
            entity.Property(e => e.Geschmack).HasMaxLength(100);
            entity.Property(e => e.Gvo)
                .HasMaxLength(255)
                .HasColumnName("GVO");
            entity.Property(e => e.Mhd)
                .HasMaxLength(15)
                .HasColumnName("MHD");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.TranspBed).HasMaxLength(50);
            entity.Property(e => e.VerpArt).HasMaxLength(50);
            entity.Property(e => e.ZollNr).HasMaxLength(15);
            entity.Property(e => e.ZusStoff).HasMaxLength(50);
        });

        modelBuilder.Entity<ProdSpezAnalyse>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ProdSpezAnalyse$ID");

            entity.ToTable("ProdSpezAnalyse");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Einheit).HasMaxLength(10);
            entity.Property(e => e.Max).HasMaxLength(10);
            entity.Property(e => e.Methode).HasMaxLength(50);
            entity.Property(e => e.Min).HasMaxLength(10);
            entity.Property(e => e.Parameter).HasMaxLength(50);
        });

        modelBuilder.Entity<ProdSpezNaehrwert>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ProdSpezNaehrwert$ID");

            entity.ToTable("ProdSpezNaehrwert");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Dwert)
                .HasMaxLength(10)
                .HasColumnName("DWert");
            entity.Property(e => e.Einheit).HasMaxLength(10);
            entity.Property(e => e.Parameter).HasMaxLength(50);
        });

        modelBuilder.Entity<RabTab>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("RabTab$ID");

            entity.ToTable("RabTab");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.GesamtRab).HasDefaultValue(false);
            entity.Property(e => e.RabEur).HasColumnType("money");
            entity.Property(e => e.RabGes).HasColumnType("money");
            entity.Property(e => e.RabPm)
                .HasMaxLength(1)
                .HasColumnName("RabPM");
            entity.Property(e => e.RabProz).HasColumnType("money");
            entity.Property(e => e.RabText).HasMaxLength(30);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Rgbvadr>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("RGBVADR$ID");

            entity.ToTable("RGBVADR");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AnzBrutto).HasColumnType("money");
            entity.Property(e => e.AnzNetto).HasColumnType("money");
            entity.Property(e => e.AnzRestOffen).HasColumnType("money");
            entity.Property(e => e.AnzStartBrutto).HasColumnType("money");
            entity.Property(e => e.AnzStartNetto).HasColumnType("money");
            entity.Property(e => e.BioNr).HasMaxLength(50);
            entity.Property(e => e.Gbabfall)
                .HasColumnType("money")
                .HasColumnName("GBAbfall");
            entity.Property(e => e.GbabrGew)
                .HasColumnType("money")
                .HasColumnName("GBAbrGew");
            entity.Property(e => e.GbabzgBetr1)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr1");
            entity.Property(e => e.GbabzgBetr2)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr2");
            entity.Property(e => e.GbabzgBetr3)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr3");
            entity.Property(e => e.GbabzgBetr4)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr4");
            entity.Property(e => e.GbabzgBetr5)
                .HasColumnType("money")
                .HasColumnName("GBAbzgBetr5");
            entity.Property(e => e.GbabzgText1)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText1");
            entity.Property(e => e.GbabzgText2)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText2");
            entity.Property(e => e.GbabzgText3)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText3");
            entity.Property(e => e.GbabzgText4)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText4");
            entity.Property(e => e.GbabzgText5)
                .HasMaxLength(30)
                .HasColumnName("GBAbzgText5");
            entity.Property(e => e.GbabzgVz1)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ1");
            entity.Property(e => e.GbabzgVz2)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ2");
            entity.Property(e => e.GbabzgVz3)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ3");
            entity.Property(e => e.GbabzgVz4)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ4");
            entity.Property(e => e.GbabzgVz5)
                .HasMaxLength(1)
                .HasColumnName("GBAbzgVZ5");
            entity.Property(e => e.Gbabzug)
                .HasColumnType("money")
                .HasColumnName("GBAbzug");
            entity.Property(e => e.Gbaltnr).HasColumnName("GBAltnr");
            entity.Property(e => e.GbangAbfall)
                .HasColumnType("money")
                .HasColumnName("GBAngAbfall");
            entity.Property(e => e.GbanlGew)
                .HasColumnType("money")
                .HasColumnName("GBAnlGew");
            entity.Property(e => e.Gbanlfnr).HasColumnName("GBANLFNR");
            entity.Property(e => e.Gbanrede)
                .HasMaxLength(30)
                .HasColumnName("GBAnrede");
            entity.Property(e => e.Gbart).HasColumnName("GBArt");
            entity.Property(e => e.Gbasld).HasColumnName("GBASLD");
            entity.Property(e => e.Gbbank)
                .HasMaxLength(30)
                .HasColumnName("GBBank");
            entity.Property(e => e.Gbbld)
                .HasMaxLength(30)
                .HasColumnName("GBBLD");
            entity.Property(e => e.Gbbldkng).HasColumnName("GBBLDKng");
            entity.Property(e => e.Gbblz)
                .HasMaxLength(10)
                .HasColumnName("GBBLZ");
            entity.Property(e => e.Gbbrutto)
                .HasColumnType("money")
                .HasColumnName("GBBrutto");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.GbdruckKz)
                .HasMaxLength(1)
                .HasColumnName("GBDruckKz");
            entity.Property(e => e.GbentfKl)
                .HasMaxLength(2)
                .HasColumnName("GBEntfKL");
            entity.Property(e => e.GbentfKlzuEur)
                .HasColumnType("money")
                .HasColumnName("GBEntfKLZuEUR");
            entity.Property(e => e.Gbezg)
                .HasDefaultValue(false)
                .HasColumnName("GBEZG");
            entity.Property(e => e.Gbezgbetr)
                .HasColumnType("money")
                .HasColumnName("GBEZGBetr");
            entity.Property(e => e.Gbfax)
                .HasMaxLength(20)
                .HasColumnName("GBFax");
            entity.Property(e => e.Gbfibu2)
                .HasDefaultValue(false)
                .HasColumnName("GBFibu2");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.GbgesMenge)
                .HasColumnType("money")
                .HasColumnName("GBGesMenge");
            entity.Property(e => e.Gbhaendler)
                .HasDefaultValue(false)
                .HasColumnName("GBHaendler");
            entity.Property(e => e.Gbiban)
                .HasMaxLength(30)
                .HasColumnName("GBIBAN");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(20)
                .HasColumnName("GBKonto");
            entity.Property(e => e.Gbktnr).HasColumnName("GBKTNr");
            entity.Property(e => e.GbktrNrLf)
                .HasMaxLength(10)
                .HasColumnName("GBKtrNrLF");
            entity.Property(e => e.Gbland)
                .HasMaxLength(30)
                .HasColumnName("GBLand");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblfstatus)
                .HasMaxLength(15)
                .HasColumnName("GBLFStatus");
            entity.Property(e => e.GbmitText1)
                .HasMaxLength(60)
                .HasColumnName("GBMitText1");
            entity.Property(e => e.GbmitText2)
                .HasMaxLength(60)
                .HasColumnName("GBMitText2");
            entity.Property(e => e.Gbmwst)
                .HasColumnType("money")
                .HasColumnName("GBMwst");
            entity.Property(e => e.Gbnachzhlg)
                .HasMaxLength(1)
                .HasColumnName("GBNachzhlg");
            entity.Property(e => e.Gbname1)
                .HasMaxLength(30)
                .HasColumnName("GBName1");
            entity.Property(e => e.Gbname2)
                .HasMaxLength(30)
                .HasColumnName("GBName2");
            entity.Property(e => e.Gbname3)
                .HasMaxLength(30)
                .HasColumnName("GBName3");
            entity.Property(e => e.Gbnetto)
                .HasColumnType("money")
                .HasColumnName("GBNetto");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.Gbort)
                .HasMaxLength(30)
                .HasColumnName("GBOrt");
            entity.Property(e => e.Gbplz)
                .HasMaxLength(10)
                .HasColumnName("GBPLZ");
            entity.Property(e => e.GbreNrLf)
                .HasMaxLength(25)
                .HasColumnName("GBReNrLF");
            entity.Property(e => e.Gbsbg)
                .HasMaxLength(10)
                .HasColumnName("GBSBG");
            entity.Property(e => e.GbstNr)
                .HasMaxLength(20)
                .HasColumnName("GBStNr");
            entity.Property(e => e.GbstProz)
                .HasColumnType("money")
                .HasColumnName("GBStProz");
            entity.Property(e => e.GbstProz2).HasColumnName("GBStProz2");
            entity.Property(e => e.GbstSchl).HasColumnName("GBStSchl");
            entity.Property(e => e.Gbstorno)
                .HasMaxLength(1)
                .HasColumnName("GBStorno");
            entity.Property(e => e.Gbstrasse)
                .HasMaxLength(30)
                .HasColumnName("GBStrasse");
            entity.Property(e => e.Gbswift)
                .HasMaxLength(30)
                .HasColumnName("GBSWIFT");
            entity.Property(e => e.Gbtelefon)
                .HasMaxLength(20)
                .HasColumnName("GBTelefon");
            entity.Property(e => e.Gbustidnr)
                .HasMaxLength(18)
                .HasColumnName("GBUSTIDNR");
            entity.Property(e => e.Gbvaluta)
                .HasMaxLength(60)
                .HasColumnName("GBValuta");
            entity.Property(e => e.GbvalutaDatum)
                .HasColumnType("datetime")
                .HasColumnName("GBValutaDatum");
            entity.Property(e => e.Gbvwhrg)
                .HasMaxLength(4)
                .HasColumnName("GBVWhrg");
            entity.Property(e => e.GbvwhrgKurs)
                .HasColumnType("money")
                .HasColumnName("GBVWhrgKurs");
            entity.Property(e => e.GbzahlBetrag)
                .HasColumnType("money")
                .HasColumnName("GBZahlBetrag");
            entity.Property(e => e.GbzuschlEur)
                .HasColumnType("money")
                .HasColumnName("GBZuschlEUR");
            entity.Property(e => e.GbzuschlSumme)
                .HasColumnType("money")
                .HasColumnName("GBZuschlSumme");
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Vdatum).HasColumnType("datetime");
        });

        modelBuilder.Entity<Rgbvhpt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("RGBVHPT$ID");

            entity.ToTable("RGBVHPT");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BuchMenge).HasColumnType("money");
            entity.Property(e => e.GbartNr).HasColumnName("GBArtNr");
            entity.Property(e => e.Gbdatum)
                .HasColumnType("datetime")
                .HasColumnName("GBDatum");
            entity.Property(e => e.Gbep)
                .HasColumnType("money")
                .HasColumnName("GBEP");
            entity.Property(e => e.Gbfiliale)
                .HasMaxLength(2)
                .HasColumnName("GBFiliale");
            entity.Property(e => e.Gbfracht)
                .HasColumnType("money")
                .HasColumnName("GBFracht");
            entity.Property(e => e.GbgesNetto)
                .HasColumnType("money")
                .HasColumnName("GBGesNetto");
            entity.Property(e => e.GbgetrArt)
                .HasMaxLength(70)
                .HasColumnName("GBGetrArt");
            entity.Property(e => e.GbgetrKng)
                .HasMaxLength(3)
                .HasColumnName("GBGetrKng");
            entity.Property(e => e.GbgridPos).HasColumnName("GBGridPos");
            entity.Property(e => e.Gbhartikel).HasColumnName("GBHArtikel");
            entity.Property(e => e.Gbkonto)
                .HasMaxLength(4)
                .HasColumnName("GBKonto");
            entity.Property(e => e.GbkontrNr).HasColumnName("GBKontrNr");
            entity.Property(e => e.Gbkz)
                .HasMaxLength(10)
                .HasColumnName("GBKz");
            entity.Property(e => e.Gblfnr).HasColumnName("GBLFNr");
            entity.Property(e => e.Gblkw)
                .HasMaxLength(10)
                .HasColumnName("GBLKW");
            entity.Property(e => e.Gbnr).HasColumnName("GBNr");
            entity.Property(e => e.GbstProz).HasColumnName("GBStProz");
            entity.Property(e => e.GbstreckNr)
                .HasMaxLength(12)
                .HasColumnName("GBStreckNr");
            entity.Property(e => e.Gbvznr)
                .HasMaxLength(10)
                .HasColumnName("GBVZNr");
            entity.Property(e => e.GbwgsNr).HasColumnName("GBWgsNr");
            entity.Property(e => e.Lkw).HasColumnName("LKW");
            entity.Property(e => e.MaabrDatum)
                .HasColumnType("datetime")
                .HasColumnName("MAAbrDatum");
            entity.Property(e => e.Nvo).HasColumnName("NVO");
            entity.Property(e => e.Tp).HasColumnName("TP");
            entity.Property(e => e.WgStrekNr).HasMaxLength(12);
        });

        modelBuilder.Entity<Satlog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SATLog$ID");

            entity.ToTable("SATLog");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AktStatus).HasMaxLength(16);
            entity.Property(e => e.AuftrNr).HasDefaultValue(0L);
            entity.Property(e => e.LastModify).HasColumnType("datetime");
            entity.Property(e => e.MandantId)
                .HasMaxLength(2)
                .HasColumnName("MandantID");
            entity.Property(e => e.ModifiedBy).HasMaxLength(255);
            entity.Property(e => e.RootObjId)
                .HasDefaultValue(0L)
                .HasColumnName("RootObjID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });

        modelBuilder.Entity<SiloAutomatik>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloAutomatik$ID");

            entity.ToTable("SiloAutomatik");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bezeichnung).HasMaxLength(20);
            entity.Property(e => e.GrKann)
                .HasColumnType("money")
                .HasColumnName("gr_kann");
            entity.Property(e => e.GrMuss)
                .HasColumnType("money")
                .HasColumnName("gr_muss");
            entity.Property(e => e.IdealWert).HasColumnType("money");
            entity.Property(e => e.KlKann)
                .HasColumnType("money")
                .HasColumnName("kl_kann");
            entity.Property(e => e.KlMuss)
                .HasColumnType("money")
                .HasColumnName("kl_muss");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });

        modelBuilder.Entity<SiloFach>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloFach$ID");

            entity.ToTable("SiloFach");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Gesperrt).HasColumnName("gesperrt");
            entity.Property(e => e.Hlgewicht)
                .HasColumnType("money")
                .HasColumnName("HLGewicht");
            entity.Property(e => e.ProfilArtNr).HasMaxLength(100);
            entity.Property(e => e.ProfilText).HasMaxLength(100);
            entity.Property(e => e.Reinigung).HasMaxLength(50);
            entity.Property(e => e.SiloBez).HasMaxLength(100);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<SiloLab>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloLAB$ID");

            entity.ToTable("SiloLAB");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.WgslabWert0)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert0");
            entity.Property(e => e.WgslabWert1)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert1");
            entity.Property(e => e.WgslabWert10)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert10");
            entity.Property(e => e.WgslabWert11)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert11");
            entity.Property(e => e.WgslabWert12)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert12");
            entity.Property(e => e.WgslabWert2)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert2");
            entity.Property(e => e.WgslabWert3)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert3");
            entity.Property(e => e.WgslabWert4)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert4");
            entity.Property(e => e.WgslabWert5)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert5");
            entity.Property(e => e.WgslabWert6)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert6");
            entity.Property(e => e.WgslabWert7)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert7");
            entity.Property(e => e.WgslabWert8)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert8");
            entity.Property(e => e.WgslabWert9)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert9");
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        });

        modelBuilder.Entity<SiloProfil>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloProfil$ID");

            entity.ToTable("SiloProfil");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bezeichnung).HasMaxLength(50);
        });

        modelBuilder.Entity<SiloRezept>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloRezept$ID");

            entity.ToTable("SiloRezept");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bezeichnung).HasMaxLength(30);
        });

        modelBuilder.Entity<SiloUmb>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloUmb$ID");

            entity.ToTable("SiloUMB");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlibiNr)
                .HasMaxLength(15)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.ArtikelBez1).HasMaxLength(30);
            entity.Property(e => e.Bediener).HasMaxLength(30);
            entity.Property(e => e.ChargNr).HasMaxLength(20);
            entity.Property(e => e.JobDatum).HasColumnType("datetime");
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Menge1).HasColumnType("money");
            entity.Property(e => e.Menge2).HasColumnType("money");
            entity.Property(e => e.Menge3).HasColumnType("money");
            entity.Property(e => e.Menge4).HasColumnType("money");
            entity.Property(e => e.Menge5).HasColumnType("money");
            entity.Property(e => e.Menge6).HasColumnType("money");
            entity.Property(e => e.Menge7).HasColumnType("money");
            entity.Property(e => e.Menge8).HasColumnType("money");
            entity.Property(e => e.MengeNetto).HasColumnType("money");
            entity.Property(e => e.Proz1).HasColumnType("money");
            entity.Property(e => e.Proz2).HasColumnType("money");
            entity.Property(e => e.Proz3).HasColumnType("money");
            entity.Property(e => e.Proz4).HasColumnType("money");
            entity.Property(e => e.Proz5).HasColumnType("money");
            entity.Property(e => e.Proz6).HasColumnType("money");
            entity.Property(e => e.Proz7).HasColumnType("money");
            entity.Property(e => e.Proz8).HasColumnType("money");
            entity.Property(e => e.Rezept).HasMaxLength(50);
            entity.Property(e => e.Rmnr).HasColumnName("RMNr");
            entity.Property(e => e.SiloBez).HasMaxLength(100);
            entity.Property(e => e.Sorte).HasMaxLength(30);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.WiegeDatum).HasColumnType("datetime");
            entity.Property(e => e.WiegeZeit).HasColumnType("datetime");
            entity.Property(e => e.Zmenge1)
                .HasColumnType("money")
                .HasColumnName("ZMenge1");
            entity.Property(e => e.Zmenge2)
                .HasColumnType("money")
                .HasColumnName("ZMenge2");
            entity.Property(e => e.Zmenge3)
                .HasColumnType("money")
                .HasColumnName("ZMenge3");
            entity.Property(e => e.Zproz1)
                .HasColumnType("money")
                .HasColumnName("ZProz1");
            entity.Property(e => e.Zproz2)
                .HasColumnType("money")
                .HasColumnName("ZProz2");
            entity.Property(e => e.Zproz3)
                .HasColumnType("money")
                .HasColumnName("ZProz3");
            entity.Property(e => e.Zsilo1).HasColumnName("ZSilo1");
            entity.Property(e => e.Zsilo2).HasColumnName("ZSilo2");
            entity.Property(e => e.Zsilo3).HasColumnName("ZSilo3");
        });

        modelBuilder.Entity<SiloVorgAlt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloVorgAlt$ID");

            entity.ToTable("SiloVorgAlt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtikelNr).HasColumnName("ArtikelNR");
            entity.Property(e => e.Bemerkung).HasMaxLength(100);
            entity.Property(e => e.Benutzer).HasMaxLength(100);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.DplfdNr).HasColumnName("DPlfdNr");
            entity.Property(e => e.ProfilBez).HasMaxLength(255);
            entity.Property(e => e.Rmnummer).HasColumnName("RMnummer");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(100);
            entity.Property(e => e.VgchargeNr)
                .HasMaxLength(20)
                .HasColumnName("VGCHargeNr");
            entity.Property(e => e.VgscheinNr).HasColumnName("VGScheinNr");
        });

        modelBuilder.Entity<SiloVorgang>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloVorgang$ID");

            entity.ToTable("SiloVorgang");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtikelNr).HasColumnName("ArtikelNR");
            entity.Property(e => e.Bemerkung).HasMaxLength(100);
            entity.Property(e => e.Benutzer).HasMaxLength(100);
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.DplfdNr).HasColumnName("DPlfdNr");
            entity.Property(e => e.ProfilBez).HasMaxLength(255);
            entity.Property(e => e.Rmnummer).HasColumnName("RMnummer");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VgArt).HasMaxLength(100);
            entity.Property(e => e.VgchargeNr)
                .HasMaxLength(20)
                .HasColumnName("VGCHargeNr");
            entity.Property(e => e.VgscheinNr).HasColumnName("VGScheinNr");
        });

        modelBuilder.Entity<SiloWg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloWGS$ID");

            entity.ToTable("SiloWGS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ChargNr).HasMaxLength(15);
            entity.Property(e => e.Keng).HasMaxLength(10);
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        });

        modelBuilder.Entity<SiloWgsent>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SiloWGSEnt$ID");

            entity.ToTable("SiloWGSEnt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ChargNr).HasMaxLength(15);
            entity.Property(e => e.Keng).HasMaxLength(10);
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        });

        modelBuilder.Entity<Spezialrabatte>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Spezialrabatte$ID");

            entity.ToTable("Spezialrabatte");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Festbetrag).HasDefaultValue(false);
            entity.Property(e => e.PlusMinus).HasMaxLength(1);
            entity.Property(e => e.ProzentEuro).HasMaxLength(1);
            entity.Property(e => e.Rabatt).HasColumnType("money");
            entity.Property(e => e.Rabattbezeichnung).HasMaxLength(50);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.ZeitBis).HasColumnType("datetime");
            entity.Property(e => e.ZeitVon).HasColumnType("datetime");
        });

        modelBuilder.Entity<TabIndex>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TabIndex$ID");

            entity.ToTable("TabIndex");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AktAgbvnr).HasColumnName("AktAGBVNr");
            entity.Property(e => e.AktAlsnr)
                .HasDefaultValue(0L)
                .HasColumnName("AktALSNr");
            entity.Property(e => e.AktArgbvnr).HasColumnName("AktARGBVNr");
            entity.Property(e => e.AktArnr)
                .HasDefaultValue(0L)
                .HasColumnName("AktARNr");
            entity.Property(e => e.AktAwgsnr).HasColumnName("AktAWGSNr");
            entity.Property(e => e.AktBstrNr).HasColumnName("AktBStrNr");
            entity.Property(e => e.AktElsnr).HasColumnName("AktELSNr");
            entity.Property(e => e.AktErnr).HasColumnName("AktERNr");
            entity.Property(e => e.AktEstrNr).HasColumnName("AktEStrNr");
            entity.Property(e => e.AktGbvnr).HasColumnName("AktGBVNr");
            entity.Property(e => e.AktKdnr).HasColumnName("AktKDNr");
            entity.Property(e => e.AktLabNr).HasDefaultValue(0L);
            entity.Property(e => e.AktLfnr).HasColumnName("AktLFNr");
            entity.Property(e => e.AktMstrNr).HasColumnName("AktMStrNr");
            entity.Property(e => e.AktPktrNr).HasColumnName("AktPKtrNr");
            entity.Property(e => e.AktRgbvnr).HasColumnName("AktRGBVNr");
            entity.Property(e => e.AktVtnr).HasColumnName("AktVTNr");
            entity.Property(e => e.AktWgsnr).HasColumnName("AktWGSNr");
            entity.Property(e => e.Eloanbindung).HasColumnName("ELOAnbindung");
            entity.Property(e => e.Elodatenpfad)
                .HasMaxLength(250)
                .HasColumnName("ELODatenpfad");
            entity.Property(e => e.ElodatenpfadAr)
                .HasMaxLength(250)
                .HasColumnName("ELODatenpfadAR");
            entity.Property(e => e.ElodatenpfadLs)
                .HasMaxLength(250)
                .HasColumnName("ELODatenpfadLS");
            entity.Property(e => e.Eloformat).HasColumnName("ELOFormat");
            entity.Property(e => e.Gdanbindung).HasColumnName("GDAnbindung");
            entity.Property(e => e.GdartUeb).HasColumnName("GDArtUeb");
            entity.Property(e => e.Gddatenpfad)
                .HasMaxLength(250)
                .HasColumnName("GDDatenpfad");
            entity.Property(e => e.Gddb)
                .HasMaxLength(10)
                .HasColumnName("GDDB");
            entity.Property(e => e.GdkdNr).HasColumnName("GDKdNr");
            entity.Property(e => e.Gdlfnr).HasColumnName("GDLFNr");
            entity.Property(e => e.Gdpasswort)
                .HasMaxLength(20)
                .HasColumnName("GDPasswort");
            entity.Property(e => e.Gduser)
                .HasMaxLength(50)
                .HasColumnName("GDUser");
            entity.Property(e => e.Version).HasMaxLength(20);
        });

        modelBuilder.Entity<Tabellenkopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Tabellenkopf$ID");

            entity.ToTable("Tabellenkopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.TbabrWeg).HasColumnName("TBAbrWeg");
            entity.Property(e => e.Tbart)
                .HasMaxLength(2)
                .HasColumnName("TBArt");
            entity.Property(e => e.TbbruNet).HasColumnName("TBBruNet");
            entity.Property(e => e.Tbkenner).HasColumnName("TBKenner");
            entity.Property(e => e.Tbnummer)
                .HasMaxLength(6)
                .HasColumnName("TBNummer");
            entity.Property(e => e.Tboption)
                .HasMaxLength(50)
                .HasColumnName("TBOption");
            entity.Property(e => e.Tbstatus)
                .HasMaxLength(1)
                .HasColumnName("TBStatus");
            entity.Property(e => e.Tbtext)
                .HasMaxLength(30)
                .HasColumnName("TBText");
        });

        modelBuilder.Entity<TblGutschein>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TblGutschein$ID");

            entity.ToTable("TblGutschein");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtNr).HasDefaultValue(0L);
            entity.Property(e => e.Gsnr)
                .HasDefaultValue(0L)
                .HasColumnName("GSnr");
            entity.Property(e => e.Preis)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(255);
        });
        
        modelBuilder.Entity<TriggerLog>(entity =>
        {
            entity.HasKey(e => e.LogID).HasName("TriggerLog$LogID");

            entity.ToTable("TriggerLog");

            entity.Property(e => e.LogID).HasColumnName("LogID");
            entity.Property(e => e.TriggerName).HasMaxLength(255);
            entity.Property(e => e.Action).HasMaxLength(50);
            entity.Property(e => e.TableAffected).HasMaxLength(100);
            entity.Property(e => e.Timestamp).HasColumnType("datetime");
            entity.Property(e => e.EntryID).HasColumnName("EntryID");
            entity.Property(e => e.Done).HasColumnName("Done");
        });

        modelBuilder.Entity<TseDatum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TseData$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BonBegin).HasColumnType("datetime");
            entity.Property(e => e.BonEnd).HasColumnType("datetime");
            entity.Property(e => e.Kvnr).HasColumnName("KVNr");
            entity.Property(e => e.ProcessData).HasMaxLength(256);
            entity.Property(e => e.ProcessTyp).HasMaxLength(100);
            entity.Property(e => e.Signature).HasMaxLength(250);
            entity.Property(e => e.TseDevId).HasColumnName("TseDevID");
        });

        modelBuilder.Entity<TseDevice>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TseDevices$ID");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.PublicKey).HasMaxLength(200);
            entity.Property(e => e.SignatureAlgorithm).HasMaxLength(50);
            entity.Property(e => e.TimeFormat).HasMaxLength(50);
            entity.Property(e => e.TseDevId).HasColumnName("TseDevID");
        });

        modelBuilder.Entity<TseTransakt>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TSE_Transakt$ID");

            entity.ToTable("TSE_Transakt");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BonId)
                .HasMaxLength(40)
                .HasColumnName("BON_ID");
            entity.Property(e => e.BonNr).HasColumnName("BON_Nr");
            entity.Property(e => e.KabschlDatum)
                .HasColumnType("datetime")
                .HasColumnName("KAbschlDatum");
            entity.Property(e => e.KabschlNr).HasColumnName("KAbschlNr");
            entity.Property(e => e.KasseId).HasColumnName("KasseID");
            entity.Property(e => e.TseEnde)
                .HasMaxLength(30)
                .HasColumnName("TSE_Ende");
            entity.Property(e => e.TseFehler)
                .HasMaxLength(30)
                .HasColumnName("TSE_Fehler");
            entity.Property(e => e.TseId).HasColumnName("TSE_ID");
            entity.Property(e => e.TseSig)
                .HasMaxLength(30)
                .HasColumnName("TSE_SIG");
            entity.Property(e => e.TseSigz).HasColumnName("TSE_SIGZ");
            entity.Property(e => e.TseStart)
                .HasMaxLength(30)
                .HasColumnName("TSE_Start");
            entity.Property(e => e.TseTanr).HasColumnName("TSE_TANR");
            entity.Property(e => e.TseVorgArt)
                .HasMaxLength(30)
                .HasColumnName("TSE_VorgArt");
            entity.Property(e => e.TseVorgDaten)
                .HasMaxLength(30)
                .HasColumnName("TSE_VorgDaten");
        });

        modelBuilder.Entity<UmbListe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UmbListe$ID");

            entity.ToTable("UmbListe");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.UbartGrp).HasColumnName("UBArtGrp");
            entity.Property(e => e.UbartNr).HasColumnName("UBArtNr");
            entity.Property(e => e.Ubbez)
                .HasMaxLength(50)
                .HasColumnName("UBBez");
            entity.Property(e => e.UbbzgGr)
                .HasMaxLength(10)
                .HasColumnName("UBBzgGr");
            entity.Property(e => e.UbchgNr)
                .HasMaxLength(15)
                .HasColumnName("UBChgNr");
            entity.Property(e => e.Ubdatum)
                .HasColumnType("datetime")
                .HasColumnName("UBDatum");
            entity.Property(e => e.Ubek)
                .HasColumnType("money")
                .HasColumnName("UBEK");
            entity.Property(e => e.Ubfakt).HasColumnName("UBFakt");
            entity.Property(e => e.Ubfaktor).HasColumnName("UBFaktor");
            entity.Property(e => e.Ubgrund)
                .HasMaxLength(50)
                .HasColumnName("UBGrund");
            entity.Property(e => e.Ublgst).HasColumnName("UBLgst");
            entity.Property(e => e.Ubmenge)
                .HasColumnType("money")
                .HasColumnName("UBMenge");
            entity.Property(e => e.Ubmkng)
                .HasMaxLength(4)
                .HasColumnName("UBMKng");
            entity.Property(e => e.Ubnr).HasColumnName("UBNr");
            entity.Property(e => e.Ubpm)
                .HasMaxLength(1)
                .HasColumnName("UBPM");
            entity.Property(e => e.UbposNr).HasColumnName("UBPosNr");
            entity.Property(e => e.UbprodNr)
                .HasMaxLength(15)
                .HasColumnName("UBProdNr");
            entity.Property(e => e.Ubwbez1)
                .HasMaxLength(50)
                .HasColumnName("UBWbez1");
            entity.Property(e => e.Ubwbez2)
                .HasMaxLength(50)
                .HasColumnName("UBWbez2");
        });

        modelBuilder.Entity<Umbkopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UMBKopf$ID");

            entity.ToTable("UMBKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Ubbez)
                .HasMaxLength(50)
                .HasColumnName("UBBez");
            entity.Property(e => e.Ubdatum)
                .HasColumnType("datetime")
                .HasColumnName("UBDatum");
            entity.Property(e => e.Ubnr).HasColumnName("UBNr");
        });

        modelBuilder.Entity<Umbpo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UMBPos$ID");

            entity.ToTable("UMBPos");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bez1).HasMaxLength(30);
            entity.Property(e => e.Bez2).HasMaxLength(30);
            entity.Property(e => e.BzgGr).HasMaxLength(10);
            entity.Property(e => e.ChargenNr).HasMaxLength(20);
            entity.Property(e => e.Grund).HasMaxLength(50);
            entity.Property(e => e.Keng).HasMaxLength(4);
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.Pm)
                .HasMaxLength(1)
                .HasColumnName("PM");
            entity.Property(e => e.Preis).HasColumnType("money");
            entity.Property(e => e.ProdNr).HasMaxLength(30);
            entity.Property(e => e.Stueck)
                .HasDefaultValue(0m)
                .HasColumnType("money");
            entity.Property(e => e.Ubnr).HasColumnName("UBNr");
        });

        modelBuilder.Entity<UmschlAnz>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UmschlAnz$ID");

            entity.ToTable("UmschlAnz");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Datum).HasColumnType("datetime");
            entity.Property(e => e.Feuchte).HasColumnType("money");
            entity.Property(e => e.FreistNr).HasMaxLength(20);
            entity.Property(e => e.FremdLsnr)
                .HasMaxLength(20)
                .HasColumnName("FremdLSNr");
            entity.Property(e => e.Kfznr)
                .HasMaxLength(30)
                .HasColumnName("KFZNr");
            entity.Property(e => e.KundName).HasMaxLength(30);
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.PlzOrt).HasMaxLength(40);
        });

        modelBuilder.Entity<UmschlKopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UmschlKopf$ID");

            entity.ToTable("UmschlKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ArtikelBez).HasMaxLength(30);
            entity.Property(e => e.ArtikelBez1).HasMaxLength(30);
            entity.Property(e => e.Bezeichnung).HasMaxLength(30);
            entity.Property(e => e.ChargNr).HasMaxLength(30);
            entity.Property(e => e.KundenName1).HasMaxLength(30);
            entity.Property(e => e.KundenName2).HasMaxLength(30);
            entity.Property(e => e.KundenOrt).HasMaxLength(30);
            entity.Property(e => e.KundenPlz).HasMaxLength(10);
            entity.Property(e => e.KundenStrasse).HasMaxLength(30);
            entity.Property(e => e.SaldoAlt).HasColumnType("money");
            entity.Property(e => e.SaldoNeu).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Vertreter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Vertreter$ID");

            entity.ToTable("Vertreter");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.VstNr)
                .HasDefaultValue((short)0)
                .HasColumnName("VStNr");
            entity.Property(e => e.Vtanrede)
                .HasMaxLength(30)
                .HasColumnName("VTAnrede");
            entity.Property(e => e.Vtbank)
                .HasMaxLength(60)
                .HasColumnName("VTBank");
            entity.Property(e => e.Vtbemerk).HasColumnName("VTBemerk");
            entity.Property(e => e.Vtberuf)
                .HasMaxLength(30)
                .HasColumnName("VTBeruf");
            entity.Property(e => e.VtbldKn)
                .HasDefaultValue((short)0)
                .HasColumnName("VTBldKn");
            entity.Property(e => e.Vtblz)
                .HasMaxLength(10)
                .HasColumnName("VTBLZ");
            entity.Property(e => e.Vtbundland)
                .HasMaxLength(21)
                .HasColumnName("VTBundland");
            entity.Property(e => e.Vtemail)
                .HasMaxLength(30)
                .HasColumnName("VTEmail");
            entity.Property(e => e.Vtfax)
                .HasMaxLength(30)
                .HasColumnName("VTFax");
            entity.Property(e => e.VtfilialNr)
                .HasDefaultValue((short)0)
                .HasColumnName("VTFilialNr");
            entity.Property(e => e.Vtiban)
                .HasMaxLength(255)
                .HasColumnName("VTIBAN");
            entity.Property(e => e.Vtkonto)
                .HasMaxLength(20)
                .HasColumnName("VTKonto");
            entity.Property(e => e.Vtland)
                .HasMaxLength(30)
                .HasColumnName("VTLand");
            entity.Property(e => e.Vtmobil)
                .HasMaxLength(30)
                .HasColumnName("VTMobil");
            entity.Property(e => e.VtmwSt)
                .HasColumnType("money")
                .HasColumnName("VTMwSt");
            entity.Property(e => e.Vtname1)
                .HasMaxLength(30)
                .HasColumnName("VTName1");
            entity.Property(e => e.Vtname2)
                .HasMaxLength(30)
                .HasColumnName("VTName2");
            entity.Property(e => e.Vtname3)
                .HasMaxLength(30)
                .HasColumnName("VTName3");
            entity.Property(e => e.Vtnummer).HasColumnName("VTNummer");
            entity.Property(e => e.Vtort)
                .HasMaxLength(30)
                .HasColumnName("VTOrt");
            entity.Property(e => e.Vtplz)
                .HasMaxLength(10)
                .HasColumnName("VTPlz");
            entity.Property(e => e.Vtsbg)
                .HasMaxLength(30)
                .HasColumnName("VTSBG");
            entity.Property(e => e.Vtsperr).HasColumnName("VTSperr");
            entity.Property(e => e.VtstSchlüssel).HasColumnName("VTStSchlüssel");
            entity.Property(e => e.Vtstrasse)
                .HasMaxLength(30)
                .HasColumnName("VTStrasse");
            entity.Property(e => e.Vtswift)
                .HasMaxLength(255)
                .HasColumnName("VTSwift");
            entity.Property(e => e.Vttelefon1)
                .HasMaxLength(30)
                .HasColumnName("VTTelefon1");
            entity.Property(e => e.Vttelefon2)
                .HasMaxLength(30)
                .HasColumnName("VTTelefon2");
        });

        modelBuilder.Entity<Vstaffel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("VStaffel$ID");

            entity.ToTable("VStaffel");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Einheitgesackt).HasColumnType("money");
            entity.Property(e => e.Einheitlose).HasColumnType("money");
            entity.Property(e => e.Einheitproz).HasMaxLength(20);
            entity.Property(e => e.SgridPos).HasColumnName("SGridPos");
            entity.Property(e => e.Stnr).HasColumnName("STNr");
            entity.Property(e => e.Stvtnr).HasColumnName("STVTNr");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
        });

        modelBuilder.Entity<Wg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("WGS$ID");

            entity.ToTable("WGS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.EigenWare).HasDefaultValue(false);
            entity.Property(e => e.GegBlgNr).HasMaxLength(15);
            entity.Property(e => e.LockLast).HasColumnType("datetime");
            entity.Property(e => e.LockStart).HasColumnType("datetime");
            entity.Property(e => e.LockUser).HasMaxLength(50);
            entity.Property(e => e.LoeschKng).HasDefaultValue(false);
            entity.Property(e => e.Nuts2)
                .HasMaxLength(5)
                .HasColumnName("NUTS2");
            entity.Property(e => e.Nuts2code)
                .HasMaxLength(5)
                .HasColumnName("NUTS2Code");
            entity.Property(e => e.Nvo)
                .HasDefaultValue(false)
                .HasColumnName("NVO");
            entity.Property(e => e.NvobilanzNr)
                .HasMaxLength(30)
                .HasColumnName("NVOBilanzNr");
            entity.Property(e => e.Nvobilanziert)
                .HasDefaultValue(false)
                .HasColumnName("NVOBilanziert");
            entity.Property(e => e.Nvonr)
                .HasMaxLength(30)
                .HasColumnName("NVONr");
            entity.Property(e => e.PktrNr)
                .HasDefaultValue(0L)
                .HasColumnName("PKtrNr");
            entity.Property(e => e.Qmg)
                .HasDefaultValue(false)
                .HasColumnName("QMG");
            entity.Property(e => e.Reinigung).HasDefaultValue(false);
            entity.Property(e => e.SammelStorno).HasDefaultValue(false);
            entity.Property(e => e.Sammelschein).HasDefaultValue(false);
            entity.Property(e => e.Send).HasDefaultValue(false);
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Thgges)
                .HasColumnType("money")
                .HasColumnName("THGGes");
            entity.Property(e => e.Thgsumm)
                .HasDefaultValue(0m)
                .HasColumnType("money")
                .HasColumnName("THGSumm");
            entity.Property(e => e.Thgwert)
                .HasColumnType("money")
                .HasColumnName("THGWert");
            entity.Property(e => e.Verwieger).HasMaxLength(15);
            entity.Property(e => e.WgsabrNr).HasColumnName("WGSAbrNr");
            entity.Property(e => e.WgsanlGewicht)
                .HasColumnType("money")
                .HasColumnName("WGSAnlGewicht");
            entity.Property(e => e.WgsanlName2)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlName2");
            entity.Property(e => e.WgsanlName3)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlName3");
            entity.Property(e => e.WgsanlNr).HasColumnName("WGSAnlNr");
            entity.Property(e => e.WgsanlOrt)
                .HasMaxLength(30)
                .HasColumnName("WGSAnlOrt");
            entity.Property(e => e.WgsanlPlz)
                .HasMaxLength(6)
                .HasColumnName("WGSAnlPLZ");
            entity.Property(e => e.WgsartNr).HasColumnName("WGSArtNr");
            entity.Property(e => e.Wgsasld)
                .HasMaxLength(2)
                .HasColumnName("WGSAsld");
            entity.Property(e => e.WgsbemLf).HasColumnName("WGSBemLF");
            entity.Property(e => e.WgsbemZus).HasColumnName("WGSBemZus");
            entity.Property(e => e.Wgsbemerkung).HasColumnName("WGSBemerkung");
            entity.Property(e => e.WgschargNr)
                .HasMaxLength(20)
                .HasColumnName("WGSChargNr");
            entity.Property(e => e.Wgsdatum)
                .HasColumnType("datetime")
                .HasColumnName("WGSDatum");
            entity.Property(e => e.Wgselnr)
                .HasMaxLength(4)
                .HasColumnName("WGSELNR");
            entity.Property(e => e.Wgsempfänger)
                .HasMaxLength(20)
                .HasColumnName("WGSEmpfänger");
            entity.Property(e => e.Wgsfeuchte)
                .HasColumnType("money")
                .HasColumnName("WGSFeuchte");
            entity.Property(e => e.Wgsga)
                .HasMaxLength(1)
                .HasColumnName("WGSGA");
            entity.Property(e => e.WgsgetrArt)
                .HasMaxLength(18)
                .HasColumnName("WGSGetrArt");
            entity.Property(e => e.Wgsgkennung)
                .HasMaxLength(3)
                .HasColumnName("WGSGKennung");
            entity.Property(e => e.WgskontraktNr).HasColumnName("WGSKontraktNr");
            entity.Property(e => e.WgsktNrHdP)
                .HasMaxLength(20)
                .HasColumnName("WGSKtNrHdP");
            entity.Property(e => e.WgslagerortSkp)
                .HasMaxLength(10)
                .HasColumnName("WGSLagerortSKP");
            entity.Property(e => e.Wgsland)
                .HasMaxLength(30)
                .HasColumnName("WGSLand");
            entity.Property(e => e.Wgslfnr).HasColumnName("WGSLFNr");
            entity.Property(e => e.Wgslfstatus)
                .HasMaxLength(10)
                .HasColumnName("WGSLFStatus");
            entity.Property(e => e.Wgslkw2)
                .HasMaxLength(15)
                .HasColumnName("WGSLKW2");
            entity.Property(e => e.Wgslkwkennz)
                .HasMaxLength(10)
                .HasColumnName("WGSLKWKennz");
            entity.Property(e => e.WgslsNr)
                .HasMaxLength(20)
                .HasColumnName("WGSLsNr");
            entity.Property(e => e.WgsmaklerNr)
                .HasMaxLength(10)
                .HasColumnName("WGSMaklerNr");
            entity.Property(e => e.Wgsmenge)
                .HasColumnType("money")
                .HasColumnName("WGSMenge");
            entity.Property(e => e.Wgsname1)
                .HasMaxLength(30)
                .HasColumnName("WGSName1");
            entity.Property(e => e.Wgsname2)
                .HasMaxLength(30)
                .HasColumnName("WGSName2");
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
            entity.Property(e => e.Wgsort)
                .HasMaxLength(30)
                .HasColumnName("WGSOrt");
            entity.Property(e => e.WgspartieBlenr)
                .HasMaxLength(20)
                .HasColumnName("WGSPartieBLENr");
            entity.Property(e => e.Wgsplz)
                .HasMaxLength(10)
                .HasColumnName("WGSPLZ");
            entity.Property(e => e.Wgspreis)
                .HasColumnType("money")
                .HasColumnName("WGSPreis");
            entity.Property(e => e.Wgsqc)
                .HasDefaultValue(false)
                .HasColumnName("WGSQC");
            entity.Property(e => e.Wgsrmnr).HasColumnName("WGSRMNr");
            entity.Property(e => e.WgssammelNr).HasColumnName("WGSSammelNr");
            entity.Property(e => e.Wgssbg)
                .HasMaxLength(10)
                .HasColumnName("WGSSBG");
            entity.Property(e => e.Wgsselektion)
                .HasMaxLength(1)
                .HasColumnName("WGSSelektion");
            entity.Property(e => e.Wgssorte)
                .HasMaxLength(20)
                .HasColumnName("WGSSorte");
            entity.Property(e => e.WgsspedFax)
                .HasMaxLength(20)
                .HasColumnName("WGSSpedFax");
            entity.Property(e => e.WgsspedName1)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedName1");
            entity.Property(e => e.WgsspedName2)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedName2");
            entity.Property(e => e.WgsspedNr).HasColumnName("WGSSpedNr");
            entity.Property(e => e.WgsspedOrt)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedOrt");
            entity.Property(e => e.WgsspedPlz)
                .HasMaxLength(10)
                .HasColumnName("WGSSpedPLZ");
            entity.Property(e => e.WgsspedSbg)
                .HasMaxLength(10)
                .HasColumnName("WGSSpedSBG");
            entity.Property(e => e.WgsspedStrasse)
                .HasMaxLength(30)
                .HasColumnName("WGSSpedStrasse");
            entity.Property(e => e.WgsspedTelefon)
                .HasMaxLength(20)
                .HasColumnName("WGSSpedTelefon");
            entity.Property(e => e.Wgssperr)
                .HasDefaultValue(false)
                .HasColumnName("WGSSperr");
            entity.Property(e => e.Wgsstrasse)
                .HasMaxLength(30)
                .HasColumnName("WGSStrasse");
            entity.Property(e => e.WgsstreckenNr).HasColumnName("WGSStreckenNr");
            entity.Property(e => e.Wgstelefon)
                .HasMaxLength(20)
                .HasColumnName("WGSTelefon");
            entity.Property(e => e.WgsvonGbvgesp)
                .HasMaxLength(1)
                .HasColumnName("WGSVonGBVgesp");
            entity.Property(e => e.Wgsvorfracht1)
                .HasMaxLength(50)
                .HasColumnName("WGSVorfracht1");
            entity.Property(e => e.Wgsvorfracht2)
                .HasMaxLength(50)
                .HasColumnName("WGSVorfracht2");
            entity.Property(e => e.Wgsvorfracht3)
                .HasMaxLength(50)
                .HasColumnName("WGSVorfracht3");
            entity.Property(e => e.Wgsvz)
                .HasColumnType("money")
                .HasColumnName("WGSVZ");
            entity.Property(e => e.Wgsvzdatum)
                .HasColumnType("datetime")
                .HasColumnName("WGSVZDatum");
            entity.Property(e => e.Wgsvznr).HasColumnName("WGSVZNr");
            entity.Property(e => e.WgswgNr)
                .HasMaxLength(15)
                .HasColumnName("WGSWgNr");
            entity.Property(e => e.WgszellenNr).HasColumnName("WGSZellenNr");
            entity.Property(e => e.WiegeGew).HasColumnType("money");
            entity.Property(e => e.Wiegezeit).HasColumnType("datetime");
        });

        modelBuilder.Entity<WgschargTbl>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("WGSChargTbl$ID");

            entity.ToTable("WGSChargTbl");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Charge).HasMaxLength(15);
            entity.Property(e => e.Keng).HasMaxLength(15);
            entity.Property(e => e.Menge).HasColumnType("money");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
        });

        modelBuilder.Entity<Wgslab>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("WGSLAB$ID");

            entity.ToTable("WGSLAB");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.WgslabBez1)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez1");
            entity.Property(e => e.WgslabBez10)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez10");
            entity.Property(e => e.WgslabBez11)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez11");
            entity.Property(e => e.WgslabBez12)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez12");
            entity.Property(e => e.WgslabBez2)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez2");
            entity.Property(e => e.WgslabBez3)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez3");
            entity.Property(e => e.WgslabBez4)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez4");
            entity.Property(e => e.WgslabBez5)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez5");
            entity.Property(e => e.WgslabBez6)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez6");
            entity.Property(e => e.WgslabBez7)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez7");
            entity.Property(e => e.WgslabBez8)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez8");
            entity.Property(e => e.WgslabBez9)
                .HasMaxLength(20)
                .HasColumnName("WGSLabBez9");
            entity.Property(e => e.WgslabEh1)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh1");
            entity.Property(e => e.WgslabEh10)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh10");
            entity.Property(e => e.WgslabEh11)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh11");
            entity.Property(e => e.WgslabEh12)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh12");
            entity.Property(e => e.WgslabEh2)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh2");
            entity.Property(e => e.WgslabEh3)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh3");
            entity.Property(e => e.WgslabEh4)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh4");
            entity.Property(e => e.WgslabEh5)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh5");
            entity.Property(e => e.WgslabEh6)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh6");
            entity.Property(e => e.WgslabEh7)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh7");
            entity.Property(e => e.WgslabEh8)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh8");
            entity.Property(e => e.WgslabEh9)
                .HasMaxLength(6)
                .HasColumnName("WGSLabEh9");
            entity.Property(e => e.WgslabWert1)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert1");
            entity.Property(e => e.WgslabWert10)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert10");
            entity.Property(e => e.WgslabWert11)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert11");
            entity.Property(e => e.WgslabWert12)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert12");
            entity.Property(e => e.WgslabWert2)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert2");
            entity.Property(e => e.WgslabWert3)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert3");
            entity.Property(e => e.WgslabWert4)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert4");
            entity.Property(e => e.WgslabWert5)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert5");
            entity.Property(e => e.WgslabWert6)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert6");
            entity.Property(e => e.WgslabWert7)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert7");
            entity.Property(e => e.WgslabWert8)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert8");
            entity.Property(e => e.WgslabWert9)
                .HasMaxLength(10)
                .HasColumnName("WGSLabWert9");
            entity.Property(e => e.Wgsnr).HasColumnName("WGSNr");
            entity.Property(e => e.ZusTxtBez1).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez2).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez3).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez4).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez5).HasMaxLength(30);
            entity.Property(e => e.ZusTxtBez6).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh1).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh2).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh3).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh4).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh5).HasMaxLength(30);
            entity.Property(e => e.ZusTxtInh6).HasMaxLength(30);
        });

        modelBuilder.Entity<Wrkopf>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("WRKopf$ID");

            entity.ToTable("WRKopf");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.WrartBez1)
                .HasMaxLength(30)
                .HasColumnName("WRArtBez1");
            entity.Property(e => e.WrartBez2)
                .HasMaxLength(30)
                .HasColumnName("WRArtBez2");
            entity.Property(e => e.WrartNr).HasColumnName("WRArtNr");
            entity.Property(e => e.Wrdatum)
                .HasColumnType("datetime")
                .HasColumnName("WRDatum");
        });

        modelBuilder.Entity<Wrpo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("WRPos$ID");

            entity.ToTable("WRPos");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SysTime).HasColumnType("datetime");
            entity.Property(e => e.SysUser).HasMaxLength(50);
            entity.Property(e => e.Wr)
                .HasMaxLength(2)
                .HasColumnName("WR??");
            entity.Property(e => e.WrartName)
                .HasMaxLength(30)
                .HasColumnName("WRArtName");
            entity.Property(e => e.WrartNr).HasColumnName("WRArtNr");
            entity.Property(e => e.Wrbasis)
                .HasMaxLength(6)
                .HasColumnName("WRBasis");
            entity.Property(e => e.Wrmax)
                .HasMaxLength(6)
                .HasColumnName("WRMax");
            entity.Property(e => e.Wrmin)
                .HasMaxLength(6)
                .HasColumnName("WRMin");
            entity.Property(e => e.WrposNr).HasColumnName("WRPosNr");
            entity.Property(e => e.Wrps)
                .HasMaxLength(2)
                .HasColumnName("WRPS");
            entity.Property(e => e.Wrstossgr)
                .HasMaxLength(6)
                .HasColumnName("WRStossgr");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    
    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder
            .Properties<PostingsSchema>()
            .HaveConversion<PostingsSchemaConverter>();
    }
}

public class PostingsSchemaConverter : ValueConverter<PostingsSchema, string>
{
    public PostingsSchemaConverter() : base(s => s.Value, c => new PostingsSchema(c)) { }
}
