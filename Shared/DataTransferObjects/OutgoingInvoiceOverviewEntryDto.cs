using Entities.Models;

namespace Shared.DataTransferObjects;

public record OutgoingInvoiceOverviewEntryDto(
    long Id,
    long InvoiceNumber,
    DateTime? InvoiceDate,
    decimal TotalGross,
    decimal TotalNet,
    decimal TotalVatAmount,
    bool IsCancellationInvoice,
    bool WasCanceled,
    InvoiceHolderDto InvoiceHolder,
    IEnumerable<TaxDto> ListTax)
{
    public static implicit operator OutgoingInvoiceOverviewEntryDto(Aradressdaten aadressdaten)
    {
        return new OutgoingInvoiceOverviewEntryDto
        (
            aadressdaten.Id,
            aadressdaten.Arnummer ?? 0,
            aadressdaten.Ardatum,
            aadressdaten.ArgesBrutto ?? 0,
            aadressdaten.ArgesNetto ?? 0,
            aadressdaten.ArgesMwst ?? 0,
            aadressdaten.IsCancellationInvoice,
            aadressdaten.WasCanceled,
            new InvoiceHolderDto(
                aadressdaten.ArkdnrRe,
                aadressdaten.Arname1Re,
                string.Join(' ', aadressdaten.ArstrasseRe, aadressdaten.Arplzre, aadressdaten.ArortRe),
                aadressdaten.ArlandRe ?? string.Empty,
                aadressdaten.IsInvoiceHolderForeigner),
            [
                new TaxDto(0, aadressdaten.Armwst1 ?? 0, aadressdaten.ArmwSt1Proz ?? 0),
                new TaxDto(0, aadressdaten.Armwst2 ?? 0, aadressdaten.ArmwSt2Proz ?? 0),
                new TaxDto(0, aadressdaten.Armwst3 ?? 0, aadressdaten.ArmwSt3Proz ?? 0)
            ]
        );
    }
}

public record InvoiceHolderDto(
    long? Number,
    string? Name,
    string FullAddress,
    string Country,
    bool IsForeigner);

public record TaxDto(
    short VatId,
    decimal VatAmount,
    decimal Vat);
