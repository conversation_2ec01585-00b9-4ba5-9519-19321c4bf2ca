using Entities.Common;
using Entities.Models;

namespace Shared.DataTransferObjects;

public record InvoicePostingDto(
    long Id,
    string Description,
    long Number,
    string Status,
    string Schema,
    decimal TotalGross,
    decimal TotalNet,
    string FibuAccount,
    TaxDto Tax)
{
    public static implicit operator InvoicePostingDto(Arhauptdatei arhauptdatei)
    {
        return new InvoicePostingDto
        (
            arhauptdatei.Id,
            arhauptdatei.Bezeichnung ?? string.Empty,
            arhauptdatei.Lsnr ?? 0,
            arhauptdatei.Status ?? string.Empty,
            arhauptdatei.ErfSchema?.Value ?? string.Empty,
            arhauptdatei.Zn ?? 0,
            arhauptdatei.Zn ?? 0,
            arhauptdatei.Konto ?? string.Empty,
            new TaxDto(arhauptdatei.MwstSchl ?? 0, arhauptdatei.MwstProz ?? 0, arhauptdatei.MwstProz ?? 0)
        );
    }
}
