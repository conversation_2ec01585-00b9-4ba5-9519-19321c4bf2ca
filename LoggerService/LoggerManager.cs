using Contracts;
using Serilog;

// ReSharper disable TemplateIsNotCompileTimeConstantProblem

namespace LoggingService;

// ILoggerManager implementation for Serilog 
public class LoggerManager : ILoggerManager
{
    private static readonly ILogger Logger = Log.Logger;

    public void LogVerbose(string message) => Logger.Verbose(message);
    public void LogVerbose(Exception ex, string message) => Logger.Verbose(ex, message);
    public void LogVerbose(string message, params object[] parameters) => Logger.Verbose(message, parameters);

    public void LogVerbose(Exception ex, string message, params object[] parameters) =>
        Logger.Verbose(ex, message, parameters);

    public void LogDebug(string message) => Logger.Debug(message);
    public void LogDebug(Exception ex, string message) => Logger.Debug(ex, message);

    public void LogDebug(string message, params object[] parameters) => Logger.Debug(message, parameters);

    public void LogDebug(Exception ex, string message, params object[] parameters) =>
        Logger.Debug(ex, message, parameters);

    public void LogInfo(string message) => Logger.Information(message);
    public void LogInfo(Exception ex, string message) => Logger.Information(ex, message);

    public void LogInfo(string message, params object[] parameters) => Logger.Information(message, parameters);

    public void LogInfo(Exception ex, string message, params object[] parameters) =>
        Logger.Information(ex, message, parameters);

    public void LogWarn(string message) => Logger.Warning(message);
    public void LogWarn(Exception ex, string message) => Logger.Warning(ex, message);

    public void LogWarn(string message, params object[] parameters) => Logger.Warning(message, parameters);

    public void LogWarn(Exception ex, string message, params object[] parameters) =>
        Logger.Warning(ex, message, parameters);

    public void LogError(string message) => Logger.Error(message);
    public void LogError(Exception ex, string message) => Logger.Error(ex, message);

    public void LogError(string message, params object[] parameters) => Logger.Error(message, parameters);

    public void LogError(Exception ex, string message, params object[] parameters) =>
        Logger.Error(ex, message, parameters);

    public void LogFatal(string message) => Logger.Fatal(message);
    public void LogFatal(Exception ex, string message) => Logger.Fatal(ex, message);

    public void LogFatal(string message, params object[] parameters) => Logger.Fatal(message, parameters);

    public void LogFatal(Exception ex, string message, params object[] parameters) =>
        Logger.Fatal(ex, message, parameters);
}