<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Quartz" Version="3.9.0" />
      <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
      <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
      <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Contracts\Contracts.csproj" />
    </ItemGroup>

</Project>
