using System.ComponentModel;
using Entities.Common;
using Microsoft.AspNetCore.Mvc;
using Service.Contracts;

namespace WinGng.Presentation.Controllers;

// Controller for accessing anything regarding Aradressdaten, Arhauptdatei
[Route("api/outgoinginvoices/postings")]
[ApiController]
public class ArhauptdateiController : ControllerBase
{
    private readonly IServiceManager _service;

    public ArhauptdateiController(IServiceManager service) => _service = service;
    
    [HttpGet("{invoiceNumber}")]
    public IActionResult GetAllPostingsFromOutgoingInvoices(long invoiceNumber)
    {
        var overview = _service.ArhauptdateiService.GetAllPostingsFromInvoice(
                false,
                invoiceNumber
                );
       return Ok(overview);
    }
    
    [HttpGet("{invoiceNumber}/{schema}")]
    public IActionResult GetAllPoPostingsFromOutgoingInvoices(long invoiceNumber, string schema)
    {
        var overview = _service.ArhauptdateiService.GetAllPostingsFromInvoice(
                false,
                invoiceNumber,
                schema.ToUpper() switch
                    {
                        PostingsSchema.PoString => PostingsSchema.Po,
                        PostingsSchema.TeString => PostingsSchema.Te,
                        _ => null
                    }
                );
       return Ok(overview);
    }
}