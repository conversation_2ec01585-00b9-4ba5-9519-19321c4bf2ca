using Microsoft.AspNetCore.Mvc;
using Service.Contracts;

namespace WinGng.Presentation.Controllers;

// Controller for accessing anything regarding Aradressdaten, Arhauptdatei
[Route("api/outgoinginvoices")]
[ApiController]
public class AradressdatenController : ControllerBase
{
    private readonly IServiceManager _service;

    public AradressdatenController(IServiceManager service) => _service = service;

    // Get
    [HttpGet]
    public IActionResult GetOutgoingInvoices()
    {
        var aradressdaten = _service.AradressdatenService.GetAllAradressdaten(false);
        return Ok(aradressdaten);
    }

    [HttpGet("overview")]
    public IActionResult GetOutgoingInvoicesOverview([FromQuery]DateTime? fromDate, [FromQuery]DateTime? toDate)
    {
        var overview = _service.AradressdatenService.GetAllAradressdatenOverview(
                false,
                fromDate,
                toDate
                );
        
       return Ok(overview);
    }
}